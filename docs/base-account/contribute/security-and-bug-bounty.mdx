---
title: "Security and Bug Bounty"
---


## Security Audits

Base Account has undergone multiple security audits. You can find the full list of our audits in the main Base Account repository.

[Base Account audits](https://github.com/coinbase/smart-wallet/tree/main/audits)

## Bug Bounty Program

The Coinbase/Base Bug Bounty program is a crowdsourced initiative that rewards security researchers
for responsibly reporting vulnerabilities in Base's smart contracts and infrastructure.

To report a bug, please follow the instructions in
the [HackerOne Bug Bounty Program](https://hackerone.com/coinbase?type=team).
