---
title: Basenames + OnchainKit Tutorial
slug: /basenames/basenames-onchainkit-tutorial
description: 'A tutorial that teaches how to integrate Basenames to your wagmi/viem App using OnchainKit'
author: hughescoin
---

# Add Basenames to your wagmi/viem App using OnchainKit

Basenames is now live! But what exactly is it? Basenames allows users to register human-readable names for their addresses and serves as a foundational building block for onchain identity. Think of it as your favorite social media handle, but even bigger. Your Basename is multichain by default and yours forever—no platform can take it away from you (just make sure to pay your fee).

Integrating Basenames into your onchain app enhances the user experience by masking complex wallet addresses. Just as domains simplify IP addresses, Basenames do the same for wallet addresses.

OnchainKit is a React component library designed to make building Onchain applications easier. In this tutorial, we'll use the `<Identity/>` component to resolve Basenames.

This demo uses Coinbase Base Account and Coinbase Wallet, but Basenames is supported across many [other wallets].

## Objectives

By the end of this tutorial, you should be able to:

- Understand how onchain identity works on the Base network
- Enable users to use their onchain identity in your app using [OnchainKit]

---

If you're starting from scratch, you'll need to create a new wagmi project. If you already have an existing wagmi project, you can skip ahead to the section on installing OnchainKit.

To create a new wagmi project using TypeScript and install the required dependencies, run the following command:

```bash
bun create wagmi
```

Next, you'll need to install OnchainKit. Run the following command:

```bash
bun add @coinbase/onchainkit
```

After adding OnchainKit, install all dependencies and start your development server with:

```
bun install && bun run dev
```

This command will install the necessary dependencies and start a development server.

To follow along with the tutorial effectively, open your web browser and your IDE side by side. This setup will allow you to code and see the changes in real time.

### Update Wagmi config

In this section, we will configure your wagmi project to support the Base blockchain by importing the necessary modules.

Start by importing the `base` and `baseSepolia` chains into your wagmi config. Navigate to `src/wagmi.ts` and update the file as follows:

```typescript wagmi.ts
import { http, cookieStorage, createConfig, createStorage } from 'wagmi';
import { base, baseSepolia } from 'wagmi/chains';
import { coinbaseWallet, injected } from 'wagmi/connectors';

export function getConfig() {
  return createConfig({
    chains: [base, baseSepolia],
    connectors: [
      injected(),
      coinbaseWallet({
        appName: 'Create Wagmi',
        preference: 'smartWalletOnly',
      }),
    ],
    storage: createStorage({
      storage: cookieStorage,
    }),
    ssr: true,
    transports: {
      [base.id]: http(),
      [baseSepolia.id]: http(),
    },
  });
}

declare module 'wagmi' {
  interface Register {
    config: ReturnType<typeof getConfig>;
  }
}
```

This configuration sets up the wagmi project to connect to the Base and BaseSepolia networks, utilizing Coinbase Wallet and other connectors.

