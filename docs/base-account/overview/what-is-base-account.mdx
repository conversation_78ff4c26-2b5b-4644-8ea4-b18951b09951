---
title: "Base Account Overview"
description: "What is a Base Account and how the Base Account SDK lets you add universal sign-in and one-tap USDC payments to any app."
---

> **TL;DR** – Base Accounts are the onchain identity and account layer powering the [Base App](https://base.app). With the **Base Account SDK** you can connect to over one hundred thousand users and unlock authentication and payments with just a few lines of code.

## What is a Base Account?

A Base Account is a Smart-Wallet–backed account that gives every user:

* **Universal sign-on** – one passkey works across every Base-enabled app.
* **One-tap payments** – low-friction USDC payments built into the account layer.
* **Private profile vault** – opt-in sharing of email, phone, shipping address, and more.
* **Multi-chain support** – one address that works across nine EVM networks (and counting).

> Under the hood, each Base Account is an ERC-4337 Smart Wallet that can be deployed on any EVM-compatible chain; nine EVM mainnet chains are enabled out of the box, including Base Mainnet.

<Note>
**Supported networks**

- **Mainnet:** Base • Arbitrum • Optimism • Zora • Polygon • BNB • Avalanche • Lordchain • Ethereum Mainnet (not recommended due to costs)
- **Testnet:** Sepolia • Base Sepolia
</Note>

## Why should developers care?

* **Higher conversion** – no app installs, seed phrases, or network switches.
* **Fewer drop-offs at checkout** – a single `pay()` call handles gas and settlement.
* **Cross-app identity** – fetch a verified email or shipping address via the same SDK.
* **Self-custodial** – users hold the keys; you never touch private data or funds.

## Next steps

| Goal | Start here |
| --- | --- |
| Build & run in 5 min | [Quickstart](/base-account/quickstart/web) |
| Authentication flow | [Authenticate users](/base-account/guides/authenticate-users) |
| Accept USDC payments on Base | [Accept payments](/base-account/guides/accept-payments) |
| Deep-dive API shapes | [TypeScript API reference](/base-account/reference/core/getProvider) |
