---
title: "Base Gasless Campaign"
---


Base is offering gas credits to help developers make the most of
Base Account's [paymaster (sponsored transactions)](/smart-wallet/concepts/features/optional/gas-free-transactions) features.

| Partner Tier | Base Gas Credit Incentive | Requirements                                                                                                                                                   | Actions                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| ------------ | ------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1            | $15k                      | <ul><li>- Support Coinbase Base Account</li><li>- Onboard to Coinbase Paymaster</li><li>- Preferred placement in your UI (ie "Create Wallet" button)</li></ul> | <ol><li>1. Bump your Coinbase SDK to add Coinbase Base Account to your app, or bump to latest version of any [supporting wallet library](/smart-wallet/concepts/usage-details/wallet-library-support).</li><li>2. Sign in / up for [Coinbase Developer Platform](https://www.coinbase.com/developer-platform) (takes less than 2 minutes). No KYC needed - just email and phone.</li><li>3. Check out the Paymaster product where the Base Mainnet Paymaster is enabled by default. Set and change your gas policy at any time.</li><li>4. Complete [this form](https://docs.google.com/forms/d/1yPnBFW0bVUNLUN_w3ctCqYM9sjdIQO3Typ53KXlsS5g/viewform?edit_requested=true)</li><li>Credits will land within 1 week of completion</li></ol> |
| 2            | $10k                      | <ul><li>- Support Coinbase Base Account</li><li>- Onboard to Coinbase Paymaster</li></ul>                                                                      | <ol><li>1. Bump your Coinbase SDK to add Coinbase Base Account to your app, or bump to latest version of any [supporting wallet library](/smart-wallet/concepts/usage-details/wallet-library-support).</li><li>2. Sign in / up for [Coinbase Developer Platform](https://www.coinbase.com/developer-platform) (takes less than 2 minutes). No KYC needed - just email and phone.</li><li>3. Check out the Paymaster product where the Base Mainnet Paymaster is enabled by default. Set and change your gas policy at any time.</li><li>4. Complete [this form](https://docs.google.com/forms/d/1yPnBFW0bVUNLUN_w3ctCqYM9sjdIQO3Typ53KXlsS5g/viewform?edit_requested=true)</li><li>Credits will land within 1 week of completion</li></ol> |
| Bonus        | $1k                       | <ul><li>- Release demo</li></ul>                                                                                                                               | Create a demo of your Coinbase Base Account integration, post on social (Farcaster and/or X) and tag Coinbase Wallet and/or Base                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |

