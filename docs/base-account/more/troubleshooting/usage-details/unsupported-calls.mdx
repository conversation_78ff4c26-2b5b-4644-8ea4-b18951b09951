---
title: 'Unsupported Calls'
---

Base Account implements an Ethereum Provider JavaScript API (as detailed in [EIP-1193](https://eips.ethereum.org/EIPS/eip-1193))
and follows the [ERC-4337](https://eips.ethereum.org/EIPS/eip-4337) standard for account abstraction.

This means that apps interacting with Base Account can expect it to behave like a regular Ethereum Virtual Machine (EVM) account.
However, there are some calls that are not supported by Base Accounts for incompatibility or security reasons.
This is a list of calls that are not supported:

- [Self Calls](#self-calls): This refers to the ability of an app to use the user's account to call itself.
- [CREATE](#create): This refers to the ability of an app to create a new contract using the OPCODE `CREATE`.

## Self Calls

For security reasons, we do not allow 3rd party apps to make calls to a user's own Base Account address.
This could allow apps to change owners, upgrade the user's account, or cause other issues.

## CREATE

This is a limitation of the ERC-4337 standard and smart accounts.
Currently, we do not support the `CREATE` opcode for smart contracts deployment.
Future versions of Base Account may support it.

<Tip>
You can use a factory contract or a transaction with the `CREATE2` opcode to deploy a smart contract.
</Tip>

## Solidity's Builtin `transfer` function

The `transfer` function is a built-in member of the `address` type in Solidity that can be used to send ETH to an address. Base Account wallets cannot receive ETH using this function.
This function has long been considered deprecated in favor of `call` by the Solidity community, but some older contracts still use it.

The reason for this is that `transfer` only forwards 2300 gas to the `transfer` call, a protective mechanism that was designed to prevent reentrancy attacks by limiting the amount of
gas available to a smart contract that might reenter the caller.
In the modern world of smart contract wallets (including for Base Account), this is often not enough gas for the smart contract's `receive` or `fallback` functions to complete their work,
causing the transaction to revert.

### Known affected contracts

- The [WETH9 contract](https://basescan.org/token/******************************************) uses `transfer` to send ETH to the user's wallet and therefore Base Accounts cannot directly unwrap ETH from it.

