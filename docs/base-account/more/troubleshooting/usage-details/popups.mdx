---
title: "Popup Tips"
---


## Overview
When a Base Account is connected and Coinbase Wallet SDK receives a request, it opens 
[keys.coinbase.com](https://keys.coinbase.com/) in a popup window and passes the request to the popup for handling. 
Keep the following points in mind when working with the Base Account popup.

## Default blocking behavior
- Most modern browsers block all popups by default, unless they are triggered by a click. 
- If a popup is blocked the browser shows a notification to the user, allowing them to manage popup settings.

### What to do about it
- Ensure there is no additional logic between the button click and the request to open the Base Account popup, 
as browsers might perceive the request as programmatically initiated. 
- If logic is unavoidable, keep it minimal and test thoroughly in all supported browsers.

## `Cross-Origin-Opener-Policy` 
If the Base Account popup opens and displays an error or infinite spinner, it may be due to the dapp's `Cross-Origin-Opener-Policy`. Be sure to use a directive that allows the Base Account popup to function. 

- ✅ Allows Base Account popup to function
  - `unsafe-none` (default)
  - `same-origin-allow-popups` (recommended)
- ❌ Breaks Base Account popup
  - `same-origin`

For more detailed information refer to the [MDN docs](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Opener-Policy).


## Base Account popup 'linger' behavior
- Sometimes a dapp may programmatically make a followup request based on the response to a previous request. 
Normally, browsers block these programmatic requests to open popups. 
- To address this, after the Base Account popup responds to a request, it will linger for 200ms to listen for another incoming request before closing. 
  - If a request is received *during* this 200ms window, it will be received and handled within the same popup window. 
  - If a request is received *after* the 200ms window and the popup has closed, opening the Base Account popup will be blocked by the browser. 
