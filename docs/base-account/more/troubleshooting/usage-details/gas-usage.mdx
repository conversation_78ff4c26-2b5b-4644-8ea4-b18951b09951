---
title: "Gas Usage"
---


Base Accounts use more gas for transactions than traditional Ethereum accounts. On L2 networks, the cost difference to the user is a matter of cents. 
The gas difference is due to the additional overhead required for:

1. **ERC-4337 Bundling**
2. **Smart Contract Operations**, including one time deployment of the Base Account contract
3. **Signature Verification**

## Gas Usage Breakdown

Here's a rough comparison of gas usage per account:

| Operation Type | Traditional Ethereum Account | Base Account |
|---------------|------------|--------------|
| Native Token Transfer | ~21,000 gas | ~100,000 gas |
| ERC-20 Token Transfer | ~65,000 gas | ~150,000 gas |
| First Deployment | N/A | ~300,000+ gas |
