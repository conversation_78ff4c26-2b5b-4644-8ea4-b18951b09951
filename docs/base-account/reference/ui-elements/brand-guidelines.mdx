---
title: 'Brand Guidelines'
description: 'Design and brand guidelines for Sign in With Base and Base Pay buttons'
---

import {BasePayButton} from "/snippets/BasePayButton.mdx"
import {SignInWithBaseButton} from "/snippets/SignInWithBaseButton.mdx"


## Sign in With Base & Base Pay 

Base account offers two buttons to use in your application: 
- [**Sign in with Base**](/base-account/reference/ui-elements/sign-in-with-base-button): for user authentication for your product
- [**Base Pay**](/base-account/reference/ui-elements/base-pay-button): payments for online and offline goods

## Sign in with Base

Integrating "Sign in With Base" offers a convenient and trusted way for users to access your services. By leveraging their established Base account, users can avoid creating and remembering new credentials, leading to a smoother onboarding and login process.

<div style={{ display: 'flex', justifyContent: 'center', backgroundColor: '#000000', padding: '20px', borderRadius: '8px' }}>
  <SignInWithBaseButton />
</div>
<br />
<div style={{ display: 'flex', justifyContent: 'center', backgroundColor: '#ffffff', padding: '20px', borderRadius: '8px' }}>
  <SignInWithBaseButton colorScheme="dark" />
</div>


### Best Practices

To provide the best possible user experience when integrating "Sign in With Base," consider the following guidelines:

- **Offer Value for Sign-in**: Clearly communicate the benefits of signing in. Users should understand why they are being asked to sign in, such as to personalize their experience, access premium features, or synchronize data across devices.

- **Prominently Display the Button**: Make the "Sign in With Base" button easily discoverable. It should be no smaller than other sign-in options and should not require users to scroll to find it.

- **Consistent Placement**: Place the "Sign in With Base" button in a consistent and logical location on your sign-in and account creation screens.

### Design & Brand Guidelines

The "Sign in With Base" button should be easily recognizable and consistent across all platforms. Adhering to these design guidelines ensures a familiar and trusted experience for users.

#### Button Appearance

The "Sign in With Base" button has two key components:

1. **The Base logo is a blue square**
   - The square never changes shades of blue, it's always `#0000FF`
   - In dark mode, the square changes color to pure white `#FFFFFF`

2. **The "Sign in with Base" text**
   - Always use "Sign in with Base" unless there's an explicit "Sign in" heading prior
   - Use "Base Sans" where possible, otherwise [create a custom button](#creating-a-custom-button)

Following are some DOs and DON'Ts for the Base branding:

#### DO
- Leave at least 8pt of padding in-between the base square and "Sign in with Base", if creating a custom button
- Use base blue on a white/light background
- Use the all-white lockup if on a black/dark background
- Use "Sign in with Base" (including "Sign in") unless "Sign in" is present as a heading on the screen

#### DON'T
- Use gradients for the logo
- Change the corner radius of the logo
- Change the color of the Base Square
- Use Base Blue on a dark background

Base offers the following out of the box components:

<div style={{ display: 'flex', justifyContent: 'center', margin: '2rem 0' }}>
  <img src="/images/base-account/SIWB-Dark-Mode.jpg" alt="Sign in with Base Dark Mode" style={{ width: '1000px', height: 'auto', marginRight: '1rem' }} />
</div>

<div style={{ textAlign: 'center', fontStyle: 'italic', marginBottom: '2rem' }}>
  (Click to enlarge)
</div>

<div style={{ display: 'flex', justifyContent: 'center', margin: '2rem 0' }}>
  <img src="/images/base-account/SIWB-Light-Mode.jpg" alt="Sign in with Base Light Mode" style={{ width: '1000px', height: 'auto', marginRight: '1rem' }} />
</div>

<div style={{ textAlign: 'center', fontStyle: 'italic', marginBottom: '2rem' }}>
  (Click to enlarge)
</div>

### Examples

<div style={{ display: 'flex', justifyContent: 'center', margin: '2rem 0' }}>
  <img src="/images/base-account/SIWB-Examples.jpg" alt="Sign in with Base Examples" style={{ width: '600px', height: 'auto' }} />
</div>

<div style={{ textAlign: 'center', fontStyle: 'italic', marginBottom: '2rem' }}>
(Click to enlarge)
</div>

### Creating a custom button

You can customize the "Sign in with Base" button to match the style of your application. Below is an example of Privy using Base branding within their user interface style.

Notice that:
- The ratio and color of the Base Square is maintained 
- A "Sign in" header is present, so just "Base" is used as the sign in option

For detailed technical integration steps and API references, please refer to these docs.

## Base Pay

Integrating "Base Pay" offers one-click checkout for users with a Base Account. Integrate it into your product for easy purchase power for online and offline goods.

<div style={{ display: 'flex', justifyContent: 'center', backgroundColor: '#000000', padding: '20px', borderRadius: '8px' }}>
  <button
      type="button"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '12px 16px',
        backgroundColor: '#ffffff',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        minWidth: '180px',
        height: '44px'
      }}
    >
      <img
        src="/images/base-account/BasePayBlueLogo.png"
        alt="Base Pay"
        style={{
          height: '20px',
          width: 'auto'
        }}
      />
    </button>
</div>
<br />
<div style={{ display: 'flex', justifyContent: 'center', backgroundColor: '#ffffff', padding: '20px', borderRadius: '8px' }}>
  <button
      type="button"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '12px 16px',
        backgroundColor: '#0000FF',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        minWidth: '180px',
        height: '44px'
      }}
    >
      <img
        src="/images/base-account/BasePayWhiteLogo.png"
        alt="Base Pay"
        style={{
          height: '20px',
          width: 'auto'
        }}
      />
    </button>
</div>

### Design & Brand Guidelines

The "Base Pay" button should be easily recognizable and consistent across all platforms. Adhering to these design guidelines ensures a familiar and trusted experience for users.

#### Button Appearance

The "Base Pay" button always uses a combination mark. It never uses typography or text to write "Base Pay" or "base pay".

Following are some DOs and DON'Ts for the Base branding:

#### DO
- Always use the "Base Pay" combination mark 
- Use the all white version of the combination mark on dark backgrounds
- Use at least 1X the height of the button for padding. If the mark is 24px high, pad the button with at least 24px on all sides

#### DON'T
- Write "Base Pay" or "base pay" using fonts or text
- Change the combination mark in any way
- Change the color of the Base Square
- Use Base Blue on a dark background

### Examples

<div style={{ display: 'flex', justifyContent: 'center', margin: '2rem 0' }}>
  <img src="/images/base-account/BasePay-Buttons.jpg" alt="Base Pay Buttons" style={{ width: '500px', height: 'auto' }} />
</div>

<div style={{ textAlign: 'center', fontStyle: 'italic', marginBottom: '2rem' }}>
  (Click to enlarge)
</div>

<div style={{ display: 'flex', justifyContent: 'center', margin: '2rem 0' }}>
  <img src="/images/base-account/BasePay-Examples.jpg" alt="Base Pay Examples" style={{ width: '600px', height: 'auto' }} />
</div>

<div style={{ textAlign: 'center', fontStyle: 'italic', marginBottom: '2rem' }}>
  (Click to enlarge)
</div>

## Media Assets

You can find the full set of Base Pay and Sign in with Base media assets in the [Base Brand Assets Figma File](https://www.figma.com/design/L8K5httaDFjiAqfWrChMFi/Sign-in-with-Base---Base-Pay%E2%80%94Media-Kit?node-id=0-1&p=f&t=5766QEX963be2lrj-0).

