---
title: "requestRevoke"
description: "Request the user's approval to revoke a Spend Permission"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
  Prompts the user to approve revoking the specified spend permission. Requires
  user interaction via their Base Account wallet UI.
</Info>

## Parameters

<ParamField body="permission" type="SpendPermission" required>
  The spend permission to revoke. This should be a SpendPermission object returned from [`requestSpendPermission`](/base-account/reference/spend-permission-utilities/requestSpendPermission) or fetched via [`fetchPermissions`](/base-account/reference/spend-permission-utilities/fetchPermissions).

<Expandable title="SpendPermission properties">
<ParamField body="permissionHash" type="string">
Deterministic EIP-712 hash of the permission.
</ParamField>

<ParamField body="signature" type="string">
  Signature for the EIP-712 payload.
</ParamField>

<ParamField body="chainId" type="number">
  Target chain ID.
</ParamField>

<ParamField body="permission" type="object">
Underlying permission fields.

<Expandable title="permission fields">
<ParamField body="account" type="address" />
<ParamField body="spender" type="address" />
<ParamField body="token" type="address" />
<ParamField body="allowance" type="bigint" />
<ParamField body="period" type="number">Duration in seconds.</ParamField>
<ParamField body="start" type="number">Unix timestamp (seconds).</ParamField>
<ParamField body="end" type="number">Unix timestamp (seconds).</ParamField>
<ParamField body="salt" type="string" />
<ParamField body="extraData" type="string" />
</Expandable>
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="hash" type="`0x${string}`">
  Transaction hash of the revoke operation.
</ResponseField>

<RequestExample>
```typescript User-initiated revoke
import { requestRevoke } from "@base-org/account/spend-permission";

try {
  const hash = await requestRevoke(permission);
  console.log("Revoke succeeded", hash);
} catch (err) {
  console.warn("Revoke was rejected or failed", err);
}
```
</RequestExample>

## Error Handling

Always wrap the call in a try-catch block to handle these errors gracefully.

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
