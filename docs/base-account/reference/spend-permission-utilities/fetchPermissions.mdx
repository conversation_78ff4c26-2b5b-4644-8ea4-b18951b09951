---
title: "fetchPermissions"
description: "Retrieve available Spend Permissions for an account and chain, optionally filtered by spender"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
  Returns permissions for a user's Base Account on a specific chain. If a `spender` is provided, returns only permissions for that spender. Otherwise, returns all permissions for the account.
</Info>

## Parameters

<ParamField body="account" type="address" required>
  User's Base Account address to query.
</ParamField>

<ParamField body="chainId" type="number" required>
  Target chain ID.
</ParamField>

<ParamField body="spender" type="address">
  Optional. Spender address you intend to use for spending. If not provided, returns all permissions for the account.
</ParamField>

<ParamField body="provider" type="EIP1193Provider">
  Optional. EIP-1193 compliant Ethereum provider instance. Get this from `sdk.getProvider()`. If not provided, uses the default SDK provider.
</ParamField>

## Returns

<ResponseField name="permissions" type="SpendPermission[]">
  Array of spend permissions matching the query.

<Expandable title="SpendPermission properties">
<ResponseField name="permissionHash" type="string">
Deterministic EIP-712 hash of the permission.
</ResponseField>

<ResponseField name="signature" type="string">
  Signature for the EIP-712 payload.
</ResponseField>

<ResponseField name="chainId" type="number">
  Target chain ID.
</ResponseField>

<ResponseField name="permission" type="object">
Underlying permission fields.

<Expandable title="permission fields">
<ResponseField name="account" type="address" />
<ResponseField name="spender" type="address" />
<ResponseField name="token" type="address" />
<ResponseField name="allowance" type="bigint" />
<ResponseField name="period" type="number">Duration in seconds.</ResponseField>
<ResponseField name="start" type="number">Unix timestamp (seconds).</ResponseField>
<ResponseField name="end" type="number">Unix timestamp (seconds).</ResponseField>
<ResponseField name="salt" type="string" />
<ResponseField name="extraData" type="string" />
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```typescript Fetch permissions for specific spender
import { fetchPermissions } from "@base-org/account/spend-permission";
import { createBaseAccountSDK } from "@base-org/account";

const sdk = createBaseAccountSDK({
  appName: 'My App',
  appLogoUrl: 'https://example.com/logo.png',
  appChainIds: [84532],
});

// Fetch permissions for a specific spender
const permissions = await fetchPermissions({
  account: "0xUserBaseAccountAddress",
  chainId: 84532,
  spender: "0xAppSpenderAddress",
  provider: sdk.getProvider(),
});

// Fetch all permissions for the account (omitting spender)
const allPermissions = await fetchPermissions({
  account: "0xUserBaseAccountAddress",
  chainId: 84532,
});
```
</RequestExample>

## Error Handling

Always wrap the call in a try-catch block to handle these errors gracefully.

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
