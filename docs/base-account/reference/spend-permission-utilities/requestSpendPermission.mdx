---
title: "requestSpendPermission"
description: "Create and sign an EIP-712 Spend Permission for a user's Base Account"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
  Constructs an EIP-712 payload for a spend permission and prompts the user to
  sign it. Returns a `SpendPermission` object containing the signature and
  normalized permission data you can later use to spend or register onchain.
</Info>

## Parameters

<ParamField body="account" type="address" required>
  Smart account that this spend permission applies to.
</ParamField>

<ParamField body="spender" type="address" required>
  The spender that can move funds from `account` within the configured limits.
</ParamField>

<ParamField body="token" type="address" required>
  Token address. Supports ERC-7528 native token address and ERC-20 contracts.
</ParamField>

<ParamField body="chainId" type="number" required>
  Chain ID where this permission is valid.
</ParamField>

<ParamField body="allowance" type="bigint" required>
  Maximum amount that can be spent during each period (in wei).
</ParamField>

<ParamField body="periodInDays" type="number" required>
  Length of each allowance period in days.
</ParamField>

<ParamField body="start" type="Date">
  Start time when the spend permission becomes valid. Defaults to now.
</ParamField>

<ParamField body="end" type="Date">
  Time when the spend permission expires. Defaults to never.
</ParamField>

<ParamField body="salt" type="string">
  Arbitrary salt to differentiate otherwise identical permissions. Hex string.
  Defaults to a random value.
</ParamField>

<ParamField body="extraData" type="string">
  Arbitrary data to attach to the permission. Hex string. Defaults to `0x`.
</ParamField>

<ParamField body="provider" type="EIP1193Provider" required>
  EIP-1193 compliant Ethereum provider instance. Get this from `sdk.getProvider()`.
</ParamField>

## Returns

<ResponseField name="permission" type="SpendPermission">
Signed spend permission payload.

<Expandable title="SpendPermission properties">
<ResponseField name="permissionHash" type="string">
Deterministic EIP-712 hash of the permission.
</ResponseField>

<ResponseField name="signature" type="string">
  Signature for the EIP-712 payload.
</ResponseField>

<ResponseField name="chainId" type="number">
  Target chain ID.
</ResponseField>

<ResponseField name="permission" type="object">
Underlying permission fields.

<Expandable title="permission fields">
<ResponseField name="account" type="address" />
<ResponseField name="spender" type="address" />
<ResponseField name="token" type="address" />
<ResponseField name="allowance" type="bigint" />
<ResponseField name="period" type="number">Duration in seconds.</ResponseField>
<ResponseField name="start" type="number">Unix timestamp (seconds).</ResponseField>
<ResponseField name="end" type="number">Unix timestamp (seconds).</ResponseField>
<ResponseField name="salt" type="string" />
<ResponseField name="extraData" type="string" />
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```typescript Create and sign a spend permission
import { requestSpendPermission } from "@base-org/account/spend-permission";
import { createBaseAccountSDK } from "@base-org/account";

const sdk = createBaseAccountSDK({
  appName: 'My App',
  appLogoUrl: 'https://example.com/logo.png',
  appChainIds: [84532],
});

const permission = await requestSpendPermission({
  account: "0xUserBaseAccountAddress",
  spender: "0xAppSpenderAddress",
  token: "0xTokenContractAddress",
  chainId: 84532,
  allowance: 1_000_000n,
  periodInDays: 30,
  provider: sdk.getProvider(),
});

console.log("Spend Permission:", permission);
```
</RequestExample>

## Error Handling

Always wrap the call in a try-catch block to handle these errors gracefully.

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
