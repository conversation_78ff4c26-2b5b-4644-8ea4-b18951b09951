---
title: "prepareSpendCallData"
description: "Prepare calldata to approve (if needed) and spend using a Spend Permission"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
  Returns one or two calls your app's spender can submit to execute a spend. If
  the permission is not yet registered onchain, an `approveWithSignature` call
  is prepended before the `spend` call.
</Info>

## Parameters

<ParamField body="permission" type="SpendPermission" required>
  Signed permission returned from [`requestSpendPermission`](/base-account/reference/spend-permission-utilities/requestSpendPermission) or fetched via [`fetchPermissions`](/base-account/reference/spend-permission-utilities/fetchPermissions).

<Expandable title="SpendPermission properties">
<ParamField body="permissionHash" type="string">
Deterministic EIP-712 hash of the permission.
</ParamField>

<ParamField body="signature" type="string">
  Signature for the EIP-712 payload.
</ParamField>

<ParamField body="chainId" type="number">
  Target chain ID.
</ParamField>

<ParamField body="permission" type="object">
Underlying permission fields.

<Expandable title="permission fields">
<ParamField body="account" type="address" />
<ParamField body="spender" type="address" />
<ParamField body="token" type="address" />
<ParamField body="allowance" type="bigint" />
<ParamField body="period" type="number">Duration in seconds.</ParamField>
<ParamField body="start" type="number">Unix timestamp (seconds).</ParamField>
<ParamField body="end" type="number">Unix timestamp (seconds).</ParamField>
<ParamField body="salt" type="string" />
<ParamField body="extraData" type="string" />
</Expandable>
</ParamField>
</Expandable>
</ParamField>

<ParamField body="amount" type="bigint">
  Amount to spend (in wei). Omit to spend the remaining allowance.
</ParamField>

## Returns

<ResponseField name="result" type="Call[]">
Array of calls to submit in order.

<Expandable title="Call type">
<ResponseField name="to" type="string" />
<ResponseField name="data" type="`0x${string}`" />
<ResponseField name="value" type="`0x0`" />
</Expandable>
</ResponseField>

<RequestExample>
```typescript Prepare and submit spend calls
import { prepareSpendCallData } from "@base-org/account/spend-permission";

const spendCalls = await prepareSpendCallData({
  permission,
  amount: 10_000n, // optional
});

// If supported, submit both in a batch via wallet_sendCalls
await provider.request({
  method: "wallet_sendCalls",
  params: [
    {
      version: "2.0",
      atomicRequired: true,
      from: spender,
      calls: spendCalls,
    },
  ],
});

// Or submit sequentially with eth_sendTransaction
for (const call of spendCalls) {
  await provider.request({
    method: "eth_sendTransaction",
    params: [{ ...call, from: spender }],
  });
}
```
</RequestExample>

## Error Handling

Always wrap the call in a try-catch block to handle these errors gracefully.

<Info>
Use `getPermissionStatus` to check `isActive` and `remainingSpend` before preparing a spend.
</Info>

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
