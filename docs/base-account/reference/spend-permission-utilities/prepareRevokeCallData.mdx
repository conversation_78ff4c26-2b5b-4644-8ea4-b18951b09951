---
title: "prepareRevokeCallData"
description: "Construct calldata so your app's spender can revoke a Spend Permission without user interaction"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
  Builds a single revoke call that your app's spender account can submit to
  revoke a permission silently. This requires your app to control a spender
  account with rights to revoke.
</Info>

## Parameters

<ParamField body="permission" type="SpendPermission" required>
  The spend permission to revoke. This should be a SpendPermission object returned from [`requestSpendPermission`](/base-account/reference/spend-permission-utilities/requestSpendPermission) or fetched via [`fetchPermissions`](/base-account/reference/spend-permission-utilities/fetchPermissions).

<Expandable title="SpendPermission properties">
<ParamField body="permissionHash" type="string">
Deterministic EIP-712 hash of the permission.
</ParamField>

<ParamField body="signature" type="string">
  Signature for the EIP-712 payload.
</ParamField>

<ParamField body="chainId" type="number">
  Target chain ID.
</ParamField>

<ParamField body="permission" type="object">
Underlying permission fields.

<Expandable title="permission fields">
<ParamField body="account" type="address" />
<ParamField body="spender" type="address" />
<ParamField body="token" type="address" />
<ParamField body="allowance" type="bigint" />
<ParamField body="period" type="number">Duration in seconds.</ParamField>
<ParamField body="start" type="number">Unix timestamp (seconds).</ParamField>
<ParamField body="end" type="number">Unix timestamp (seconds).</ParamField>
<ParamField body="salt" type="string" />
<ParamField body="extraData" type="string" />
</Expandable>
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="revokeCall" type="Call">
Call data for the revoke transaction.

<Expandable title="Call type">
<ResponseField name="to" type="string" />
<ResponseField name="data" type="`0x${string}`" />
<ResponseField name="value" type="`0x0`" />
</Expandable>
</ResponseField>

<RequestExample>
```typescript Silent revoke via spender account
import { prepareRevokeCallData } from "@base-org/account/spend-permission";

const revokeCall = await prepareRevokeCallData(permission);

await provider.request({
  method: "wallet_sendCalls",
  params: [
    {
      version: "2.0",
      atomicRequired: true,
      from: spender,
      calls: [revokeCall],
    },
  ],
});
```
</RequestExample>

## Error Handling

Always wrap the call in a try-catch block to handle these errors gracefully.

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
