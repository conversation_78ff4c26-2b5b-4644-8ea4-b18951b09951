---
title: "atomic"
description: "Ensures batched transactions are executed atomically and contiguously"
---

Defined in [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792)

<Info>
The atomic capability specifies how wallets execute batches of transactions, providing guarantees for atomic transaction execution. When supported, all transactions in a batch must succeed together or fail together.
</Info>

## Parameters

<ParamField body="supported" type="string" required>
The atomic capability status for the current chain and account.

**Possible values:**
- `"supported"`: Wallet will execute all calls atomically and contiguously
- `"ready"`: Wallet can upgrade to atomic execution pending user approval  
- `"unsupported"`: No atomicity or contiguity guarantees
</ParamField>

## Returns

<ResponseField name="atomic" type="object">
The atomic capability configuration for the specified chain.

<Expandable title="Atomic capability properties">
<ResponseField name="supported" type="string">
Indicates the level of atomic execution support available.
</ResponseField>
</Expandable>
</ResponseField>

## Example Usage

<RequestExample>
```typescript Check Capability Support
const capabilities = await provider.request({
  method: 'wallet_getCapabilities',
  params: ['******************************************']
});

console.log(capabilities["0x2105"].atomic);
```

```typescript Send Atomic Transactions
const result = await provider.request({
  method: "wallet_sendCalls",
  params: [{
    version: "1.0",
    chainId: "0x2105",
    from: "******************************************",
    atomicRequired: true,
    calls: [
      {
        to: "******************************************",
        value: "0x0",
        data: "0xa9059cbb000000000000000000000000..."
      }
    ]
  }]
});
```
</RequestExample>

<ResponseExample>
```json Capability Response (Supported)
{
  "0x2105": {
    "atomic": {
      "supported": "supported"
    }
  }
}
```

```json Capability Response (Ready for Upgrade)
{
  "0x2105": {
    "atomic": {
      "supported": "ready"
    }
  }
}
```

```json Capability Response (Unsupported)
{
  "0x2105": {
    "atomic": {
      "supported": "unsupported"
    }
  }
}
```
</ResponseExample>

## Error Handling

| Code | Message | Description |
| ---- | ------- | ----------- |
| 4100 | Atomic execution not supported | Wallet does not support atomic transaction execution |
| 5700 | Atomic capability required | Transaction requires atomic execution but wallet doesn't support it |
| 5750 | Atomic upgrade rejected | User rejected the upgrade to atomic execution capability |

## Use Cases

### DeFi Operations

Atomic execution is crucial for DeFi operations where multiple steps must complete together:

```typescript
// Swap tokens atomically
const swapCalls = await provider.request({
  method: "wallet_sendCalls",
  params: [{
    version: "1.0",
    chainId: "0x2105",
    from: userAddress,
    atomicRequired: true,
    calls: [
      // 1. Approve token spend
      {
        to: tokenAddress,
        value: "0x0",
        data: approveCallData
      },
      // 2. Execute swap
      {
        to: swapContractAddress,
        value: "0x0",
        data: swapCallData
      },
      // 3. Claim rewards (if applicable)
      {
        to: rewardsContractAddress,
        value: "0x0",
        data: claimCallData
      }
    ]
  }]
});
```

### NFT Minting with Payment

```typescript
// Mint NFT and pay fees atomically
const mintCalls = await provider.request({
  method: "wallet_sendCalls",
  params: [{
    version: "1.0",
    chainId: "0x2105",
    from: userAddress,
    atomicRequired: true,
    calls: [
      // 1. Pay minting fee
      {
        to: paymentAddress,
        value: "0x16345785d8a0000", // 0.1 ETH
        data: "0x"
      },
      // 2. Mint NFT
      {
        to: nftContractAddress,
        value: "0x0",
        data: mintCallData
      }
    ]
  }]
});
```

## Error Handling

Handle atomic capability errors appropriately:

```typescript
async function executeAtomicTransaction(calls) {
  try {
    // Check atomic capability first
    const capabilities = await provider.request({
      method: 'wallet_getCapabilities',
      params: [userAddress]
    });
    
    const atomicCapability = capabilities["0x2105"]?.atomic;
    
    if (!atomicCapability || atomicCapability === "unsupported") {
      throw new Error("Atomic execution not supported");
    }
    
    // Execute atomic transaction
    const result = await provider.request({
      method: "wallet_sendCalls",
      params: [{
        version: "1.0",
        chainId: "0x2105",
        from: userAddress,
        atomicRequired: true,
        calls
      }]
    });
    
    return result;
    
  } catch (error) {
    if (error.code === 4100) {
      console.error("Atomic execution not supported by wallet");
      // Fallback to sequential execution
      return executeSequentialTransaction(calls);
    } else {
      console.error("Atomic transaction failed:", error);
      throw error;
    }
  }
}
```

## Relationship with EIP-7702

The atomic capability works with EIP-7702 to enable EOA (Externally Owned Accounts) to upgrade to smart accounts that support atomic transaction execution:

```typescript
// Check if wallet can upgrade to atomic execution  
const capabilities = await provider.request({
  method: 'wallet_getCapabilities',
  params: [eoaAddress]
});

if (capabilities["0x2105"].atomic === "ready") {
  console.log("Wallet can upgrade to support atomic execution with user approval");
}
```

## Best Practices

1. **Check Capabilities First**: Always verify atomic support before requiring it
2. **Provide Fallbacks**: Implement sequential execution as a fallback when atomic isn't available
3. **Use for Related Operations**: Only require atomicity for operations that must succeed together
4. **Clear Error Messages**: Provide helpful error messages when atomic execution fails

<Warning>
The atomic capability is chain-specific. Always check support for the specific chain you're targeting.
</Warning>

<Info>
Apps should first check wallet capabilities using `wallet_getCapabilities` before sending requests requiring atomic execution.
</Info>

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />