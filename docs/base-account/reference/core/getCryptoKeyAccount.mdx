---
title: "getCryptoKeyAccount"
description: "Retrieve the current crypto key account associated with the user's session"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)



## Parameters

This function takes no parameters.

## Returns

<ResponseField name="result" type="CryptoKeyAccountResult">
An object containing the user's crypto key account information or null if none exists.

<Expandable title="CryptoKeyAccountResult properties">
<ResponseField name="account" type="WebAuthnAccount | LocalAccount | null">
The user's crypto key account object, or null if none is available.

<Expandable title="WebAuthnAccount properties">

<ResponseField name="publicKey" type="string">
Public key associated with the account.
</ResponseField>

<ResponseField name="type" type="string">
Account type identifier. Value: "webauthn"
</ResponseField>
</Expandable>

<Expandable title="LocalAccount properties">
<ResponseField name="address" type="string">
Ethereum address of the account (42-character hex string starting with 0x).
</ResponseField>

<ResponseField name="publicKey" type="string">
Public key associated with the account.
</ResponseField>

<ResponseField name="type" type="string">
Account type identifier. Value: "local"
</ResponseField>
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```typescript Basic Usage
import { getCryptoKeyAccount } from '@base-org/account';

const cryptoAccount = await getCryptoKeyAccount();
if (cryptoAccount?.account) {
  console.log('Account address:', cryptoAccount.account.address);
}
```

```typescript Account Verification
const cryptoAccount = await getCryptoKeyAccount();

if (!cryptoAccount?.account) {
  console.log('No account found - user needs to sign in');
  return null;
}

const { account } = cryptoAccount;
console.log('Account type:', account.type);
```
</RequestExample>

<ResponseExample>
```typescript Success Response (WebAuthn Account)
{
  account: {
    address: "******************************************",
    publicKey: "0x04a1b2c3d4e5f6...",
    type: "webauthn"
  }
}
```

```typescript Success Response (Local Account)
{
  account: {
    address: "******************************************",
    publicKey: "0x04b2c3d4e5f6a7...",
    type: "local"
  }
}
```

```typescript Success Response (No Account)
{
  account: null
}
```
</ResponseExample>

## Error Handling

| Code | Message | Description |
| ---- | ------- | ----------- |
| 4001 | User denied account access | User rejected the account access request |
| 4100 | SDK not initialized | Base Account SDK not properly initialized |
| 4200 | Session expired | User session has expired, requires reconnection |
| 4300 | Account unavailable | Account temporarily unavailable |

<Warning>
Always check if the account exists before using it, as users may not be connected or may have disconnected.
</Warning>

## Account State Management

```typescript
import { getCryptoKeyAccount } from '@base-org/account';

class AccountManager {
  private currentAccount: WebAuthnAccount | LocalAccount | null = null;
  
  async initialize() {
    const cryptoAccount = await getCryptoKeyAccount();
    this.currentAccount = cryptoAccount?.account || null;
    
    return this.isConnected();
  }
  
  isConnected(): boolean {
    return !!this.currentAccount;
  }
  
  getAddress(): string | null {
    return this.currentAccount?.address || null;
  }
  
  getAccountType(): 'webauthn' | 'local' | null {
    return this.currentAccount?.type || null;
  }
  
  async refresh() {
    const cryptoAccount = await getCryptoKeyAccount();
    const newAccount = cryptoAccount?.account;
    
    // Check if account changed
    if (newAccount?.address !== this.currentAccount?.address) {
      console.log('Account changed:', newAccount?.address);
      this.currentAccount = newAccount;
      return true;
    }
    
    return false;
  }
}
```

## Integration with Provider

```typescript
import { getCryptoKeyAccount, createBaseAccountSDK } from '@base-org/account';

async function initializeApp() {
  const sdk = createBaseAccountSDK({
    appName: 'My App',
    appLogoUrl: 'https://example.com/logo.png',
    appChainIds: [8453], // Base mainnet
  });
  
  // Check current account status
  const cryptoAccount = await getCryptoKeyAccount();
  
  if (cryptoAccount?.account) {
    console.log('User is already connected:', cryptoAccount.account.address);
    
    // Get provider for transactions
    const provider = sdk.getProvider();
    
    return {
      sdk,
      provider,
      account: cryptoAccount.account,
      isConnected: true
    };
  } else {
    console.log('User needs to connect');
    
    return {
      sdk,
      provider: null,
      account: null,
      isConnected: false
    };
  }
}
```

## Account Verification

```typescript
async function verifyAccountAccess() {
  const cryptoAccount = await getCryptoKeyAccount();
  
  if (!cryptoAccount?.account) {
    throw new Error('No account available');
  }
  
  const { account } = cryptoAccount;
  
  // Verify account has required properties
  if (!account.address || !account.publicKey) {
    throw new Error('Invalid account data');
  }
  
  // Verify address format
  if (!/^0x[a-fA-F0-9]{40}$/.test(account.address)) {
    throw new Error('Invalid address format');
  }
  
  return account;
}
```

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />