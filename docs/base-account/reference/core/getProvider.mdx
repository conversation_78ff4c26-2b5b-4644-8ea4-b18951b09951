---
title: "getProvider"
description: "Get an Ethereum provider instance from the Base Account SDK"
---

The `getProvider()` method returns an Ethereum provider instance that complies with [EIP-1193](https://eips.ethereum.org/EIPS/eip-1193) standards. This provider can be used with popular web3 libraries like Viem, Wagmi, and Web3.js.

## Usage

```tsx
import { createBaseAccountSDK, base } from '@base-org/account';

const sdk = createBaseAccountSDK({
  appName: 'My App Name',
  appLogoUrl: 'https://example.com/logo.png',
  appChainIds: [base.constants.CHAIN_IDS.base],
});

const provider = sdk.getProvider();
```

## Returns

An EIP-1193 compliant Ethereum provider that supports:
- Standard RPC methods (`eth_requestAccounts`, `eth_sendTransaction`, `wallet_sendCalls` etc.)
- Custom Wallet methods (`coinbase_fetchPermissions`)
- Event subscription (`accountsChanged`, `chainChanged`, etc.)

For a full list of supported methods, see the [Provider Section](/base-account/reference/core/provider-rpc-methods/request-overview)

## Integration Examples

### With Viem

```tsx
import { createWalletClient, custom } from 'viem';
import { base } from 'viem/chains';

const provider = sdk.getProvider();

const client = createWalletClient({
  chain: base,
  transport: custom(provider)
});

// Use the client
const [account] = await client.getAddresses();
const hash = await client.sendTransaction({
  account,
  to: '0x...',
  value: parseEther('0.1')
});
```

### With Wagmi

```tsx
import { createConfig, custom } from 'wagmi';
import { base } from 'wagmi/chains';

const provider = sdk.getProvider();

const config = createConfig({
  chains: [base],
  transports: {
    [base.id]: custom(provider),
  },
});
```

### Direct Provider Usage

```tsx
// Request accounts
const accounts = await provider.request({ 
  method: 'eth_requestAccounts' 
});

// Send transaction
const txHash = await provider.request({
  method: 'eth_sendTransaction',
  params: [{
    from: accounts[0],
    to: '0x...',
    value: '0x38d7ea4c68000', // 0.001 ETH in wei
  }]
});

// Send batch transactions
const result = await provider.request({
  method: 'wallet_sendCalls',
  params: [{
    version: '2.0.0',
    from: accounts[0],
    chainId: '0x2105', // Base mainnet
    atomicRequired: true,
    calls: [
      {
        to: '0x...',
        value: '0x0',
        data: '0x...'
      }
    ]
  }]
});
```

## Event Handling

The provider emits standard EIP-1193 events:

```tsx
// Listen for account changes
provider.on('accountsChanged', (accounts) => {
  console.log('Accounts changed:', accounts);
});

// Listen for chain changes
provider.on('chainChanged', (chainId) => {
  console.log('Chain changed:', chainId);
});

// Listen for connection events
provider.on('connect', (connectInfo) => {
  console.log('Connected:', connectInfo);
});

// Listen for disconnection
provider.on('disconnect', (error) => {
  console.log('Disconnected:', error);
});
```

## Error Handling

Handle provider errors gracefully:

```tsx
try {
  const accounts = await provider.request({ 
    method: 'eth_requestAccounts' 
  });
} catch (error) {
  if (error.code === 4001) {
    console.log('User rejected the request');
  } else if (error.code === -32602) {
    console.log('Invalid parameters');
  } else {
    console.error('Unexpected error:', error);
  }
}
```

## Provider Configuration

The provider behavior is configured through the SDK initialization:

```tsx
const sdk = createBaseAccountSDK({
  appName: 'My App Name',
  appLogoUrl: 'https://example.com/logo.png',
  appChainIds: [base.constants.CHAIN_IDS.base],
  // Additional configuration affects provider behavior
  subAccounts: {
    toOwnerAccount: async () => ({ account: cryptoAccount?.account })
  }
});
```

## TypeScript Support

The provider is fully typed when using TypeScript:

```tsx
import type { EIP1193Provider } from '@base-org/account';

const provider: EIP1193Provider = sdk.getProvider();

// TypeScript will provide full intellisense for supported methods
```

The `getProvider()` method is the primary way to interact with Base Account from your application, providing a standard interface that works seamlessly with the web3 ecosystem.
