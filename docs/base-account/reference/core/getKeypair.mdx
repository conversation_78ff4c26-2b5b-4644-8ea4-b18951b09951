---
title: "getKeypair"
description: "Retrieve an existing P256 key pair from storage"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
Retrieves an existing P256 key pair if one has been previously generated and stored. This is useful for checking if keys already exist before generating new ones.
</Info>

## Parameters

This function takes no parameters.

## Returns

<ResponseField name="result" type="P256KeyPair | null">
The stored P256 key pair or `null` if no key pair exists.

<Expandable title="P256KeyPair properties">
<ResponseField name="publicKey" type="string">
The public key for the stored pair in hexadecimal format.
</ResponseField>

<ResponseField name="privateKey" type="string">
The private key for the stored pair. Handle with extreme care.
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```typescript Basic Usage
import { getKeypair } from '@base-org/account';

const existingKeyPair = await getKeypair();
if (existingKeyPair) {
  console.log('Found existing key pair');
} else {
  console.log('No existing key pair found');
}
```

```typescript Get or Create Pattern
import { getKeypair, generateKeyPair } from '@base-org/account';

let keyPair = await getKeypair();
if (!keyPair) {
  keyPair = await generateKeyPair();
}
```
</RequestExample>

<ResponseExample>
```typescript Success Response (Key Pair Found)
{
  publicKey: "0x04a1b2c3d4e5f6...",
  privateKey: "0x1a2b3c4d5e6f7a..."
}
```

```typescript Success Response (No Key Pair)
null
```
</ResponseExample>

<Warning>
**Private Key Access**

The retrieved private keys should be handled with the same security considerations as newly generated keys.
</Warning>

## Get or Create Pattern

A common pattern is to check for existing keys before generating new ones:

```typescript
import { getKeypair, generateKeyPair } from '@base-org/account';

async function getOrCreateKeyPair() {
  // Try to get existing key pair first
  let keyPair = await getKeypair();
  
  if (!keyPair) {
    // Generate new key pair if none exists
    console.log('No existing key pair, generating new one...');
    keyPair = await generateKeyPair();
  } else {
    console.log('Using existing key pair');
  }
  
  return keyPair;
}
```



## Storage Behavior

The `getKeypair` function retrieves keys from:

- Browser's secure storage (for web applications)
- Platform-specific secure storage (for native applications)
- Memory cache (for the current session)

<Info>
Key pairs are stored securely and are only accessible within the same origin and application context.
</Info>

## Error Handling

The `getKeypair` function can throw errors for:

- Storage access failures
- Data corruption issues
- Browser compatibility problems

Always wrap calls to `getKeypair` in a try-catch block:

```typescript
try {
  const keyPair = await getKeypair();
  if (keyPair) {
    // Use existing keys
  } else {
    // No keys found, may need to generate new ones
  }
} catch (error) {
  console.error('Error accessing key storage:', error);
  // Handle storage access errors
}
```

## Key Lifecycle Management

```typescript
class KeyManager {
  private keyPair: P256KeyPair | null = null;
  
  async initialize() {
    try {
      // Load existing keys
      this.keyPair = await getKeypair();
      
      if (this.keyPair) {
        console.log('Loaded existing key pair');
      } else {
        console.log('No stored keys found');
      }
      
      return !!this.keyPair;
    } catch (error) {
      console.error('Failed to initialize key manager:', error);
      return false;
    }
  }
  
  hasKeys(): boolean {
    return !!this.keyPair;
  }
  
  async ensureKeys(): Promise<P256KeyPair> {
    if (!this.keyPair) {
      console.log('Generating new key pair...');
      this.keyPair = await generateKeyPair();
    }
    return this.keyPair;
  }
  
  getPublicKey(): string | null {
    return this.keyPair?.publicKey || null;
  }
}
```

## Security Considerations

<Warning>
**Private Key Access**

The retrieved private keys should be handled with the same security considerations as newly generated keys.
</Warning>

- Always verify key integrity before use
- Implement proper access controls
- Consider re-generating keys periodically for enhanced security

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />