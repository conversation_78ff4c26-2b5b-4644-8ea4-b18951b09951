---
title: "eth_getTransactionCount"
description: "Get the number of transactions sent from an address"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the number of transactions sent from an address.
</Info>

## Parameters

<ParamField body="address" type="string" required>
The address to get the transaction count for (20 bytes).
</ParamField>

<ParamField body="blockParameter" type="string" required>
Integer block number, or the string "latest", "earliest" or "pending".
</ParamField>

## Returns

<ResponseField name="result" type="string">
A hexadecimal string representing the integer of the number of transactions sent from this address.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getTransactionCount",
  "params": [
    "******************************************",
    "latest"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x1"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                            | Description |
| ------ | ---------------------------------- | ----------- |
| -32602 | Invalid address or block parameter | The provided address or block parameter is invalid |
| 4100   | Requested method not supported     | The method is not supported by the wallet |

<Info>
This value is used as the nonce for subsequent transactions from the address.
</Info>
