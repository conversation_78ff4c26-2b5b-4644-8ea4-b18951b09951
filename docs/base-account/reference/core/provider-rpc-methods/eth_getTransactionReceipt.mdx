---
title: "eth_getTransactionReceipt"
description: "Get the receipt of a transaction by transaction hash"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the receipt of a transaction by transaction hash. Note that the receipt is not available for pending transactions.
</Info>

## Parameters

<ParamField body="transactionHash" type="string" required>
The hash of a transaction (32 bytes).
</ParamField>

## Returns

<ResponseField name="result" type="object">
A transaction receipt object, or null when no receipt was found.

<Expandable title="Transaction receipt properties">
<ResponseField name="transactionHash" type="string">
Hash of the transaction.
</ResponseField>

<ResponseField name="transactionIndex" type="string">
Integer of the transaction index position in the block.
</ResponseField>

<ResponseField name="blockHash" type="string">
Hash of the block where this transaction was in.
</ResponseField>

<ResponseField name="blockNumber" type="string">
Block number where this transaction was in.
</ResponseField>

<ResponseField name="from" type="string">
Address of the sender.
</ResponseField>

<ResponseField name="to" type="string">
Address of the receiver. null when it's a contract creation transaction.
</ResponseField>

<ResponseField name="cumulativeGasUsed" type="string">
The total amount of gas used when this transaction was executed in the block.
</ResponseField>

<ResponseField name="gasUsed" type="string">
The amount of gas used by this specific transaction alone.
</ResponseField>

<ResponseField name="contractAddress" type="string">
The contract address created, if the transaction was a contract creation, otherwise null.
</ResponseField>

<ResponseField name="logs" type="array">
Array of log objects, which this transaction generated.
</ResponseField>

<ResponseField name="logsBloom" type="string">
Bloom filter for the logs of the transaction.
</ResponseField>

<ResponseField name="status" type="string">
Either 1 (success) or 0 (failure).
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getTransactionReceipt",
  "params": [
    "0xb903239f8543d04b5dc1ba6579132b143087c68db1b2168786408fcbce568238"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "transactionHash": "0xb903239f8543d04b5dc1ba6579132b143087c68db1b2168786408fcbce568238",
    "transactionIndex": "0x1",
    "blockHash": "0xc6ef2fc5426d6ad6fd9e2a26abeab0aa2411b7ab17f30a99d3cb96aed1d1055b",
    "blockNumber": "0xb",
    "from": "******************************************",
    "to": "0x85h43d8a49eeb85d32cf465507dd71d507100c1",
    "cumulativeGasUsed": "0x33bc",
    "gasUsed": "0x4dc",
    "contractAddress": null,
    "logs": [],
    "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
    "status": "0x1"
  }
}
```

```json Not Found
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": null
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid transaction hash       | The provided transaction hash is invalid |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Info>
Transaction receipts are only available for mined transactions. Pending transactions will return null.
</Info>
