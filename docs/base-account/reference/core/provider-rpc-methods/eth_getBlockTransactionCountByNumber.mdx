---
title: "eth_getBlockTransactionCountByNumber"
description: "Get the number of transactions in a block by block number"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the number of transactions in a block by block number.
</Info>

## Parameters

<ParamField body="blockParameter" type="string" required>
Integer block number, or the string "latest", "earliest" or "pending".
</ParamField>

## Returns

<ResponseField name="result" type="string">
The number of transactions in this block as a hexadecimal string.
</ResponseField>

<RequestExample>
```json Latest Block
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getBlockTransactionCountByNumber",
  "params": [
    "latest"
  ]
}
```

```json Specific Block
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getBlockTransactionCountByNumber",
  "params": [
    "0x1b4"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0xa"
}
```

```json Empty Block
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x0"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid block parameter        | The provided block parameter is invalid |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Info>
This method returns the count of transactions in a specific block identified by its number.
</Info>
