---
title: "eth_getStorageAt"
description: "Get the value from a storage position at a given address"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the value from a storage position at a given address.
</Info>

## Parameters

<ParamField body="address" type="string" required>
The address to read from (20 bytes).
</ParamField>

<ParamField body="position" type="string" required>
Integer of the position in the storage (hexadecimal).
</ParamField>

<ParamField body="blockParameter" type="string" required>
Integer block number, or the string "latest", "earliest" or "pending".
</ParamField>

## Returns

<ResponseField name="result" type="string">
The value at this storage position as a hexadecimal string.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getStorageAt",
  "params": [
    "******************************************",
    "0x0",
    "latest"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x0000000000000000000000000000000000000000000000000000000000000001"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                            | Description |
| ------ | ---------------------------------- | ----------- |
| -32602 | Invalid address or storage position | Invalid parameters provided |
| 4100   | Requested method not supported     | The method is not supported by the wallet |

<Info>
Storage positions start at 0x0. The result is a 32-byte hexadecimal value representing the data stored at that position.
</Info>

```

