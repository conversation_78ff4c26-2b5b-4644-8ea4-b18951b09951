---
title: "eth_accounts"
description: "Returns a list of addresses owned by the connected wallet without prompting user authorization"
---

Defined in [EIP-1474](https://github.com/ethereum/EIPs/blob/master/EIPS/eip-1474.md)

<Info>
Returns a list of addresses owned by the connected wallet. Unlike `eth_requestAccounts`, this method returns an empty array if no accounts are available or if the user has not authorized any accounts to the caller. This method does not prompt the user to approve connection.
</Info>

## Parameters

This method does not accept any parameters.

## Returns

<ResponseField name="result" type="Array<string>">
An array of Ethereum addresses (hexadecimal strings), which the connected user controls. The array will typically contain a single address, which is the currently selected account in the wallet. If the wallet is not connected or no accounts are authorized, this method returns an empty array.
</ResponseField>


<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_accounts",
  "params": []
}
```
</RequestExample>

<ResponseExample>
```json Connected Accounts
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": ["0xabc123..."]
}
```

```json No Accounts Connected
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": []
}
```
</ResponseExample>



## Error Handling


| Code | Message                        | Description                                             |
| ---- | ------------------------------ | ------------------------------------------------------- |
| 4100 | Requested method not supported | The provider does not support the `eth_accounts` method |
| 4900 | Disconnected                   | The provider is disconnected from the wallet           |

