---
title: "eth_getBlockTransactionCountByHash"
description: "Get the number of transactions in a block by block hash"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the number of transactions in a block by block hash.
</Info>

## Parameters

<ParamField body="blockHash" type="string" required>
Hash of a block (32 bytes).
</ParamField>

## Returns

<ResponseField name="result" type="string">
The number of transactions in this block as a hexadecimal string.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getBlockTransactionCountByHash",
  "params": [
    "0xc6ef2fc5426d6ad6fd9e2a26abeab0aa2411b7ab17f30a99d3cb96aed1d1055b"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0xa"
}
```

```json Empty Block
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x0"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid block hash             | The provided block hash is invalid |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Info>
This method returns the count of transactions in a specific block identified by its hash.
</Info>

```
