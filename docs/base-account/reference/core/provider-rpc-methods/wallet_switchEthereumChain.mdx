---
title: "wallet_switchEthereum<PERSON>hain"
description: "Switch the wallet to a different Ethereum chain"
---

Defined in [EIP-3326](https://eips.ethereum.org/EIPS/eip-3326)

<Info>
Requests that the wallet switches its active Ethereum chain to one of the chains already added to the wallet.
</Info>

## Parameters

<ParamField body="chainId" type="string" required>
A 0x-prefixed hexadecimal string representing the chain ID of the chain to switch to.
</ParamField>

## Returns

<ResponseField name="result" type="null">
Returns null if successful.
</ResponseField>

<RequestExample>
```json Base Mainnet
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_switchEthereumChain",
  "params": [{
    "chainId": "0x2105"
  }]
}
```

```json Ethereum Mainnet
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_switchEthereum<PERSON>hain",
  "params": [{
    "chainId": "0x1"
  }]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": null
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4001 | User rejected the request      | User denied switching chains |
| 4100 | Requested method not supported | The method is not supported by the wallet |
| 4902 | Unrecognized chain ID          | The requested chain has not been added to the wallet |

<Info>
If the chain is not already added to the wallet, use `wallet_addEthereumChain` first.
</Info>

<Warning>
Switching chains will affect all subsequent blockchain operations in your application.
</Warning>
