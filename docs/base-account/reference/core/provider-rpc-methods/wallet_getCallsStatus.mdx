---
title: "wallet_getCallsStatus"
description: "Get the status of a call batch sent via wallet_sendCalls"
---

Defined in [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792)

<Info>
Returns the status of a call batch that was sent via `wallet_sendCalls`. This method allows applications to track the execution status and retrieve transaction receipts for batch operations.
</Info>

## Parameters

<ParamField body="callsId" type="string" required>
The call bundle identifier returned by a previous `wallet_sendCalls` request.
</ParamField>

## Returns

<ResponseField name="result" type="object">
Status information for the call batch.

<Expandable title="CallsStatus properties">
<ResponseField name="version" type="string">
The version of the API being used. Currently "1.0".
</ResponseField>

<ResponseField name="chainId" type="string">
The chain ID in hexadecimal format.
</ResponseField>

<ResponseField name="id" type="string">
The call bundle identifier.
</ResponseField>

<ResponseField name="status" type="number">
Status code indicating the current state of the batch:
- **1xx (Pending)**: 100 = Batch received but not completed onchain
- **2xx (Confirmed)**: 200 = Batch included onchain without reverts
- **4xx (Offchain failures)**: 400 = Batch failed and wallet will not retry
- **5xx (Chain failures)**: 500 = Batch reverted completely
- **6xx (Partial failures)**: 600 = Batch reverted partially
</ResponseField>

<ResponseField name="atomic" type="boolean">
Indicates whether the wallet executed calls atomically. If `true`, all calls were executed in a single transaction. If `false`, calls were executed in multiple transactions.
</ResponseField>

<ResponseField name="receipts" type="Receipt[]">
Transaction receipts for the call batch. Structure depends on the `atomic` field:
- If `atomic` is `true`: Single receipt or array of receipts for the batch transaction
- If `atomic` is `false`: Array of receipts for all transactions containing batch calls

<Expandable title="Receipt properties">
<ResponseField name="logs" type="Log[]">
The logs generated by the calls. For smart contract wallets, only includes logs relevant to the specific calls.
</ResponseField>

<ResponseField name="status" type="'0x1' | '0x0'">
Transaction status: `0x1` for success, `0x0` for failure.
</ResponseField>

<ResponseField name="blockHash" type="string">
Hash of the block containing these calls.
</ResponseField>

<ResponseField name="blockNumber" type="string">
Block number containing these calls (hex format).
</ResponseField>

<ResponseField name="gasUsed" type="string">
The amount of gas used by these calls (hex format).
</ResponseField>

<ResponseField name="transactionHash" type="string">
Hash of the transaction containing these calls.
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="capabilities" type="object">
Optional capability-specific metadata.
</ResponseField>
</Expandable>
</ResponseField>

## Example Usage

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_getCallsStatus",
  "params": ["0xe670ec64341771606e55d6b4ca35a1a6b75ee3d5145a99d05921026d1527331"]
}
```

```typescript SDK Usage
import { createBaseAccountSDK } from '@base-org/account';

const provider = createBaseAccountSDK().getProvider();

// Get status of a batch sent via wallet_sendCalls
const callsId = "0xe670ec64341771606e55d6b4ca35a1a6b75ee3d5145a99d05921026d1527331"; 

const status = await provider.request({
  method: 'wallet_getCallsStatus',
  params: [callsId]
});

console.log('Batch status:', status.status);
console.log('Atomic execution:', status.atomic);
console.log('Receipts:', status.receipts);
```
</RequestExample>

<ResponseExample>
```json Successful Batch (Atomic)
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "version": "1.0",
    "chainId": "0x2105",
    "id": "0xe670ec64341771606e55d6b4ca35a1a6b75ee3d5145a99d05921026d1527331",
    "status": 200,
    "atomic": true,
    "receipts": [
      {
        "logs": [
          {
            "address": "******************************************",
            "topics": ["0x5a2a90727cc9d000dd060b1132a5c977c9702bb3a52afe360c9c22f0e9451a68"],
            "data": "0xabcd"
          }
        ],
        "status": "0x1",
        "blockHash": "0xf19bbafd9fd0124ec110b848e8de4ab4f62bf60c189524e54213285e7f540d4a",
        "blockNumber": "0xabcd",
        "gasUsed": "0xdef",
        "transactionHash": "0x9b7bb827c2e5e3c1a0a44dc53e573aa0b3af3bd1f9f5ed03071b100bb039eaff"
      }
    ]
  }
}
```

```json Pending Batch
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "version": "1.0",
    "chainId": "0x2105",
    "id": "0xe670ec64341771606e55d6b4ca35a1a6b75ee3d5145a99d05921026d1527331",
    "status": 100,
    "atomic": true,
    "receipts": []
  }
}
```

```json Failed Batch
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "version": "1.0",
    "chainId": "0x2105",
    "id": "0xe670ec64341771606e55d6b4ca35a1a6b75ee3d5145a99d05921026d1527331",
    "status": 500,
    "atomic": true,
    "receipts": [
      {
        "logs": [],
        "status": "0x0",
        "blockHash": "0xf19bbafd9fd0124ec110b848e8de4ab4f62bf60c189524e54213285e7f540d4a",
        "blockNumber": "0xabcd",
        "gasUsed": "0xabc",
        "transactionHash": "0x9b7bb827c2e5e3c1a0a44dc53e573aa0b3af3bd1f9f5ed03071b100bb039eaff"
      }
    ]
  }
}
```
</ResponseExample>

## Status Code Reference

| Code | Category | Meaning |
|------|----------|---------|
| 100 | Pending | Batch received but not completed onchain |
| 200 | Success | Batch included onchain without reverts |
| 400 | Offchain Error | Batch failed, wallet will not retry |
| 500 | Chain Error | Batch reverted completely |
| 600 | Partial Error | Batch reverted partially, some changes may be onchain |

## Error Handling

| Code | Message | Description |
| ---- | ------- | ----------- |
| -32602 | Invalid params | Invalid call bundle identifier |
| 4100 | Method not supported | Wallet doesn't support wallet_getCallsStatus |
| 4200 | Calls not found | No batch found with the specified identifier |

## Usage with wallet_sendCalls

This method is designed to work with batches sent via `wallet_sendCalls`:

```typescript
// Send a batch of calls
const callsId = await provider.request({
  method: 'wallet_sendCalls',
  params: [{
    version: '1.0',
    chainId: '0x2105',
    from: userAddress,
    calls: [
      { to: '0x...', value: '0x0', data: '0x...' },
      { to: '0x...', value: '0x0', data: '0x...' }
    ]
  }]
});

// Poll for status updates
const checkStatus = async () => {
  const status = await provider.request({
    method: 'wallet_getCallsStatus',
    params: [callsId]
  });
  
  if (status.status === 200) {
    console.log('Batch completed successfully!');
    console.log('Transaction receipts:', status.receipts);
  } else if (status.status === 100) {
    console.log('Batch still pending...');
    setTimeout(checkStatus, 2000); // Check again in 2 seconds
  } else {
    console.error('Batch failed with status:', status.status);
  }
};

checkStatus();
```

<Warning>
The receipts structure varies based on whether the batch was executed atomically. Always check the `atomic` field to properly interpret the receipts array.
</Warning>

<Info>
This method follows the EIP-5792 standard for wallet batch operations. Not all wallets may support this method - check wallet capabilities first.
</Info>

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
