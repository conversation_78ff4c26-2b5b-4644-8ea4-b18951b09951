# Overview

## Introduction

CoinbaseWalletProvider is an [JavaScript Ethereum provider](https://eips.ethereum.org/EIPS/eip-1193).
It allows JavaScript applications to make Ethereum RPC requests, via its `request` method.
These requests will be handled in one of three ways

1. Sent to the Wallet (Wallet mobile app, extension, or popup window).
2. Handled locally
3. Passed onto default RPC provider for the given chain, if it exists.
