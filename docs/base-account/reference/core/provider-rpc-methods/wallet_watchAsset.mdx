---
title: "wallet_watchAsset"
description: "Add a token to the wallet's asset watchlist"
---

Defined in [EIP-747](https://eips.ethereum.org/EIPS/eip-747)

<Info>
Requests that the wallet tracks the given token in the user's wallet. Once a token is added, it will be shown in the wallet's token list.
</Info>

## Parameters

<ParamField body="type" type="string" required>
The asset type. Currently only "ERC20" is supported.
</ParamField>

<ParamField body="options" type="object" required>
Token options object.

<Expandable title="Options properties">
<ParamField body="address" type="string" required>
The address of the token contract (42-character hexadecimal address).
</ParamField>

<ParamField body="symbol" type="string" required>
A ticker symbol or shorthand, up to 11 characters.
</ParamField>

<ParamField body="decimals" type="number" required>
The number of token decimals.
</ParamField>

<ParamField body="image" type="string">
A string URL of a token logo image (optional).
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="boolean">
Returns true if the token was successfully added to the watch list.
</ResponseField>

<RequestExample>
```json ERC20 Token
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_watchAsset",
  "params": {
    "type": "ERC20",
    "options": {
      "address": "******************************************",
      "symbol": "USDC",
      "decimals": 6,
      "image": "https://cryptologos.cc/logos/usd-coin-usdc-logo.png"
    }
  }
}
```

```json Custom Token
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_watchAsset",
  "params": {
    "type": "ERC20",
    "options": {
      "address": "******************************************",
      "symbol": "DAI",
      "decimals": 18
    }
  }
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": true
}
```

```json User Rejected
{
  "id": 1,
  "jsonrpc": "2.0",
  "error": {
    "code": 4001,
    "message": "User rejected the request"
  }
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4001 | User rejected the request      | User denied adding the token |
| 4100 | Requested method not supported | The method is not supported by the wallet |
| -32602 | Invalid params                 | Invalid token parameters |

<Warning>
Always verify the token contract address and ensure it's a legitimate token to prevent users from adding malicious tokens.
</Warning>

<Info>
The token will appear in the user's wallet token list if successfully added.
</Info>
