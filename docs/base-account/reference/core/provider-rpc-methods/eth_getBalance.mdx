---
title: "eth_getBalance"
description: "Get the balance of an account at a given block"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the balance of the account of given address.
</Info>

## Parameters

<ParamField body="address" type="string" required>
The address to check for balance (20 bytes).
</ParamField>

<ParamField body="blockParameter" type="string" required>
Integer block number, or the string "latest", "earliest" or "pending".
</ParamField>

## Returns

<ResponseField name="result" type="string">
A hexadecimal string representing the current balance in wei.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getBalance",
  "params": ["******************************************", "latest"]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x0234c8a3397aab58"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                            | Description |
| ------ | ---------------------------------- | ----------- |
| -32602 | Invalid address or block parameter | The provided address or block parameter is invalid |
| 4100   | Requested method not supported     | The method is not supported by the wallet |

<Info>
The result represents the balance in wei. To convert to ETH, divide by 10^18.
</Info>

