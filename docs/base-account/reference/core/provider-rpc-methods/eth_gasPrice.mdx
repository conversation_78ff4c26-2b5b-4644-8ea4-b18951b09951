---
title: "eth_gasPrice"
description: "Get the current gas price"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the current price per gas in wei.
</Info>

## Parameters

This method does not accept any parameters.

## Returns

<ResponseField name="result" type="string">
A hexadecimal string representing the current gas price in wei.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_gasPrice",
  "params": []
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x09184e72a000"
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4100 | Requested method not supported | The method is not supported by the wallet |

<Info>
The result is in wei. To convert to gwei, divide by 10^9.
</Info>
