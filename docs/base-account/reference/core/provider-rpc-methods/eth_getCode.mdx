---
title: "eth_getCode"
description: "Get the bytecode at a given address"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the bytecode at a given address. This is useful for verifying if an address is a contract or an externally owned account.
</Info>

## Parameters

<ParamField body="address" type="string" required>
The address to get bytecode from (20 bytes).
</ParamField>

<ParamField body="blockParameter" type="string" required>
Integer block number, or the string "latest", "earliest" or "pending".
</ParamField>

## Returns

<ResponseField name="result" type="string">
The bytecode at the given address as a hexadecimal string. Returns "0x" for externally owned accounts.
</ResponseField>

<RequestExample>
```json Contract Address
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getCode",
  "params": [
    "******************************************",
    "latest"
  ]
}
```

```json <PERSON>OA Address
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getCode",
  "params": [
    "******************************************",
    "latest"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Contract Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x608060405234801561001057600080fd5b50600436106100365760003560e01c..."
}
```

```json EOA Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                            | Description |
| ------ | ---------------------------------- | ----------- |
| -32602 | Invalid address or block parameter | The provided address or block parameter is invalid |
| 4100   | Requested method not supported     | The method is not supported by the wallet |

<Info>
If the result is "0x", the address is an externally owned account (EOA). If it contains bytecode, it's a smart contract.
</Info>
