---
title: "eth_sendTransaction"
description: "Create and send a new transaction or message call"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Creates new message call transaction or a contract creation, if the data field contains code. This method submits the transaction to the network and returns the transaction hash once it's been accepted by the network.
</Info>

## Parameters

<ParamField body="transaction" type="object" required>
The transaction object to send.

<Expandable title="Transaction object properties">
<ParamField body="to" type="string">
The recipient address (20 bytes). For contract creation transactions, this field should be omitted.
</ParamField>

<ParamField body="from" type="string" required>
The sender address (20 bytes).
</ParamField>

<ParamField body="gas" type="string">
Integer of the gas provided for the transaction execution (hex format).
</ParamField>

<ParamField body="gasPrice" type="string">
Integer of the gasPrice used for each paid gas (hex format).
</ParamField>

<ParamField body="value" type="string">
Integer of the value sent with this transaction (hex format).
</ParamField>

<ParamField body="data" type="string">
The compiled code of a contract OR the hash of the invoked method signature and encoded parameters.
</ParamField>

<ParamField body="nonce" type="string">
Integer of a nonce. This allows to overwrite your own pending transactions that use the same nonce.
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="string">
The transaction hash (32 bytes) as a hexadecimal string.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_sendTransaction",
  "params": [{
    "from": "******************************************",
    "to": "******************************************",
    "gas": "0x76c0",
    "gasPrice": "0x9184e72a000",
    "value": "0x9184e72a",
    "data": "******************************************"
  }]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0xb903239f8543d04b5dc1ba6579132b143087c68db1b2168786408fcbce568238"
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4001 | User rejected the request      | User denied the transaction request |
| 4100 | Requested method not supported | The method is not supported by the wallet |
| 4200 | Wallet not connected           | No wallet connection available |
| -32602 | Invalid params                 | Invalid transaction parameters |
| -32000 | Insufficient funds             | Account doesn't have enough balance |

<Warning>
Make sure to validate transaction parameters before sending, especially the `to` address and `value` to prevent loss of funds.
</Warning>
