---
title: "wallet_addEthereum<PERSON>hain"
description: "Add a new Ethereum chain to the wallet"
---

Defined in [EIP-3085](https://eips.ethereum.org/EIPS/eip-3085)

<Info>
Requests that the wallet tracks the given chain and adds it to the wallet's UI. This method is used to suggest new networks to the user.
</Info>

## Parameters

<ParamField body="chainData" type="object" required>
The chain data object containing network information.

<Expandable title="Chain data properties">
<ParamField body="chainId" type="string" required>
A 0x-prefixed hexadecimal string representing the chain ID.
</ParamField>

<ParamField body="chainName" type="string" required>
The human-readable name of the chain.
</ParamField>

<ParamField body="nativeCurrency" type="object" required>
An object describing the currency that is "native" to this chain.

<Expandable title="Native currency properties">
<ParamField body="name" type="string" required>
The name of the native currency.
</ParamField>

<ParamField body="symbol" type="string" required>
The symbol of the native currency.
</ParamField>

<ParamField body="decimals" type="number" required>
The number of decimals for the native currency.
</ParamField>
</Expandable>
</ParamField>

<ParamField body="rpcUrls" type="array" required>
An array of RPC URL strings.
</ParamField>

<ParamField body="blockExplorerUrls" type="array">
An array of block explorer URL strings (optional).
</ParamField>

<ParamField body="iconUrls" type="array">
An array of icon URL strings (optional).
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="null">
Returns null if successful.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_addEthereumChain",
  "params": [{
    "chainId": "0x2105",
    "chainName": "Base Mainnet",
    "nativeCurrency": {
      "name": "Ether",
      "symbol": "ETH",
      "decimals": 18
    },
    "rpcUrls": ["https://mainnet.base.org"],
    "blockExplorerUrls": ["https://basescan.org"]
  }]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": null
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4001 | User rejected the request      | User denied adding the chain |
| 4100 | Requested method not supported | The method is not supported by the wallet |
| 4902 | Unrecognized chain ID          | The chain ID is not recognized |

<Warning>
Make sure to provide accurate RPC URLs and block explorer URLs. Incorrect URLs may cause connectivity issues.
</Warning>
