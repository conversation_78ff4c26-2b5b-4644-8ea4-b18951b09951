---
title: "eth_getProof"
description: "Get the Merkle proof for account and storage values"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the account and storage values of the specified account including the Merkle-proof.
</Info>

## Parameters

<ParamField body="address" type="string" required>
The address of the account (20 bytes).
</ParamField>

<ParamField body="storageKeys" type="array" required>
Array of storage-keys which should be proofed and included.
</ParamField>

<ParamField body="blockParameter" type="string" required>
Integer block number, or the string "latest", "earliest" or "pending".
</ParamField>

## Returns

<ResponseField name="result" type="object">
The proof object containing account and storage proofs.

<Expandable title="Proof object properties">
<ResponseField name="address" type="string">
The address of the account.
</ResponseField>

<ResponseField name="accountProof" type="array">
Array of rlp-serialized MerkleTree-Nodes, starting with the stateRoot-Node.
</ResponseField>

<ResponseField name="balance" type="string">
The balance of the account.
</ResponseField>

<ResponseField name="codeHash" type="string">
Hash of the code of the account.
</ResponseField>

<ResponseField name="nonce" type="string">
Nonce of the account.
</ResponseField>

<ResponseField name="storageHash" type="string">
SHA3 of the StorageRoot.
</ResponseField>

<ResponseField name="storageProof" type="array">
Array of storage proof objects.

<Expandable title="Storage proof properties">
<ResponseField name="key" type="string">
The requested storage key.
</ResponseField>

<ResponseField name="value" type="string">
The storage value.
</ResponseField>

<ResponseField name="proof" type="array">
Array of rlp-serialized MerkleTree-Nodes.
</ResponseField>
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getProof",
  "params": [
    "******************************************",
    ["0x0000000000000000000000000000000000000000000000000000000000000000"],
    "latest"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "address": "******************************************",
    "accountProof": [
      "0xf90211a0...",
      "0xf90211a0..."
    ],
    "balance": "0x0",
    "codeHash": "0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470",
    "nonce": "0x0",
    "storageHash": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421",
    "storageProof": [
      {
        "key": "0x0000000000000000000000000000000000000000000000000000000000000000",
        "value": "0x0",
        "proof": [
          "0xf90211a0..."
        ]
      }
    ]
  }
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid parameters             | Invalid address, storage keys, or block parameter |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Info>
This method is useful for verifying account states and storage values using Merkle proofs without trusting the provider.
</Info>
