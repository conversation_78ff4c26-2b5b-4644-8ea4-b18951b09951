---
title: "eth_signTypedData_v4"
description: "Sign structured data according to EIP-712"
---

Defined in [EIP-712](https://eips.ethereum.org/EIPS/eip-712)

<Info>
Signs structured data according to EIP-712, which provides a more secure way to sign data by providing context and structure to the data being signed.
</Info>

## Parameters

<ParamField body="address" type="string" required>
The address that should sign the data (20 bytes).
</ParamField>

<ParamField body="typedData" type="object" required>
The typed structured data to be signed.

<Expandable title="Typed data object properties">
<ParamField body="types" type="object" required>
An object containing type definitions for the structured data.
</ParamField>

<ParamField body="primaryType" type="string" required>
The primary type from the types object that will be signed.
</ParamField>

<ParamField body="domain" type="object" required>
The domain separator as defined by EIP-712.

<Expandable title="Domain properties">
<ParamField body="name" type="string">
The user-readable name of the signing domain.
</ParamField>

<ParamField body="version" type="string">
The current major version of the signing domain.
</ParamField>

<ParamField body="chainId" type="number">
The chain ID of the network.
</ParamField>

<ParamField body="verifyingContract" type="string">
The address of the contract that will verify the signature.
</ParamField>

<ParamField body="salt" type="string">
A disambiguating salt for the protocol.
</ParamField>
</Expandable>
</ParamField>

<ParamField body="message" type="object" required>
The data object to be signed according to the primary type.
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="string">
The signature as a hexadecimal string.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_signTypedData_v4",
  "params": [
    "******************************************",
    {
      "types": {
        "EIP712Domain": [
          { "name": "name", "type": "string" },
          { "name": "version", "type": "string" },
          { "name": "chainId", "type": "uint256" },
          { "name": "verifyingContract", "type": "address" }
        ],
        "Person": [
          { "name": "name", "type": "string" },
          { "name": "wallet", "type": "address" }
        ]
      },
      "primaryType": "Person",
      "domain": {
        "name": "Example App",
        "version": "1",
        "chainId": 1,
        "verifyingContract": "******************************************"
      },
      "message": {
        "name": "Alice",
        "wallet": "******************************************"
      }
    }
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x4355c47d63924e8a72e509b65029052eb6c299d53a04e167c5775fd466751c9d07299936d304c153f6443dfa05f40ff007d72911b6f72307f996231605b915621c"
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4001 | User rejected the request      | User denied the signing request |
| 4100 | Requested method not supported | The method is not supported by the wallet |
| -32602 | Invalid params                 | Invalid typed data structure |

<Warning>
Always validate the domain and verifying contract address to prevent signature replay attacks.
</Warning>

<Info>
EIP-712 signatures provide better security than personal_sign by giving users context about what they're signing.
</Info>
