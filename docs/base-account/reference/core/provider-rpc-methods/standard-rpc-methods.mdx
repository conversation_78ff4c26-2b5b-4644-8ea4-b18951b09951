---
title: "Standard RPC Methods"
description: "Standard Ethereum RPC methods supported by Base Account"
---

Base Account supports all standard Ethereum RPC methods, ensuring compatibility with existing web3 applications and libraries. Each method below links to detailed documentation including parameters, returns, and error handling.

## Account Methods

### [eth_accounts](/base-account/reference/core/provider-rpc-methods/eth_accounts)

Returns a list of addresses owned by the client.

### [eth_requestAccounts](/base-account/reference/core/provider-rpc-methods/eth_requestAccounts)

Requests that the user provide an Ethereum address to be identified by. This method is used to initiate a connection between your application and the user's wallet.

## Chain Information

### [eth_chainId](/base-account/reference/core/provider-rpc-methods/eth_chainId)

Returns the chain ID of the current network.

### [eth_blockNumber](/base-account/reference/core/provider-rpc-methods/eth_blockNumber)

Returns the number of the most recent block.

### [eth_coinbase](/base-account/reference/core/provider-rpc-methods/eth_coinbase)

Returns the client coinbase address.

## Balance and Transaction Data

### [eth_getBalance](/base-account/reference/core/provider-rpc-methods/eth_getBalance)

Returns the balance of the account of given address.

### [eth_getTransactionCount](/base-account/reference/core/provider-rpc-methods/eth_getTransactionCount)

Returns the number of transactions sent from an address.

### [eth_getTransactionByHash](/base-account/reference/core/provider-rpc-methods/eth_getTransactionByHash)

Returns information about a transaction by transaction hash.

### [eth_getTransactionReceipt](/base-account/reference/core/provider-rpc-methods/eth_getTransactionReceipt)

Returns the receipt of a transaction by transaction hash.

## Block Information

### [eth_getBlockByNumber](/base-account/reference/core/provider-rpc-methods/eth_getBlockByNumber)

Returns information about a block by block number.

### [eth_getBlockByHash](/base-account/reference/core/provider-rpc-methods/eth_getBlockByHash)

Returns information about a block by block hash.

### [eth_getBlockTransactionCountByNumber](/base-account/reference/core/provider-rpc-methods/eth_getBlockTransactionCountByNumber)

Returns the number of transactions in a block by block number.

### [eth_getBlockTransactionCountByHash](/base-account/reference/core/provider-rpc-methods/eth_getBlockTransactionCountByHash)

Returns the number of transactions in a block by block hash.

## Transaction Methods

### [eth_sendTransaction](/base-account/reference/core/provider-rpc-methods/eth_sendTransaction)

Creates new message call transaction or a contract creation for signed transactions.

### [eth_sendRawTransaction](/base-account/reference/core/provider-rpc-methods/eth_sendRawTransaction)

Creates new message call transaction or a contract creation for signed transactions.

## Gas and Fee Methods

### [eth_estimateGas](/base-account/reference/core/provider-rpc-methods/eth_estimateGas)

Generates and returns an estimate of how much gas is necessary to allow the transaction to complete.

### [eth_gasPrice](/base-account/reference/core/provider-rpc-methods/eth_gasPrice)

Returns the current price per gas in wei.

### [eth_feeHistory](/base-account/reference/core/provider-rpc-methods/eth_feeHistory)

Returns base fee per gas and transaction effective priority fee per gas history for the requested/supported block range.

## Contract and Storage Methods

### [eth_getCode](/base-account/reference/core/provider-rpc-methods/eth_getCode)

Returns code at a given address.

### [eth_getStorageAt](/base-account/reference/core/provider-rpc-methods/eth_getStorageAt)

Returns the value from a storage position at a given address.

### [eth_getLogs](/base-account/reference/core/provider-rpc-methods/eth_getLogs)

Returns an array of all logs matching a given filter object.

### [eth_getProof](/base-account/reference/core/provider-rpc-methods/eth_getProof)

Returns the account and storage values of the specified account including the Merkle-proof.

## Signing Methods

### [personal_sign](/base-account/reference/core/provider-rpc-methods/personal_sign)

Signs a message with the private key of the given account.

### [eth_signTypedData_v4](/base-account/reference/core/provider-rpc-methods/eth_signTypedData_v4)

Signs typed data according to EIP-712.

## Network Methods

### [wallet_addEthereumChain](/base-account/reference/core/provider-rpc-methods/wallet_addEthereumChain)

Adds an Ethereum chain to the wallet.

### [wallet_switchEthereumChain](/base-account/reference/core/provider-rpc-methods/wallet_switchEthereumChain)

Switches the wallet to the specified Ethereum chain.

### [wallet_watchAsset](/base-account/reference/core/provider-rpc-methods/wallet_watchAsset)

Requests that the user track the token in their wallet.

## Advanced Methods

### [eth_getTransactionByBlockHashAndIndex](/base-account/reference/core/provider-rpc-methods/eth_getTransactionByBlockHashAndIndex)

Returns information about a transaction by block hash and transaction index position.

### [eth_getTransactionByBlockNumberAndIndex](/base-account/reference/core/provider-rpc-methods/eth_getTransactionByBlockNumberAndIndex)

Returns information about a transaction by block number and transaction index position.

### [eth_getUncleCountByBlockHash](/base-account/reference/core/provider-rpc-methods/eth_getUncleCountByBlockHash)

Returns the number of uncles in a block by block hash.

### [eth_getUncleCountByBlockNumber](/base-account/reference/core/provider-rpc-methods/eth_getUncleCountByBlockNumber)

Returns the number of uncles in a block by block number.

### [web3_clientVersion](/base-account/reference/core/provider-rpc-methods/web3_clientVersion)

Returns the current client version.

## Error Handling

All RPC methods can throw errors. Common error codes include:

- `-32700`: Parse error
- `-32600`: Invalid request
- `-32601`: Method not found
- `-32602`: Invalid params
- `-32603`: Internal error
- `-32000`: Server error

```javascript
try {
  const result = await provider.request({
    method: 'eth_getBalance',
    params: ['0x1234...', 'latest']
  });
} catch (error) {
  console.error('RPC Error:', error.code, error.message);
}
```

## Best Practices

1. **Always handle errors**: RPC calls can fail for various reasons
2. **Use appropriate block parameters**: `latest`, `earliest`, `pending`, or specific block numbers
3. **Validate addresses**: Ensure addresses are properly formatted
4. **Cache responses**: Some data doesn't change frequently
5. **Use batch requests**: When supported, batch multiple calls for efficiency

## Compatibility

Base Account maintains full compatibility with:
- Web3.js
- Ethers.js
- Wagmi
- Viem
- Other standard web3 libraries

All standard RPC methods work exactly as expected, ensuring your existing applications can integrate with Base Account without modification.