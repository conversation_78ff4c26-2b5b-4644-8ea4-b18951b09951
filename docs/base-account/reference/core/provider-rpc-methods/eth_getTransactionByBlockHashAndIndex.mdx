---
title: "eth_getTransactionByBlockHashAndIndex"
description: "Get a transaction by block hash and transaction index position"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns information about a transaction by block hash and transaction index position.
</Info>

## Parameters

<ParamField body="blockHash" type="string" required>
Hash of a block (32 bytes).
</ParamField>

<ParamField body="transactionIndex" type="string" required>
Integer of the transaction index position as a hexadecimal string.
</ParamField>

## Returns

<ResponseField name="result" type="object">
A transaction object, or null when no transaction was found.

<Expandable title="Transaction object properties">
<ResponseField name="blockHash" type="string">
Hash of the block where this transaction was in.
</ResponseField>

<ResponseField name="blockNumber" type="string">
Block number where this transaction was in.
</ResponseField>

<ResponseField name="from" type="string">
Address of the sender.
</ResponseField>

<ResponseField name="gas" type="string">
Gas provided by the sender.
</ResponseField>

<ResponseField name="gasPrice" type="string">
Gas price provided by the sender in Wei.
</ResponseField>

<ResponseField name="hash" type="string">
Hash of the transaction.
</ResponseField>

<ResponseField name="input" type="string">
The data sent along with the transaction.
</ResponseField>

<ResponseField name="nonce" type="string">
The number of transactions made by the sender prior to this one.
</ResponseField>

<ResponseField name="to" type="string">
Address of the receiver. null when it's a contract creation transaction.
</ResponseField>

<ResponseField name="transactionIndex" type="string">
Integer of the transaction index position in the block.
</ResponseField>

<ResponseField name="value" type="string">
Value transferred in Wei.
</ResponseField>

<ResponseField name="v" type="string">
ECDSA recovery id.
</ResponseField>

<ResponseField name="r" type="string">
ECDSA signature r.
</ResponseField>

<ResponseField name="s" type="string">
ECDSA signature s.
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getTransactionByBlockHashAndIndex",
  "params": [
    "0xc6ef2fc5426d6ad6fd9e2a26abeab0aa2411b7ab17f30a99d3cb96aed1d1055b",
    "0x0"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "blockHash": "0xc6ef2fc5426d6ad6fd9e2a26abeab0aa2411b7ab17f30a99d3cb96aed1d1055b",
    "blockNumber": "0xb",
    "from": "******************************************",
    "gas": "0x76c0",
    "gasPrice": "0x9184e72a000",
    "hash": "0xb903239f8543d04b5dc1ba6579132b143087c68db1b2168786408fcbce568238",
    "input": "0x",
    "nonce": "0x1",
    "to": "0x85h43d8a49eeb85d32cf465507dd71d507100c1",
    "transactionIndex": "0x0",
    "value": "0x2710",
    "v": "0x25",
    "r": "0xc9cf86333bcb065d140032ecaab5d9281bde80f21b9687b3e94161de42d51895",
    "s": "0x727a108a0b8d101465414033c3f705a9c7b826e596766046ee1183dbc8aeaa68"
  }
}
```

```json Not Found
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": null
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid parameters             | Invalid block hash or transaction index |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Info>
Transaction indices start at 0x0 for the first transaction in a block. If the index exceeds the number of transactions in the block, null is returned.
</Info>

```

```
