---
title: "coinbase_fetchPermissions"
description: "Retrieve permissions associated with a specific account and chain, optionally filtered by spender"
---

Coinbase-specific RPC method

<Info>
Retrieves permissions associated with a specific account and chain. If a spender is provided, returns only permissions for that spender. Otherwise, returns all permissions for the account. This method excludes permissions that have expired or been revoked, returning only active spend permissions.
</Info>

## Parameters

<ParamField body="account" type="string" required>
The address of the account whose permissions are being queried.
</ParamField>

<ParamField body="chainId" type="string" required>
The ID of the blockchain, in hexadecimal format.
</ParamField>

<ParamField body="spender" type="string">
Optional. The entity granted with the permission to spend the account's funds. If not provided, returns all permissions for the account.
</ParamField>

<ParamField body="pageOptions" type="object">
Optional pagination settings for the request.

<Expandable title="PageOptions properties">
<ParamField body="pageSize" type="number">
The number of permissions to fetch in a single request. Defaults to 50.
</ParamField>

<ParamField body="cursor" type="string">
A unique identifier to start fetching from a specific page.
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="object">
The permissions response object.

<Expandable title="FetchPermissionsResult properties">
<ResponseField name="permissions" type="array">
An array of permission objects.
</ResponseField>

<ResponseField name="pageDescription" type="object">
Pagination information for the response.
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```json Request with spender
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "coinbase_fetchPermissions",
  "params": [{
    "account": "******************************************",
    "chainId": "0x14A34",
    "spender": "******************************************"
  }]
}
```

```json Request without spender (all permissions)
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "coinbase_fetchPermissions",
  "params": [{
    "account": "******************************************",
    "chainId": "0x14A34"
  }]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "permissions": [
      {
        "createdAt": **********,
        "permissionHash": "0xabc123...",
        "signature": "0xdef456...",
        "spendPermission": {
          "account": "******************************************",
          "spender": "******************************************",
          "token": "******************************************",
          "allowance": "1000000000000000000",
          "period": 86400,
          "start": **********,
          "end": **********,
          "salt": "12345678901234567890",
          "extraData": "0x"
        }
      }
    ],
    "pageDescription": {
      "pageSize": 1,
      "nextCursor": "abc123next"
    }
  }
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4001 | User rejected the request      | User denied the permission request |
| 4100 | Requested method not supported | The method is not supported by the wallet |
| -32602 | Invalid params                 | Invalid account, chainId, or spender parameters |

<Warning>
Ensure the `chainId` and `account` parameters are correctly formatted and valid for the blockchain you are querying. If provided, the `spender` parameter must also be a valid address.
</Warning>
