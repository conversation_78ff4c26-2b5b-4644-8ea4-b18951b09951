---
title: "eth_coinbase"
description: "Get the client coinbase address"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the client coinbase address.
</Info>

## Parameters

This method does not accept any parameters.

## Returns

<ResponseField name="result" type="string">
The current coinbase address (20 bytes).
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_coinbase",
  "params": []
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "******************************************"
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4100 | Requested method not supported | The method is not supported by the wallet |
