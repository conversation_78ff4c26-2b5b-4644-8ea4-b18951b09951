---
title: "eth_chainId"
description: "Get the currently configured chain ID"
---

Defined in [EIP-695](https://github.com/ethereum/EIPs/blob/master/EIPS/eip-695.md)

<Info>
Returns the currently configured chain ID, a value used in replay-protected transaction signing as introduced by EIP-155.
</Info>

## Parameters

This method does not accept any parameters.

## Returns

<ResponseField name="result" type="string">
A hexadecimal string representing the chain ID.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_chainId",
  "params": []
}
```
</RequestExample>

<ResponseExample>
```json Ethereum Mainnet
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x1"
}
```

```json Base Mainnet
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x2105"
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4100 | Requested method not supported | The method is not supported by the wallet |

<Info>
Common chain IDs: Ethereum Mainnet (0x1), Base Mainnet (0x2105), Base Sepolia (0x14a34).
</Info>
