---
title: "eth_sendRawTransaction"
description: "Submit a signed transaction to the network"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Submits a pre-signed transaction for broadcast to the Ethereum network.
</Info>

## Parameters

<ParamField body="signedTransactionData" type="string" required>
The signed transaction data as a hexadecimal string.
</ParamField>

## Returns

<ResponseField name="result" type="string">
The transaction hash (32 bytes) as a hexadecimal string, or the zero hash if the transaction is not yet available.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_sendRawTransaction",
  "params": ["0xd46e8dd67c5d32be8d24c6b0afe7c5c3f4e9c3b2dae18d0c6b0cf5c8f3e8b2c1"]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0xe670ec64341771606e55d6b4ca35a1a6b75ee3d5145a99d05921026d1527331"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid params                 | Invalid transaction data format |
| -32000 | Insufficient funds             | Account doesn't have enough balance |
| 4001   | User rejected the request      | User denied the transaction request |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Warning>
Ensure the transaction is properly signed before submitting. Invalid signatures will result in transaction failure.
</Warning>
