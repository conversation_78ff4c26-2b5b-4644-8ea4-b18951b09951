---
title: "wallet_connect"
description: "Connect wallet and request account access"
---

Custom Coinbase Wallet method for establishing connection

<Info>
Requests that the wallet connects to the dApp and provides account access. This is similar to `eth_requestAccounts` but provides additional connection features.
</Info>

## Parameters

<ParamField body="options" type="object">
Optional configuration object for the connection.

<Expandable title="Options properties">
<ParamField body="version" type="string">
The wallet connect version to use.
</ParamField>

<ParamField body="jsonrpc" type="string">
The JSON-RPC version (typically "2.0").
</ParamField>

<ParamField body="capabilities" type="object">
Optional capabilities to request during connection, such as signInWithEthereum for authentication.

<Expandable title="Available capabilities">
<ParamField body="signInWithEthereum" type="object">
Request SIWE (Sign-In With Ethereum) authentication during connection.

<Expandable title="signInWithEthereum properties">
<ParamField body="nonce" type="string" required>
A unique random string to prevent replay attacks.
</ParamField>

<ParamField body="chainId" type="string" required>
The chain ID as a hexadecimal string (e.g., "0x2105" for Base Mainnet).
</ParamField>
</Expandable>
</ParamField>
</Expandable>
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="object">
Connection result object containing account information and capabilities results.

<Expandable title="Result properties">
<ResponseField name="accounts" type="array">
Array of connected account objects.

<Expandable title="Account object properties">
<ResponseField name="address" type="string">
The account address.
</ResponseField>

<ResponseField name="capabilities" type="object">
Capabilities results if requested during connection.

<Expandable title="Capabilities results">
<ResponseField name="signInWithEthereum" type="object">
SIWE authentication result if requested.

<Expandable title="signInWithEthereum result">
<ResponseField name="message" type="string">
The SIWE-formatted message that was signed.
</ResponseField>

<ResponseField name="signature" type="string">
The cryptographic signature of the message.
</ResponseField>
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="chainId" type="string">
The current chain ID as a hexadecimal string.
</ResponseField>

<ResponseField name="isConnected" type="boolean">
Whether the wallet is connected.
</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```json Basic Connection
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_connect",
  "params": [{}]
}
```

```json With Options
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_connect",
  "params": [{
    "version": "1.0",
    "jsonrpc": "2.0"
  }]
}
```

```json With signInWithEthereum Capability
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_connect",
  "params": [{
    "version": "1",
    "capabilities": {
      "signInWithEthereum": {
        "nonce": "abc123def456",
        "chainId": "0x2105"
      }
    }
  }]
}
```
</RequestExample>

<ResponseExample>
```json Basic Connection Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "accounts": [{
      "address": "******************************************"
    }],
    "chainId": "0x2105",
    "isConnected": true
  }
}
```

```json signInWithEthereum Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "accounts": [{
      "address": "******************************************",
      "capabilities": {
        "signInWithEthereum": {
          "message": "localhost:3000 wants you to sign in with your Ethereum account:\n******************************************\n\nSign in with Ethereum to the app.\n\nURI: http://localhost:3000\nVersion: 1\nChain ID: 8453\nNonce: abc123def456\nIssued At: 2024-01-15T10:30:00Z",
          "signature": "0x1234567890abcdef..."
        }
      }
    }],
    "chainId": "0x2105",
    "isConnected": true
  }
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4001 | User rejected the request      | User denied the connection request |
| 4100 | Requested method not supported | The method is not supported by the wallet |
| 4200 | Wallet not available           | The wallet is not installed or available |
| -32602 | Invalid params                 | Invalid nonce or chainId in signInWithEthereum capability |

<Warning>
This is a Coinbase Wallet-specific method and may not be available in other wallets.
</Warning>

<Info>
After successful connection, the wallet will emit connection events and provide access to account information.
</Info>

<Tip>
When using the `signInWithEthereum` capability, always generate a fresh, unique nonce for each authentication attempt to prevent replay attacks. The signature can be verified on your backend using libraries like viem.
</Tip>

## Usage with Capabilities

You can use the `wallet_connect` with the [`signInWithEthereum`](/base-account/reference/core/capabilities/signInWithEthereum.mdx) capability to authenticate the user.

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
