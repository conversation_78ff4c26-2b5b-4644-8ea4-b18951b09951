---
title: "eth_estimateGas"
description: "Estimate gas needed for a transaction"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Generates and returns an estimate of how much gas is necessary to allow the transaction to complete.
</Info>

## Parameters

<ParamField body="transaction" type="object" required>
The transaction call object.

<Expandable title="Transaction object properties">
<ParamField body="from" type="string">
The address the transaction is sent from (20 bytes).
</ParamField>

<ParamField body="to" type="string">
The address the transaction is directed to (20 bytes).
</ParamField>

<ParamField body="gas" type="string">
Integer of the gas provided for the transaction execution.
</ParamField>

<ParamField body="gasPrice" type="string">
Integer of the gasPrice used for each paid gas.
</ParamField>

<ParamField body="value" type="string">
Integer of the value sent with this transaction.
</ParamField>

<ParamField body="data" type="string">
Hash of the method signature and encoded parameters.
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="string">
The amount of gas used as a hexadecimal string.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_estimateGas",
  "params": [{
    "from": "******************************************",
    "to": "******************************************",
    "gas": "0x76c0",
    "gasPrice": "0x9184e72a000",
    "value": "0x9184e72a",
    "data": "******************************************"
  }]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x5208"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid params                 | Invalid transaction parameters |
| -32000 | Execution reverted             | Transaction would revert during execution |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Info>
The estimate may not be accurate if the blockchain state changes between the estimation and actual execution.
</Info>
