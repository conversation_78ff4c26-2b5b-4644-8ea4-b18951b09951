---
title: "wallet_getCapabilities"
description: "Get the wallet's supported capabilities for the given account"
---

Defined in [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792)

<Info>
Returns the wallet's capabilities for a given account. Capabilities indicate what additional functionality the wallet supports beyond standard RPC methods, such as atomic batch transactions, gasless transactions, auxiliary funds, and authentication features.
</Info>

## Parameters

<ParamField body="account" type="string" required>
The account address to check capabilities for.
Pattern: `^0x[0-9a-fA-F]{40}$`
</ParamField>

## Returns

<ResponseField name="result" type="object">
An object where each key is a chain ID (as a hexadecimal string) and each value is an object containing the capabilities supported on that chain.

<Expandable title="Capabilities by chain">
<ResponseField name="[chainId]" type="object">
Capabilities object for a specific chain (e.g., "0x2105" for Base Mainnet).

<Expandable title="Available capabilities">
<ResponseField name="auxiliaryFunds" type="object">
Indicates wallet access to funds beyond on-chain balance verification (MagicSpend).

<Expandable title="auxiliaryFunds properties">
<ResponseField name="supported" type="boolean">
Whether auxiliary funds are available for this account on this chain.
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="atomic" type="object">
Indicates support for atomic batch transaction execution.

<Expandable title="atomic properties">
<ResponseField name="supported" type="string">
Atomic execution support level: "supported", "ready", or "unsupported".
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="paymasterService" type="object">
Indicates support for gasless transactions via paymaster services.

<Expandable title="paymasterService properties">
<ResponseField name="supported" type="boolean">
Whether paymaster services are supported for this account on this chain.
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="flowControl" type="object">
Indicates support for flow control capabilities.

<Expandable title="flowControl properties">
<ResponseField name="supported" type="boolean">
Whether flow control is supported for this account on this chain.
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="datacallback" type="object">
Indicates support for data callback capabilities.

<Expandable title="datacallback properties">
<ResponseField name="supported" type="boolean">
Whether data callbacks are supported for this account on this chain.
</ResponseField>
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>
</Expandable>
</ResponseField>

## Example Usage

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_getCapabilities",
  "params": ["******************************************"]
}
```

```typescript SDK Usage
import { createBaseAccountSDK } from '@base-org/account';

const provider = createBaseAccountSDK().getProvider();

// Get capabilities for a user address
const capabilities = await provider.request({
  method: 'wallet_getCapabilities',
  params: ['******************************************']
});

// Check specific capabilities
const baseCapabilities = capabilities["0x2105"]; // Base Mainnet
const hasAuxiliaryFunds = baseCapabilities?.auxiliaryFunds?.supported;
const supportsAtomic = baseCapabilities?.atomic?.supported === "supported";
const hasPaymaster = baseCapabilities?.paymasterService?.supported;

console.log('Capabilities:', {
  hasAuxiliaryFunds,
  supportsAtomic,
  hasPaymaster
});
```
</RequestExample>

<ResponseExample>
```json Full Capabilities Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "0x2105": {
      "auxiliaryFunds": {
        "supported": true
      },
      "atomic": {
        "supported": "supported"
      },
      "paymasterService": {
        "supported": true
      },
      "flowControl": {
        "supported": false
      },
      "datacallback": {
        "supported": false
      }
    },
    "0x14A34": {
      "auxiliaryFunds": {
        "supported": false
      },
      "atomic": {
        "supported": "ready"
      },
      "paymasterService": {
        "supported": true
      }
    }
  }
}
```

```json Limited Capabilities Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "0x2105": {
      "auxiliaryFunds": {
        "supported": false
      },
      "atomic": {
        "supported": "unsupported"
      },
      "paymasterService": {
        "supported": false
      }
    }
  }
}
```
</ResponseExample>

## Capability Detection Patterns

### Check Single Capability

```typescript
async function checkAuxiliaryFunds(userAddress: string): Promise<boolean> {
  try {
    const capabilities = await provider.request({
      method: 'wallet_getCapabilities',
      params: [userAddress]
    });

    return capabilities["0x2105"]?.auxiliaryFunds?.supported || false;
  } catch (error) {
    console.error('Failed to check capabilities:', error);
    return false;
  }
}
```

### Check Multiple Capabilities

```typescript
async function getWalletCapabilities(userAddress: string) {
  const capabilities = await provider.request({
    method: 'wallet_getCapabilities',
    params: [userAddress]
  });

  const baseCapabilities = capabilities["0x2105"] || {};

  return {
    hasAuxiliaryFunds: baseCapabilities.auxiliaryFunds?.supported || false,
    hasAtomicBatch: baseCapabilities.atomic?.supported === "supported",
    hasPaymaster: !!baseCapabilities.paymasterService?.supported,
    hasFlowControl: !!baseCapabilities.flowControl?.supported,
    hasDataCallback: !!baseCapabilities.datacallback?.supported
  };
}
```

### Conditional Transaction Building

```typescript
async function buildTransaction(userAddress: string, calls: any[]) {
  const capabilities = await provider.request({
    method: 'wallet_getCapabilities',
    params: [userAddress]
  });

  const baseCapabilities = capabilities["0x2105"] || {};
  
  const txParams: any = {
    version: '1.0',
    chainId: '0x2105',
    from: userAddress,
    calls
  };

  // Add gasless capability if supported
  if (baseCapabilities.paymasterService?.supported) {
    txParams.capabilities = {
      paymasterService: {
        url: "https://paymaster.base.org/api/v1/sponsor"
      }
    };
  }

  // Use atomic execution if supported and multiple calls
  if (calls.length > 1 && baseCapabilities.atomic?.supported === "supported") {
    txParams.atomicRequired = true;
  }

  return txParams;
}
```

## Error Handling

| Code | Message | Description |
| ---- | ------- | ----------- |
| -32602 | Invalid params | Invalid account address format |
| 4100 | Method not supported | Wallet doesn't support wallet_getCapabilities |
| 4200 | Wallet not available | Wallet is not installed or available |

<Warning>
Capabilities are chain-specific and account-specific. Always check capabilities for the specific chain and account combination you're targeting.
</Warning>

<Info>
Not all wallets support all capabilities. Use capability detection to provide progressive enhancement rather than blocking functionality when capabilities aren't available.
</Info>

## Integration with Other Methods

### With wallet_sendCalls

Use capabilities to enhance transaction execution:

```typescript
const capabilities = await provider.request({
  method: 'wallet_getCapabilities',
  params: [userAddress]
});

// Use capabilities in wallet_sendCalls
const result = await provider.request({
  method: 'wallet_sendCalls',
  params: [{
    version: '1.0',
    chainId: '0x2105',
    from: userAddress,
    atomicRequired: capabilities["0x2105"]?.atomic?.supported === "supported",
    calls: [{
      to: '0x...',
      value: '0x0',
      data: '0x...'
    }],
    capabilities: {
      paymasterService: capabilities["0x2105"]?.paymasterService?.supported ? {
        url: "https://paymaster.base.org/api/v1/sponsor"
      } : undefined
    }
  }]
});
```

### With wallet_connect

Capabilities detection doesn't affect wallet_connect, but you can use the results to inform your authentication flow:

```typescript
const capabilities = await provider.request({
  method: 'wallet_getCapabilities',
  params: [userAddress]
});

// signInWithEthereum is always available with wallet_connect
const { accounts } = await provider.request({
  method: 'wallet_connect',
  params: [{
    version: '1',
    capabilities: {
      signInWithEthereum: { 
        nonce: generateNonce(), 
        chainId: '0x2105'
      }
    }
  }]
});
```

## Best Practices

1. **Always Check First**: Call `wallet_getCapabilities` before using advanced features
2. **Cache Results**: Capabilities typically don't change frequently, consider caching
3. **Graceful Fallbacks**: Implement fallback behavior when capabilities aren't supported
4. **Chain-Specific**: Check capabilities for each chain your app supports
5. **Progressive Enhancement**: Use capabilities to enhance UX, not gate basic functionality

## Related Documentation

- [Capabilities Overview](/base-account/reference/core/capabilities/overview) - Complete guide to using capabilities
- [wallet_sendCalls](/base-account/reference/core/provider-rpc-methods/wallet_sendCalls) - Execute transactions with capabilities
- [wallet_connect](/base-account/reference/core/provider-rpc-methods/wallet_connect) - Connect with authentication capabilities

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
