---
title: "web3_clientVersion"
description: "Get the current client version"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the current client version.
</Info>

## Parameters

This method does not accept any parameters.

## Returns

<ResponseField name="result" type="string">
The current client version string.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "web3_clientVersion",
  "params": []
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "CoinbaseWallet/4.0.0"
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4100 | Requested method not supported | The method is not supported by the wallet |
