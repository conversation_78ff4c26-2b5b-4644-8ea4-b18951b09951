---
title: "eth_blockNumber"
description: "Get the number of the most recent block"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the number of most recent block.
</Info>

## Parameters

This method does not accept any parameters.

## Returns

<ResponseField name="result" type="string">
A hexadecimal string representing the integer of the current block number the client is on.
</ResponseField>

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_blockNumber",
  "params": []
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x4b7"
}
```
</ResponseExample>

## Error Handling

| Code | Message                        | Description |
| ---- | ------------------------------ | ----------- |
| 4100 | Requested method not supported | The method is not supported by the wallet |

<Info>
The result is the block number in hexadecimal format. Convert to decimal to get the actual block number.
</Info>
