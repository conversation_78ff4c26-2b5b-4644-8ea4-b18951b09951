---
title: "eth_getUncleCountByBlockNumber"
description: "Get the number of uncles in a block by block number"
---

Defined in the [Ethereum JSON-RPC Specification](https://ethereum.org/en/developers/docs/apis/json-rpc/)

<Info>
Returns the number of uncles in a block by block number.
</Info>

## Parameters

<ParamField body="blockParameter" type="string" required>
Integer block number, or the string "latest", "earliest" or "pending".
</ParamField>

## Returns

<ResponseField name="result" type="string">
The number of uncles in this block as a hexadecimal string.
</ResponseField>

<RequestExample>
```json Latest Block
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getUncleCountByBlockNumber",
  "params": [
    "latest"
  ]
}
```

```json Specific Block
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "eth_getUncleCountByBlockNumber",
  "params": [
    "0x1b4"
  ]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x1"
}
```

```json No Uncles
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": "0x0"
}
```
</ResponseExample>

## Error Handling

| Code   | Message                        | Description |
| ------ | ------------------------------ | ----------- |
| -32602 | Invalid block parameter        | The provided block parameter is invalid |
| 4100   | Requested method not supported | The method is not supported by the wallet |

<Info>
Uncle blocks are blocks that were mined but not included in the main blockchain. This method returns their count for a specific block number.
</Info>

```
