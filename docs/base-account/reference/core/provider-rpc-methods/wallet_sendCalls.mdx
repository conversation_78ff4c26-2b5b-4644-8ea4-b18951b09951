---
title: "wallet_sendCalls"
description: "Submit a batch of calls to the wallet for execution"
---

Defined in [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792)

<Info>
Requests that the wallet submits a batch of calls. This method allows applications to send multiple transactions atomically or sequentially.
</Info>

## Parameters

<ParamField body="version" type="string" required>
The version of the API format. This must be "2.0.0".
</ParamField>

<ParamField body="id" type="string">
The ID of the batch of calls for tracking purposes.
</ParamField>

<ParamField body="from" type="string" required>
The sender's address.
Pattern: `^0x[0-9a-fA-F]{40}$`
</ParamField>

<ParamField body="chainId" type="string" required>
The EIP-155 chain ID of the calls. This must match the currently selected network in the wallet.
Pattern: `^0x([1-9a-f]+[0-9a-f]*|0)$`
</ParamField>

<Param<PERSON>ield body="atomicRequired" type="boolean" required>
`true` if the wallet must execute all calls atomically. If `false`, the wallet may execute the calls sequentially without atomicity. If `false` and the wallet is capable of executing the calls atomically, it may do so.
</ParamField>

<ParamField body="calls" type="array" required>
An array of call objects to execute.

<Expandable title="Call object structure">
<ParamField body="to" type="string" required>
The recipient address for the call.
</ParamField>

<ParamField body="value" type="string" required>
The value to send with the call (in wei, hex format).
</ParamField>

<ParamField body="data" type="string">
The call data (optional, hex format).
</ParamField>
</Expandable>
</ParamField>

<ParamField body="capabilities" type="object">
Dapps can use this object to communicate with the wallet about supported capabilities.
</ParamField>

## Returns

<ResponseField name="result" type="object">
An object containing information about the sent batch, including transaction details and status.
</ResponseField>

## Example Usage

<RequestExample>
```json Request
{
  "id": 1,
  "jsonrpc": "2.0",
  "method": "wallet_sendCalls",
  "params": [{
    "version": "2.0.0",
    "from": "******************************************",
    "chainId": "0xaa36a7",
    "atomicRequired": true,
    "calls": [
      {
        "to": "******************************************",
        "value": "0x0"
      },
      {
        "to": "******************************************",
        "value": "0x0"
      }
    ]
  }]
}
```
</RequestExample>

<ResponseExample>
```json Success Response
{
  "id": 1,
  "jsonrpc": "2.0",
  "result": {
    "batchId": "0x123...",
    "status": "pending"
  }
}
```
</ResponseExample>

## Error Handling

<ResponseField name="code" type="number">
Error code indicating the type of error that occurred.
</ResponseField>

<ResponseField name="message" type="string">
Human-readable error message describing what went wrong.
</ResponseField>

| Code   | Message                                                                  | Description |
| ------ | ------------------------------------------------------------------------ | ----------- |
| -32602 | The wallet cannot parse the request                                      | Invalid request format |
| -32000 | Version not supported                                                    | API version not supported |
| 4001   | User rejected the request                                                | User denied the transaction |
| 4100   | The requested account and/or method has not been authorized by the user  | Authorization required |
| 5700   | The wallet does not support a capability that was not marked as optional | Missing capability |
| 5710   | EIP-7702 not supported on the specified chain ID                         | Chain not supported |
| 5720   | There is already a batch submitted with the specified batch ID           | Duplicate batch ID |
| 5740   | The batch is too large for the wallet to process                         | Batch size limit exceeded |
| 5750   | EIP-7702 upgrade rejected for this chain and account                     | Upgrade rejected |

<Warning>
Ensure that the `chainId` matches the currently selected network in the wallet to avoid transaction failures.
</Warning>

<Tip>
When `atomicRequired` is set to `false`, consider the implications of partial execution if some calls fail while others succeed.
</Tip>
