---
title: "Spend Permissions"
---

import { GithubRepoCard } from "/snippets/GithubRepoCard.mdx"

You can find the open-source contracts repository by clicking the button below:

<GithubRepoCard title="Spend Permissions" githubUrl="https://github.com/coinbase/spend-permissions" />

### Structs

#### `SpendPermission`

Defines the complete parameters of a spend permission.

<Warning>
  The fields of the `SpendPermission` structure must be strictly ordered as
  defined below.
</Warning>

| Field       | Type      | Description                                                                                |
| ----------- | --------- | ------------------------------------------------------------------------------------------ |
| `account`   | `address` | Smart account this spend permission is valid for.                                          |
| `spender`   | `address` | Entity that can spend `account`'s tokens.                                                  |
| `token`     | `address` | Token address (ERC-7528 native token address or ERC-20 contract).                          |
| `allowance` | `uint160` | Maximum allowed value to spend within each `period`.                                       |
| `period`    | `uint48`  | Time duration for resetting used `allowance` on a recurring basis (seconds).               |
| `start`     | `uint48`  | Timestamp this spend permission is valid starting at (unix seconds).                       |
| `end`       | `uint48`  | Timestamp this spend permission is valid until (unix seconds).                             |
| `salt`      | `uint256` | An arbitrary salt to differentiate unique spend permissions with otherwise identical data. |
| `extraData` | `bytes`   | Arbitrary data to include in the permission.                                               |

#### `PeriodSpend`

Describes the cumulative spend for the current active period.

| Field   | Type      | Description                              |
| ------- | --------- | ---------------------------------------- |
| `start` | `uint48`  | Start time of the period (unix seconds). |
| `end`   | `uint48`  | End time of the period (unix seconds).   |
| `spend` | `uint160` | Accumulated spend amount for period.     |

---

### Contract functions

#### `approve`

Approve a spend permission via a direct call from the `account`. Only callable by the `account` specified in the spend permission.

```solidity
function approve(SpendPermission calldata spendPermission) external;
```

---

#### `approveWithSignature`

Approve a spend permission via a signature from the `account` owner. Compatible with [ERC-6492](https://eips.ethereum.org/EIPS/eip-6492) signatures for automatic account creation if needed.

```solidity
function approveWithSignature(SpendPermission calldata spendPermission, bytes calldata signature) external;
```

---

#### `spend`

Spend tokens using a spend permission, transferring them from the `account` to the `spender`. Only callable by the `spender` specified in the permission.

```solidity
function spend(SpendPermission memory spendPermission, uint160 value) external;
```

---

#### `revoke`

Revoke a spend permission, permanently disabling its use. Only callable by the `account` specified in the spend permission.

```solidity
function revoke(SpendPermission calldata spendPermission) external;
```

---

#### `revokeAsSpender`

Revoke a spend permission, permanently disabling its use. Only callable by the `spender` specified in the spend permission.

```solidity
function revokeAsSpender(SpendPermission calldata spendPermission) external;
```

---

#### `getHash`

Generate a hash of a `SpendPermission` struct for signing, in accordance with [EIP-712](https://github.com/ethereum/EIPs/blob/master/EIPS/eip-712.md).

```solidity
function getHash(SpendPermission memory spendPermission) public view returns (bytes32);
```

---

#### `isApproved`

Check if a spend permission is approved, regardless of whether the current time is within the valid time range of the permission.

```solidity
function isApproved(SpendPermission memory spendPermission) public view returns (bool);
```

---

#### `isRevoked`

Check if a spend permission is revoked, regardless of whether the current time is within the valid time range of the permission.

```solidity
function isRevoked(SpendPermission memory spendPermission) public view returns (bool);
```

---

#### `isValid`

Check if a spend permission is approved and not revoked, regardless of whether the current time is within the valid time range of the permission.

```solidity
function isValid(SpendPermission memory spendPermission) public view returns (bool);
```

---

#### `getLastUpdatedPeriod`

Retrieve the `start`, `end`, and accumulated `spend` for the last updated period of a spend permission.

```solidity
function getLastUpdatedPeriod(SpendPermission memory spendPermission) public view returns (PeriodSpend memory);
```

---

#### `getCurrentPeriod`

Retrieve the `start`, `end`, and accumulated `spend` for the current period of a spend permission.
Reverts if the current time is outside the valid time range of the permission, but does not validate whether the
spend permission has been approved or revoked.

```solidity
function getCurrentPeriod(SpendPermission memory spendPermission) public view returns (PeriodSpend memory);
```
