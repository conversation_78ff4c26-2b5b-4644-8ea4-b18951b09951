---
title: "getPaymentStatus"
description: "Check the status of a payment transaction"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
The `getPaymentStatus` function allows you to check the status of a payment transaction after it has been submitted. Use this to track whether a payment has been completed, is still pending, or has failed.

**Try it out:** Test the `getPaymentStatus` function interactively in our [Base Pay SDK Playground](https://base.github.io/account-sdk/pay-playground).
</Info>

## Parameters

<ParamField body="id" type="string" required>
Transaction hash from the pay result that you want to check the status of.

**Pattern:** `^0x[0-9a-fA-F]{64}$`
</ParamField>

<ParamField body="testnet" type="boolean">
Must match the testnet setting used in the original pay call. Default: false
</ParamField>

## Returns

<ResponseField name="result" type="PaymentStatus">
Payment status information including current state and details.

<Expandable title="PaymentStatus properties">
<ResponseField name="status" type="string">
Current status of the payment.

**Possible values:**
- `"completed"`: Payment successfully processed and confirmed
- `"pending"`: Payment still being processed by the network  
- `"failed"`: Payment failed to process (funds not transferred)
- `"not_found"`: Transaction ID not found or invalid
</ResponseField>

<ResponseField name="id" type="string">
Original transaction hash that was queried.
</ResponseField>

<ResponseField name="message" type="string">
Human-readable status message explaining the current state.
</ResponseField>

<ResponseField name="sender" type="string">
Sender address (present for pending, completed, and failed statuses).
</ResponseField>

<ResponseField name="amount" type="string">
Amount that was sent (present for completed transactions).
</ResponseField>

<ResponseField name="recipient" type="string">
Address that received the payment (present for completed transactions).
</ResponseField>

<ResponseField name="error" type="string">
Error details (present for failed status).
</ResponseField>
</Expandable>
</ResponseField>


<RequestExample>
```typescript Basic Status Check
import { getPaymentStatus } from '@base-org/account';

const status = await getPaymentStatus({
  id: "0xabcd1234...",
  testnet: false
});

console.log("Payment status:", status.status);
```

```typescript Complete Payment Flow
import { pay, getPaymentStatus } from '@base-org/account';

try {
  // Send payment
  const payment = await pay({
    amount: "10.50",
    to: "0x1234567890123456789012345678901234567890"
  });
} catch (error) {
  console.error(`Payment failed: ${error.message}`);
}

try {
  // Check status
  const status = await getPaymentStatus({
    id: payment.id,
    testnet: false
  });
  
  console.log("Status:", status.status);
  catch (error) {
  console.error(`Get status Failed: ${error.message}`);
}
```
</RequestExample>

<ResponseExample>
```typescript Completed Payment
{
  status: "completed",
  id: "0xabcd1234...",
  message: "Payment completed successfully",
  sender: "0x742d35Cc4Bf53E0e6C42E5d9F0A8D2F6D8A8B7C9",
  amount: "10.50",
  recipient: "0x1234567890123456789012345678901234567890"
}
```

```typescript Pending Payment
{
  status: "pending",
  id: "0xabcd1234...",
  message: "Payment is being processed",
  sender: "0x742d35Cc4Bf53E0e6C42E5d9F0A8D2F6D8A8B7C9"
}
```

```typescript Failed Payment
{
  status: "failed",
  id: "0xabcd1234...",
  message: "Payment failed due to insufficient balance",
  sender: "0x742d35Cc4Bf53E0e6C42E5d9F0A8D2F6D8A8B7C9",
  error: "Insufficient balance"
}
```

```typescript Transaction Not Found
{
  status: "not_found",
  id: "0xabcd1234...",
  message: "Transaction not found"
}
```
</ResponseExample>

## Error Handling

The `getPaymentStatus` function can throw errors for:

- Invalid transaction ID format
- Network connection issues
- Transaction not found

Always wrap calls to `getPaymentStatus` in a try-catch block to handle these errors gracefully.

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />
