---
title: "pay"
description: "Send USDC payments on the Base network"
---

Defined in the [Base Account SDK](https://github.com/base/account-sdk)

<Info>
The `pay` function is the core method of Base Pay that lets your users send USDC (digital dollars) on the Base network. No crypto knowledge required - we handle all the complexity. **No fees for merchants or users.**

**Try it out:** Test the `pay` function interactively in our [Base Pay SDK Playground](https://base.github.io/account-sdk/pay-playground).
</Info>

## Parameters

<ParamField body="amount" type="string" required>
Amount of USDC to send (e.g., "10.50" or "0.01").
</ParamField>

<ParamField body="to" type="string" required>
Ethereum address to send USDC to (must start with 0x).

**Pattern:** `^0x[0-9a-fA-F]{40}$`
</ParamField>

<ParamField body="testnet" type="boolean">
Set to true to use Base Sepolia testnet instead of mainnet. Default: false
</ParamField>

<ParamField body="payerInfo" type="object">
Optional payer information configuration for data callbacks.

<Expandable title="PayerInfo properties">
<ParamField body="requests" type="array" required>
Array of information requests from the payer.

<Expandable title="InfoRequest properties">
<ParamField body="type" type="string" required>
The type of information being requested.

**Possible values:** `'email' | 'physicalAddress' | 'phoneNumber' | 'name' | 'onchainAddress'`
</ParamField>

<ParamField body="optional" type="boolean">
Whether this information is optional. Default: false
</ParamField>
</Expandable>
</ParamField>

<ParamField body="callbackURL" type="string">
Optional callback URL for server-side validation.
</ParamField>
</Expandable>
</ParamField>

## Returns

<ResponseField name="result" type="PayResult">
Payment result on success. The function throws an error on failure.

<Expandable title="Payment Success properties">
<ResponseField name="id" type="string">
Transaction hash - use this to check payment status.
</ResponseField>

<ResponseField name="amount" type="string">
Amount that was sent.
</ResponseField>

<ResponseField name="to" type="string">
Address that received the payment.
</ResponseField>

<ResponseField name="payerInfoResponses" type="object">
Optional responses from information requests.
</ResponseField>
</Expandable>
</ResponseField>

## Errors

The `pay` function throws an error when the payment fails. The error object contains a message explaining what went wrong.


<RequestExample>
```typescript Basic Payment
import { pay } from '@base-org/account';

try {
  const payment = await pay({
    amount: "10.50",
    to: "0x****************************************",
    testnet: false
  });
  console.log(`Payment sent! Transaction ID: ${payment.id}`);
} catch (error) {
  console.error(`Payment failed: ${error.message}`);
}
```

```typescript Payment with Data Collection
try {
  const payment = await pay({
    amount: "25.00",
    to: "0x****************************************",
    payerInfo: {
      requests: [
        { type: 'email', optional: false },
        { type: 'phoneNumber', optional: true },
        { type: 'physicalAddress', optional: true }
      ],
      callbackURL: "https://your-api.com/validate"
    }
  });
  
  console.log(`Payment sent! Transaction ID: ${payment.id}`);
  
  // Access collected user information
  if (payment.payerInfoResponses) {
    console.log('Email:', payment.payerInfoResponses.email);
    
    if (payment.payerInfoResponses.phoneNumber) {
      console.log('Phone:', payment.payerInfoResponses.phoneNumber.number);
      console.log('Country:', payment.payerInfoResponses.phoneNumber.country);
    }
    
    if (payment.payerInfoResponses.physicalAddress) {
      const address = payment.payerInfoResponses.physicalAddress;
      console.log('Address:', address.address1);
      console.log('City:', address.city);
      console.log('State:', address.state);
      console.log('Postal Code:', address.postalCode);
      console.log('Recipient Name:', `${address.name.firstName} ${address.name.familyName}`);
    }
  }
} catch (error) {
  console.error(`Payment failed: ${error.message}`);
}
```
</RequestExample>

<ResponseExample>
```typescript Basic Success Response
{
  id: "0xabcd1234...",
  amount: "10.50",
  to: "0x****************************************"
}
```

```typescript Success Response with Data Collection
{
  id: "0xabcd1234...",
  amount: "25.00",
  to: "0x****************************************",
  payerInfoResponses: {
    email: "<EMAIL>",
    phoneNumber: {
      number: "+**********",
      country: "US"
    },
    physicalAddress: {
      address1: "123 Main St",
      city: "San Francisco",
      state: "CA",
      postalCode: "94105",
      country: "US",
      name: {
        firstName: "John",
        familyName: "Doe"
      }
    }
  }
}
```

```typescript Error (thrown)
{
  "code": 4001,
  "message": "Request rejected",
  "stack": "Error: Request rejected\n    at getEthProviderError..."
}
```
</ResponseExample>

## Error Handling

The `pay` function throws errors instead of returning a result. Always wrap calls to `pay` in a try-catch block to handle errors gracefully:

```typescript
try {
  const payment = await pay({
    amount: "10.00",
    to: "0xRecipient"
  });
  // Payment succeeded, use payment.id for tracking
  console.log(`Payment sent! Transaction ID: ${payment.id}`);
} catch (error) {
  // Payment failed
  console.error(`Payment failed: ${error.message}`);
}
```

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />