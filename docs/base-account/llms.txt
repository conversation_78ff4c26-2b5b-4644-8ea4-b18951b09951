# https://docs.base.org/base-account/llms.txt

## Base Account Documentation

> Base Account is a passkey-secured, ERC-4337 smart wallet with one‑tap payments, spend permissions, and sub‑accounts—built for seamless multi‑chain apps.

## Introduction
- [What is Base Account?](https://docs.base.org/base-account/overview/what-is-base-account.md) — Core concepts and benefits

## Quickstart
- [Web (Next.js)](https://docs.base.org/base-account/quickstart/web.md) — Add Base Account to a web app
- [React Native Integration](https://docs.base.org/base-account/quickstart/mobile-integration.md) — Mobile setup and flows

## Guides
- [Authenticate Users](https://docs.base.org/base-account/guides/authenticate-users.md) — SIWE with ERC‑6492 signatures
- [Accept Payments](https://docs.base.org/base-account/guides/accept-payments.md) — One‑tap USDC payments with Base Pay

## Framework Integrations
- [Wagmi: Setup](https://docs.base.org/base-account/framework-integrations/wagmi/setup.md) — Configure connectors and chains
- [Privy: Setup](https://docs.base.org/base-account/framework-integrations/privy/setup.md) — Authentication + sub‑accounts

## Reference
- [Account SDK: createBaseAccount](https://docs.base.org/base-account/reference/core/createBaseAccount.md) — Initialize SDK and provider
- [Provider RPC: wallet_sendCalls](https://docs.base.org/base-account/reference/core/provider-rpc-methods/wallet_sendCalls.md) — Batch and sponsored calls

## More
- [Troubleshooting: Popups](https://docs.base.org/base-account/more/troubleshooting/usage-details/popups.md) — Browser settings and flows
- [Troubleshooting: Gas Usage](https://docs.base.org/base-account/more/troubleshooting/usage-details/gas-usage.md) — Cost breakdown and tips

## Basenames
- [Basenames FAQ](https://docs.base.org/base-account/basenames/basenames-faq.md) — Naming, transfer, and primary name setup

## Contribute
- [Contribute to Docs](https://docs.base.org/base-account/contribute/contribute-to-base-account-docs.md) — How to propose changes and fixes

## Optional
- [Base Gasless Campaign](https://docs.base.org/base-account/more/base-gasless-campaign.md) — Incentives for sponsored gas integrations
- [Telemetry](https://docs.base.org/base-account/more/telemetry.md) — Anonymous metrics and privacy details


