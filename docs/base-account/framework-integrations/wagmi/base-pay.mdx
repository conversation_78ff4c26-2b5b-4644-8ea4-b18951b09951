---
title: "Base Pay"
description: "Accept USDC payments with Base Pay in your Wagmi-powered React application"
---

Base Pay works the same way in Wagmi applications as it does anywhere else - it operates independently of wallet connections and uses the Base Account SDK directly.

## Implementation

Base Pay doesn't require any special Wagmi integration. Simply follow the [Accept Payments guide](/base-account/guides/accept-payments) - all the code examples work exactly the same in your Wagmi app.

The key points:

- **No wallet connection needed** - Base Pay handles everything through the SDK
- **Same API** - Use `pay()` and `getPaymentStatus()` exactly as shown in the main guide
- **Works alongside Wagmi** - You can display the user's connected address from `useAccount()` but it's not required for payments

## Quick Example

```tsx
import { pay } from '@base-org/account'
import { useAccount } from 'wagmi' // Optional - just for display

export function CheckoutButton() {
  const { address } = useAccount() // Optional

  const handlePayment = async () => {
    try {
      const payment = await pay({
        amount: '5.00',
        to: '0xYourAddress',
        testnet: true
      })
      console.log('Payment sent:', payment.id)
    } catch (error) {
      console.error('Payment failed:', error)
    }
  }

  return (
    <div>
      {address && <p>Connected: {address}</p>}
      <button onClick={handlePayment}>
        Pay $5.00 with Base Pay
      </button>
    </div>
  )
}
```

<Warning>
**Please Follow the Brand Guidelines** 

If you intend on using the `BasePayButton`, please follow the [Brand Guidelines](/base-account/reference/ui-elements/brand-guidelines) to ensure consistency across your application.

</Warning>

## Learn More

For complete implementation details, examples, and advanced features like collecting user information, see the main [Accept Payments guide](/base-account/guides/accept-payments).
