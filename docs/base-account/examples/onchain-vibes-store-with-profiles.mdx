---
title: 'Onchain Vibes Store'
description: 'How Onchain Vibes Store uses Coinbase Base Account Profiles for secure user data collection during onchain checkout.'
---

import {GithubRepoCard} from "/snippets/GithubRepoCard.mdx";

## Overview

Onchain Vibes Store demonstrates how to use [**Base Account Profiles**](/smart-wallet/concepts/features/optional/profiles)
to securely collect user information (like email and physical address)
during an onchain transaction. This feature enables seamless, privacy-first
data collection for e-commerce and other onchain apps.

## What Are Base Account Profiles?

Base Account Profiles allow your app to request personal information from users as part of a transaction. Supported data types include:

- Email address
- Phone number
- Physical address
- Name
- Onchain wallet address

Users are always in control: they see exactly what you request and can choose to share or withhold any information.

## How It Works in This App

1. **User clicks Buy**: The checkout UI lets users select what info to share (email, address).
2. **App requests data**: The app sends a transaction with a `dataCallback` capability, specifying the requested data and a callback URL for validation.
3. **Base Account prompts the user**: The wallet UI collects the requested info.
4. **Validation**: The wallet POSTs the data to your callback API for validation.
5. **Transaction proceeds**: If validation passes, the transaction completes and the app displays the collected info.

<div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
  <img
    alt="Onchain Vibes Store"
    src="https://i.imgur.com/rC674pl.gif"
    width="1000"
    loading="lazy"
  />
</div>
<div style={{ textAlign: 'center', marginTop: '0.5rem', fontStyle: 'italic' }}>
    Onchain Vibes Store Quick Demo
  </div>

## Skip ahead

If you want to skip ahead and just get the final code, you can find it here:

<GithubRepoCard title="Onchain Vibes Store" githubUrl="https://github.com/base/demos/tree/master/smart-wallet/onchain-vibes-store" />


## UI Walkthrough: CheckoutButton

The main logic lives in `src/components/CheckoutButton.tsx`:

```tsx src/components/CheckoutButton.tsx
const requests = [];
if (dataToRequest.email) requests.push({ type: "email", optional: false });
if (dataToRequest.address) requests.push({ type: "physicalAddress", optional: false });

const response: any = await provider?.request({
  method: "wallet_sendCalls",
  params: [{
    version: "1.0",
    chainId: numberToHex(84532), // Base Sepolia
    calls: [
      {
        to: "******************************************", // USDC contract
        data: encodeFunctionData({
          abi: erc20Abi,
          functionName: "transfer",
          args: [
            "******************************************",
            parseUnits("0.01", 6),
          ],
        }),
      },
    ],
    capabilities: {
      dataCallback: {
        requests,
        callbackURL: getCallbackURL(),
      },
    },
  }],
});
```

- The user selects which data to share (checkboxes for email/address).
- The app sends a transaction with a `dataCallback` capability.
- The callback URL is set to your API endpoint (must be HTTPS, e.g. via ngrok in dev).

---

## API Walkthrough: Data Validation

The callback API is implemented in `src/api/data-validation/route.ts`:

```ts src/api/data-validation/route.ts
export async function POST(request: NextRequest) {
  const requestData = await request.json();
  try {
    const email = requestData.requestedInfo.email;
    const physicalAddress = requestData.requestedInfo.physicalAddress;
    const errors: any = {};
    if (email && email.endsWith("@example.com")) {
      errors.email = "Example.com emails are not allowed";
    }
    if (physicalAddress) {
      if (physicalAddress.postalCode && physicalAddress.postalCode.length < 5) {
        if (!errors.physicalAddress) errors.physicalAddress = {};
        errors.physicalAddress.postalCode = "Invalid postal code";
      }
      if (physicalAddress.countryCode === "XY") {
        if (!errors.physicalAddress) errors.physicalAddress = {};
        errors.physicalAddress.countryCode = "We don't ship to this country";
      }
    }
    if (Object.keys(errors).length > 0) {
      return NextResponse.json({ errors });
    }
    // Return requestData wrapped in request object
    return NextResponse.json({ request: requestData });
  } catch (error) {
    return NextResponse.json({ errors: { server: "Server error validating data" } });
  }
}
```

- The API receives the user's data, validates it, and returns errors if needed.
- If validation passes, it must return the request data wrapped in a `request` object.
- If errors are returned, the wallet prompts the user to correct their info.

---

## Wagmi & Wallet Setup

The app uses Wagmi and the Coinbase Wallet connector, configured for Base Account and profiles:

```ts
import { coinbaseWallet } from "wagmi/connectors";
const cbWalletConnector = coinbaseWallet({
  appName: "Vibes Store",
  preference: {
    keysUrl: "https://keys.coinbase.com/connect",
    options: "smartWalletOnly",
  },
});
```

---

## Testing Locally

1. Start your dev server: `npm run dev`
2. Start ngrok: `ngrok http 3000`
3. Set the ngrok URL in the `CheckoutButton` component

```tsx
function getCallbackURL() {
    return "https://your-ngrok-url.ngrok-free.app/api/data-validation"
  }
```

4. Try a purchase and share profile data

---

## Best Practices

- **Only request what you need**: Ask for the minimum info required.
- **Explain why**: Tell users why you need each field.
- **Validate thoroughly**: Implement robust server-side validation.
- **Handle errors gracefully**: Show clear error messages.
- **Use HTTPS**: Your callback URL must be HTTPS (ngrok for local dev).

---

## Resources

- [Profiles Guide](/smart-wallet/guides/profiles)
- [Profiles Reference](/smart-wallet/technical-reference/profiles-reference)
- [Base Account Docs](/smart-wallet/concepts/what-is-smart-wallet)
- [Wagmi Docs](https://wagmi.sh/)
