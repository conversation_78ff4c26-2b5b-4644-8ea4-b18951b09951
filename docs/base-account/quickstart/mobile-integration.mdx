---
title: "Mobile (React Native)"
---

import { Danger } from "/snippets/danger.mdx";
import { GithubRepoCard } from "/snippets/GithubRepoCard.mdx"

This guide helps you add support for Base Account into a React Native app
by integrating the
[Mobile Wallet Protocol Client](https://www.npmjs.com/package/@mobile-wallet-protocol/client).

<Note>
  This doc is updated for Mobile Wallet Protocol Client `v1.0.0`
</Note>

<Danger>
  **Deep Link Handling**

  Breaking change in v1.0.0: Universal Links and App Links requirements are
  removed in favor of custom schemes (e.g. `myapp://`).
</Danger>

## Before You Start

This guide walks you through adding support for Base Account into an existing React Native app or starter project.

If you prefer to skip ahead and start with a working example, navigate to the repository below:

<GithubRepoCard title="Mobile Wallet Protocol Expo Example" githubUrl="https://github.com/MobileWalletProtocol/smart-wallet-expo-example" />


If you are looking to integrate Base Account into an existing React Native app or starter project, follow the instructions below.

## Step 1: Install Mobile Wallet Protocol Client

Add the latest version of [Mobile Wallet Protocol Client](https://mobilewalletprotocol.github.io/wallet-mobile-sdk/) to your project.

<CodeGroup>
```zsh npm
npm i @mobile-wallet-protocol/client@latest
```

```zsh yarn
yarn add @mobile-wallet-protocol/client@latest
```
</CodeGroup>

## Step 2: Add Polyfills

### Install peer dependencies

The Mobile Wallet Protocol Client library requires the [Expo WebBrowser](https://docs.expo.dev/versions/latest/sdk/webbrowser/) and [Async Storage](https://react-native-async-storage.github.io/async-storage/docs/install) packages to be installed.
Follow the instructions on the respective pages for any additional setup.

<CodeGroup>
```zsh npm
npm i expo expo-web-browser @react-native-async-storage/async-storage
```

```zsh yarn
yarn add expo expo-web-browser @react-native-async-storage/async-storage
```
</CodeGroup>

### Polyfills

Mobile Wallet Protocol Client requires `crypto.randomUUID`, `crypto.getRandomValues`, and `URL` to be polyfilled globally since they are not available in the React Native environment.

Below is an example of how to polyfill these functions in your app using the [expo-crypto](https://docs.expo.dev/versions/latest/sdk/crypto/) and [expo-standard-web-crypto](https://github.com/expo/expo/tree/master/packages/expo-standard-web-crypto/) packages.

<CodeGroup>
```zsh npm
npm i expo-crypto expo-standard-web-crypto react-native-url-polyfill
```

```zsh yarn
yarn add expo-crypto expo-standard-web-crypto react-native-url-polyfill
```
</CodeGroup>

<CodeGroup>
```js polyfills.js
import "react-native-url-polyfill/auto";
import { polyfillWebCrypto } from "expo-standard-web-crypto";
import { randomUUID } from "expo-crypto";

polyfillWebCrypto();
crypto.randomUUID = randomUUID;
```

```tsx App.tsx
import "./polyfills"; // import before @mobile-wallet-protocol/client

import { CoinbaseWalletSDK } from "@mobile-wallet-protocol/client";

/// ...
```
</CodeGroup>

## Step 3: Usage

Mobile Wallet Protocol Client provides 2 interfaces for mobile app to interact with the Base Account, an EIP-1193 compliant provider interface and a wagmi connector.

<Check>
  If your app is using wallet aggregator, go straight to [**Option 2: Wagmi
  Connector**](#option-2-wagmi-connector) for 1-line integration.
</Check>

### Option 1: EIP-1193 Provider

<Warning>
  The `app` prefix in SDK config params is removed in v1.0.0.
</Warning>

Create a new `EIP1193Provider` instance, which is EIP-1193 compliant.

```tsx App.tsx
import { EIP1193Provider } from "@mobile-wallet-protocol/client";

// Step 1. Initialize provider with your dapp's metadata and target wallet
const metadata = {
  name: "My App Name",
  customScheme: "myapp://", // only custom scheme (e.g. `myapp://`) is supported in v1.0.0
  chainIds: [8453],
  logoUrl: "https://example.com/logo.png",
};
const provider = new EIP1193Provider({
  metadata,
  wallet: Wallets.CoinbaseSmartWallet,
});

// ...

// 2. Use the provider
const addresses = await provider.request({ method: "eth_requestAccounts" });
const signedData = await provider.request({
  method: "personal_sign",
  params: ["0x48656c6c6f20776f726c6421", addresses[0]],
});
```

### Option 2: Wagmi Connector

Add the latest version of Mobile Wallet Protocol wagmi-connectors to your project.

<CodeGroup>
```zsh npm
npm i @mobile-wallet-protocol/wagmi-connectors@latest
```

```zsh yarn
yarn add @mobile-wallet-protocol/wagmi-connectors@latest
```
</CodeGroup>

Simply import the `createConnectorFromWallet` function and pass in the wallet you want to use to wagmi config.

```ts config.ts
import {
  createConnectorFromWallet,
  Wallets,
} from "@mobile-wallet-protocol/wagmi-connectors";

const metadata = {
  name: "My App Name",
  customScheme: "myapp://", // only custom scheme (e.g. `myapp://`) is supported in v1.0.0
  chainIds: [8453],
  logoUrl: "https://example.com/logo.png",
};

export const config = createConfig({
  chains: [base],
  connectors: [
    createConnectorFromWallet({
      metadata,
      wallet: Wallets.CoinbaseSmartWallet,
    }),
  ],
  transports: {
    [base.id]: http(),
  },
});
```

Then you can use wagmi's react interface to interact with the Base Account.

```tsx App.tsx
import { useConnect } from "wagmi";

// ...

const { connect, connectors } = useConnect();

return (
  <Button
    title={"Connect"}
    onPress={() => {
      connect({ connector: connectors[0] });
    }}
  />
);
```

## Give feedback!

Send us feedback on the [Base Discord](https://discord.com/invite/buildonbase/) or create a new issue on the [MobileWalletProtocol/react-native-client](https://github.com/MobileWalletProtocol/react-native-client/issues) repository.

