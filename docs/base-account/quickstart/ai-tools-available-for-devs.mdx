---
title: "AI Tools for Base Account Developers"
---


Base Account has a number of AI tools available for builders and developers.
We keep expanding the list of tools and features, so please check back soon for updates.

<CardGroup>
  <Card
    title="Base Builder MCP"
    href="https://github.com/base/base-builder-mcp"
    icon={<svg
      version="1.0"
      xmlns="http://www.w3.org/2000/svg"
      width="40px"
      height="40px"
      viewBox="0 0 200.000000 200.000000"
      preserveAspectRatio="xMidYMid meet"
    >
      <g
        transform="translate(-30.000000,200.000000) scale(0.100000,-0.100000)"
        fill="currentColor"
        stroke="none"
      >
        <path
          d="M1040 1781 c-66 -20 -118 -66 -433 -379 -354 -354 -363 -365 -321
    -406 36 -36 51 -24 394 319 360 360 379 375 457 360 79 -14 143 -87 143 -163
    0 -69 -27 -103 -285 -362 -260 -261 -274 -279 -239 -314 35 -36 54 -22 299
    225 132 133 260 254 284 270 134 87 306 -49 250 -198 -5 -15 -149 -167 -319
    -338 -210 -210 -314 -322 -325 -348 -26 -64 -13 -99 72 -188 41 -43 84 -81 94
    -84 30 -10 69 20 69 53 0 21 -16 44 -65 92 -36 35 -65 71 -65 80 0 8 138 152
    306 320 342 341 354 357 354 470 0 44 -7 80 -21 110 -44 98 -148 170 -246 170
    l-53 0 0 53 c0 168 -189 307 -350 258z"
        />
        <path
          d="M839 1314 c-295 -294 -314 -320 -312 -433 2 -203 215 -336 395 -248
    40 20 114 88 307 280 210 211 254 259 254 283 0 30 -25 54 -57 54 -13 0 -111
    -91 -276 -254 -140 -140 -265 -259 -277 -265 -12 -6 -44 -11 -71 -11 -90 0
    -162 72 -162 162 0 27 5 59 11 71 6 12 125 137 265 277 163 165 254 263 254
    276 0 27 -19 50 -49 59 -20 6 -53 -23 -282 -251z"
        />
      </g>
    </svg>}
  />
    
  <Card
    title="LLMs.txt File"
    href="/base-account/llms.txt"
    icon={<svg width="40px" height="40px" viewBox="0 0 56 56" xmlns="http://www.w3.org/2000/svg">
      <text
        x="32"
        y="34"
        fontFamily="monospace"
        fontSize="16"
        textAnchor="middle"
        dominantBaseline="middle"
        fontWeight="bold"
        letterSpacing="-1"
        fill="currentColor"
      >
        llms-txt
      </text>
    </svg>}
  />

  <Card
    title="Agent Kit"
    href="https://docs.cdp.coinbase.com/agentkit/docs/welcome"
    icon={<svg width="32px" height="32px" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="18" y="21" width="45" height="33" rx="4" fill="#474950" />
      <rect x="6" y="10" width="40" height="21" rx="4" fill="#676869" />
      <rect x="15" y="16" width="7" height="9" rx="3.5" fill="white" />
      <rect x="30" y="16" width="7" height="9" rx="3.5" fill="white" />
      <path
        d="M46 17H49C50.1046 17 51 17.8954 51 19V23C51 24.1046 50.1046 25 49 25H46V17Z"
        fill="white"
      />
      <path
        d="M6 25L3 25C1.89543 25 1 24.1046 1 23L1 19C1 17.8954 1.89543 17 3 17L6 17L6 25Z"
        fill="white"
      />
      <path
        d="M24.0874 48.349C24.0874 48.9438 24.5687 49.4251 25.1635 49.4251H39.7729C40.3677 49.4251 40.849 48.9438 40.849 48.349C40.849 47.7542 40.3677 47.2729 39.7729 47.2729H25.1635C24.5687 47.2729 24.0874 47.7542 24.0874 48.349Z"
        fill="white"
      />
      <path
        d="M24.3161 45.1342C24.5269 45.345 24.8019 45.4496 25.0768 45.4496C25.3518 45.4496 25.6268 45.345 25.8376 45.1342L28.5277 42.4441C28.9492 42.0241 28.9492 41.3426 28.5277 40.9226L25.9213 38.3161C25.4998 37.8946 24.8198 37.8946 24.3998 38.3161C23.9783 38.7361 23.9783 39.4176 24.3998 39.8376L26.2441 41.6818L24.3161 43.6113C23.8946 44.0313 23.8946 44.7128 24.3161 45.1328L24.3161 45.1342Z"
        fill="white"
      />
    </svg>}
  />
    
</CardGroup>

## Base Builder MCP

This repository is an [Model Context Protocol](https://modelcontextprotocol.io/introduction) server destined for Base Builders.
It contains a list of tools that you can give your AI coding assistant to help it build with Base Account

In particular, it allows your AI coding assistant to efficiently find the right guides that are relevant to the code you are writing.

[Base Builder MCP](https://github.com/base/base-builder-mcp)

## LLMs.txt File

This is a simple text file that contains the full context of our documentation for your LLMs.
It is a convenient and useful tool for your AI coding assistant to help it build with Base Account.

[LLMs.txt File](/smart-wallet/llms.txt)

## Agent Kit

This is a tool that allows you to build your AI agent using embedded Wallet APIs.
It is a great starting point for your AI agent projects with Base Account.

[Agent Kit](https://docs.cdp.coinbase.com/agentkit/docs/welcome)
