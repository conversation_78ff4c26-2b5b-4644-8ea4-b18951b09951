---
title: Data Driven Growth
description: Build with Purpose, Not Assumptions
---

## How Base.dev Powers Your Growth

Base.dev is the builder tool platform that helps developers build, grow, and earn through miniapps on Base. It provides real-time analytics and user behavior insights to optimize your app's performance and engagement, helping builders understand exactly how users interact with their miniapps and identify the features that drive retention and growth.

**Build** with tools like [Minikit](/mini-apps/technical-reference/minikit/overview) and [OnchainKit](/onchainkit/getting-started), plus educational resources like workshops, and livestreams.

**Grow** through real-time analytics that show how users interact with your app, along with ad credits, gas credits, and distribution in the Base App where users can discover your miniapp.

**Earn** with analytics that help you identify which features users value most, so you can focus development on what drives engagement and revenue. Additional monetization tools are coming soon.

### Importing your Mini App

<iframe 
  src="https://screen.studio/share/sr8W34EB" 
  title="Embedded content"
  className="w-full h-96 rounded-xl"
></iframe>

#### Step 1: Sign in to Base.dev

Sign up or sign in with an existing Base account.

#### Step 2: Add Base Builder Object

Import a mini app by adding the Base builder object into your manifest. This must be at the top level of your manifest file.

<Callout type="info">
For a complete example, see the [manifest documentation](/mini-apps/features/manifest).
</Callout>

```json
{
  "accountAssociation": { ... },
  "frame": { ... },
  "baseBuilder": {
    "allowedAddresses": ["0x..."]
  }
}
```

<Note>
The address you are allowing is the address you are signed up with on base.dev.
</Note>

#### Step 3: Deploy and Verify

Deploy the manifest again and check that your manifest now includes the Base builder object. Once confirmed, you can take the mini app URL and click import.

#### Next Steps

After importing, your mini app will be available in the Base.dev dashboard where you can access analytics and manage your app's growth.

## Available Analytics Data

Base.dev provides the following analytics data for your miniapp:

### User Engagement Metrics

#### Active & New Users

Number of Base App users who have opened your mini app all-time. New Users are those who opened it for the first time, while Active Users are those who have returned and opened it again. Track this to measure user retention and identify if your app encourages repeat usage.

#### Number of Opens

The total number of times users opened your application. Use this metric to understand overall app popularity and correlate with feature releases or marketing efforts.

#### Median Session Time

The average of all user session durations. Longer sessions typically indicate higher engagement with your content. Compare session times across different features to identify what keeps users engaged.

### Traffic Analysis

#### Entry Points

How users are entering your application. Shows traffic sources such as Base App Home. Optimize your presence on high-traffic entry points and investigate low-performing sources to improve discoverability.

### Data Access

All analytics data is available through the Base.dev dashboard in real-time. Metrics update as users interact with your miniapp within the Base App ecosystem.