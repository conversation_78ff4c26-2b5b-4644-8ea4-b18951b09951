---
title: Optimize Onboarding
description: Reduce friction with wallet‑optional flows and clear value moments
---

Optimize your onboarding flow to increase user engagement and retention. This guide outlines the best practices that will keep your users in-app and engaged.

### Overview

Deliver value instantly and avoid blocking actions.

- Make the first interaction instant and non-blocking
- Authenticate only when required for security purposes and defer prompts until necessary
- Prefer the built-in Base Account; only offer connect/switch for alternate wallets, never gating
- Use progressive disclosure tied to intent (buy, post, personalize)
- Keep users in-app with [SDK actions for links](/mini-apps/features/links); avoid fragile static urls

### Recommended onboarding flow

<Steps>
<Step title="First render">
- Show immediate value (demo content, sample state, or read-only mode)
- Personalize instantly with [`context`](/mini-apps/technical-reference/minikit/provider-and-initialization) of the user's profile to instantly personalize
- Display one clear CTA that leads to a meaningful action (e.g. "Post a message", "Buy a token", "Follow a user")
</Step>

<Step title="User initiates a protected action">
- Trigger Sign In with Farcaster (SIWF) / Quick Auth only when needed per [Authentication](/mini-apps/features/Authentication)
- For onchain actions, use the Base Account automatically. Eliminate explicit wallet connect flows
- Alternate wallets: offer a non-blocking connect/switch option without gating exploration
</Step>

<Step title="Celebrate and amplify">
- After success, prompt social actions via [SDK actions](/mini-apps/features/links) and [Sharing & Social Graph](/mini-apps/features/sharing-and-social-graph)
- Offer next step: save, follow, or share — optimize with [Search & Discovery](/mini-apps/features/search-and-discovery)
</Step>
</Steps>

### UX patterns that work

<Tip>
- Progressive prompts: ask only when needed (buy, post, personalize)
- Clear copy: explain why you’re asking ("Sign in to save your score")
</Tip>

- One-time deep link (Connect Account users): if SIWF requires a one-time Farcaster link to register auth address, message it as a quick, one-time setup and return the user seamlessly
- Friendly fallbacks: if auth is skipped or fails, allow continued browsing in read-only mode

### Authentication and wallet guidance

#### Authentication

- Only when your backend needs a verified user
- Use SIWF/Quick Auth to issue a session (JWT) when required

<Warning>
Do not treat Mini App context as primary auth (it can be spoofed)
</Warning>

Read more in [Authentication](/mini-apps/features/Authentication).

#### Wallets
- Base App provides an in-app Base Account. This should be the default wallet used by your app to streamline interactions.
- Do not show a connect button on first load
- If you support other wallets, show connect/switch as optional and non-blocking
- Use the OnchainKit Wallet component or Wagmi hooks as needed

### Do not use raw deeplinks

<Warning>
- Always use official SDK actions for cross-client compatibility (e.g., compose cast, view profile)
- This prevents dead ends and ensures consistent behavior across hosts
</Warning>

Learn how to implement them with [SDK actions](/mini-apps/features/links).

### Measure activation and iterate

<Info>
- Define activation as the first successful protected action (e.g., first post, first onchain action)
- Track funnel: first render → intent click → auth/wallet prompt → success → share/save
</Info>

- Break down Create Account vs Connect Account behavior to spot friction
- See: Base Build Analytics (coming soon)

### Implementation checklist

<Check>
- Landing screen is usable without auth or wallet prompts
- Trigger SIWF/Quick Auth only when backend needs it
</Check>

- Use MiniKit context for analytics only; avoid using it as primary auth
- Use Base Account seamlessly for onchain actions; no upfront connect flow
- If supporting alternate wallets, provide optional, non-blocking connect/switch
- Use SDK actions for social flows (compose/view) instead of deeplinks
- Provide a post-success share step and clear next action
- Test both Create Account and Connect Account paths

### Further reading

<CardGroup cols={3}>
  <Card title="Authentication" icon="key" href="/mini-apps/features/Authentication" />
  <Card title="Links & SDK Actions" icon="link" href="/mini-apps/features/links" />
  <Card title="Search & Discovery" icon="magnifying-glass" href="/mini-apps/features/search-and-discovery" />
</CardGroup>

<CardGroup cols={3}>
  <Card title="Sharing & Social Graph" icon="share" href="/mini-apps/features/sharing-and-social-graph" />
  <Card title="Provider & Initialization" icon="cog" href="/mini-apps/technical-reference/minikit/provider-and-initialization" />
  <Card title="useAuthenticate" icon="book-open" href="/mini-apps/technical-reference/minikit/hooks/useAuthenticate" />
</CardGroup>



