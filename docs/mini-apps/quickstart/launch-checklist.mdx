---
title: Launch Checklist 
description: High-level checklist and rationale for what to implement next, with links to bite‑size feature guides
---

This checklist introduces the key concepts that make Mini Apps succeed. Each section leads with the value, then points you to a focused feature guide. No code here—follow the links to implement.

## Register for Base Build

Base Build unlocks Builder Rewards, boosts your chances of being featured, provides growth insights, and gives you a Preview tool to test and debug your app.

<Card title="Register for Base Build" icon="chart-simple" href="https://base.dev" />


## Authentication

Authenticate when it unlocks value, not before. Fast, optional sign‑in keeps momentum and lets users act the moment onchain interactions are needed.

<Card title="Authentication" icon="user" href="/mini-apps/features/Authentication" />

## Manifest

Your manifest powers saving, discovery, and rich embeds. A strong manifest includes complete fields, valid assets, and `noindex: true` during testing.

<Card title="Manifest" icon="file" href="/mini-apps/features/manifest" />

## Embeds & Previews

Distribution starts in the feed: compelling previews with a clear image and launch button turn impressions into launches.

<Card title="Embeds & Previews" icon="image" href="/mini-apps/features/embeds-and-previews" />

## Search & Discovery

Be found across surfaces: set a primary category, share once to trigger indexing, and keep assets valid to appear in search and categories.

<Card title="Search & Discovery" icon="magnifying-glass" href="/mini-apps/features/search-and-discovery" />

## Sharing & Social Graph

Design for social lift: native share flows and social navigation turn single‑player moments into threads and returns.

<Card title="Sharing & Social Graph" icon="share" href="/mini-apps/features/sharing-and-social-graph" />

## Notifications (coming soon)

Re‑engage saved users with relevant, rate‑limited notifications at the right moments.

<Card title="Notifications" icon="bell" href="/mini-apps/features/notifications" />

## UX Best Practices

Build for compact, touch‑first contexts: respect safe areas, keep interfaces concise, and emphasize clear primary actions.

<CardGroup cols={2}>
  <Card title="Design patterns" icon="puzzle-piece" href="/mini-apps/design-ux/design-patterns" />
  <Card title="OnchainKit" icon="box" href="/mini-apps/design-ux/onchainkit" />
</CardGroup>


## Build for Growth 

Follow best practices to improve user engagement and retention. 

<CardGroup cols={2}>
  <Card title="Optimize Onboarding" icon="users" href="/mini-apps/growth/optimize-onboarding" />
  <Card title="Build Viral Mini Apps" icon="users" href="/mini-apps/growth/build-viral-mini-apps" />
</CardGroup>




