---
title: "Mini App Development Templates"
description: "Ready-to-use templates, tools, and tutorials to accelerate your Mini App development on Base"
sidebarTitle: "Templates"
---

## Vibe Code Templates

These interactive templates provide complete, working examples that you can fork and customize for your own Mini App projects.

<CardGroup cols={2}>
<Card title="Powerpuff Girl Personality Generator" icon="sparkles" href="https://v0.app/chat/powerpuff-girls-0yey0ja6kqW">
  An engaging personality quiz that analyzes users' Farcaster activity to determine their Powerpuff Girl personality match.
</Card>

<Card title="Mini App Base Pay" icon="credit-card" href="https://v0.app/chat/mini-app-base-pay-u0ZNsSt5Yjb">
  A streamlined payment interface demonstrating USD transactions through the Farcaster Mini App SDK.
</Card>
</CardGroup>

### Powerpuff Girl Personality Generator

This interactive template demonstrates how to build an engaging user experience by combining social data analysis with AI-powered personality matching.

**Key technologies implemented:**
- **Redis** for real-time notifications and caching
- **Neynar API** to query and analyze user's top Farcaster casts
- **OpenAI API** for intelligent character personality matching

<Tip>
This template showcases effective data integration patterns for social-based Mini Apps.
</Tip>

### Mini App Base Pay

A clean, functional payment interface that demonstrates the core payment flows available in Farcaster Mini Apps.

**Key technologies implemented:**
- **Base Pay** for secure payment processing
- **tokenSend action** from the Mini App SDK
- **Flexible recipient configuration** supporting both addresses and FIDs

<Note>
This template provides a solid foundation for any Mini App requiring payment functionality.
</Note>

## Video Tutorials

<Card title="Learn Manifest Signing with Farcaster Tools" icon="play" href="https://youtu.be/GlIxpp9323w?feature=shared">
  Step-by-step video tutorial demonstrating proper Mini App manifest signing using Farcaster development tools.
</Card>

**Tutorial covers:**
- Manifest signing process and security requirements
- Farcaster tools integration and setup
- Complete implementation walkthrough with best practices

## GitHub Templates

Production-ready code repositories that you can clone and deploy immediately.

<CardGroup cols={2}>
<Card title="My Mini Zora" icon="github" href="https://github.com/base/demos/tree/master/minikit/my-mini-zora">
  A personalized showcase creating custom collages of users' token holdings as dynamic embeds.
</Card>

<Card title="MiniKit Quick Auth" icon="key" href="https://github.com/coinbase/onchainkit/tree/main/examples/minikit-example">
  Essential authentication patterns and MiniKit integration starter template.
</Card>

 <Card 
    title="Full Mini Demo" 
    icon="github" 
    href="https://github.com/base/demos/tree/master/minikit/mini-app-full-demo"
  >
    A comprehensive showcase demonstrating the complete range of MiniKit capabilities and Base ecosystem integrations.
  </Card>
  <Card 
  title="Existing App Integration" 
  icon="github" 
  href="https://github.com/base/demos/tree/master/minikit/mini-app-route"
>
  Shows how to add Mini App routes to existing Next.js applications without major refactoring.
</Card>
</CardGroup>


### My Mini Zora

Transform user token holdings into shareable, visual experiences with this comprehensive NFT showcase template.

**Key technologies implemented:**
- **Zora API** for comprehensive token data retrieval
- **Dynamic embed generation** for personalized social sharing
- **Advanced token analysis** for user-specific portfolio insights

<Check>
Perfect starting point for Mini Apps requiring  dynamic social sharing capabilities & using the zora api.
</Check>

### MiniKit Quick Auth

Master the fundamentals of Mini App authentication with this essential starter template.

**Key technologies implemented:**
- **MiniKit Quick Auth** for streamlined user authentication
- **Core Mini App SDK** integration patterns
- **Complete authentication flow** demonstrations and best practices

### Full Mini Demo
A comprehensive showcase demonstrating the complete range of MiniKit capabilities and Base ecosystem integrations in a single, feature-rich application.


**Key technologies implemented:**
- **Mini App SDK** Complete Mini App SDK integration with all available actions and hooks
- **Complete authentication flow** demonstrations and best practices

<Note>
This demo serves as the ultimate reference implementation, showcasing every Mini App feature in production-ready code that developers can learn from and adapt.
</Note>

### Existing App Integration
Seamlessly add Mini App functionality to your existing Next.js application with this practical conversion template that requires minimal code changes.

**Key technologies implemented:**
- **Route-level Mini App** integration without disrupting existing application architecture
- **Incremental adoption pattern** allowing gradual Mini App feature rollout
- **Scoped Mini App** allowing Mini App capabilities only within a certain route

<Check>
Perfect starting point for developers who want to extend existing Next.js applications with Mini App capabilities without rebuilding from scratch.
</Check>

## Getting Started

<Steps>
<Step title="Choose your template">
  Select the template that best matches your Mini App concept and requirements.
</Step>

<Step title="Fork or clone">
  Use the provided links to fork the interactive templates or clone the GitHub repositories.
</Step>

<Step title="Customize and deploy">
  Modify the templates to match your specific use case and deploy using your preferred hosting solution.
</Step>
</Steps>

### More Mini App Resources

<CardGroup cols={2}>
  <Card
    title="Viral Mini Apps"
    icon="sparkles"
    href="/mini-apps/growth/build-viral-mini-apps"
  >
    Strategies and examples for building highly shareable, viral mini apps.
  </Card>
  <Card
    title="Data Driven Growth"
    icon="chart-bar"
    href="/mini-apps/growth/data-driven-growth"
  >
    Learn how to use analytics and Base.dev to optimize your mini app's growth.
  </Card>
  <Card
    title="Optimize Onboarding"
    icon="user-plus"
    href="/mini-apps/growth/optimize-onboarding"
  >
    Best practices for onboarding users and maximizing retention.
  </Card>
  <Card
    title="Design & UX Best Practices"
    icon="paint-brush"
    href="/mini-apps/design-ux/best-practices"
  >
    Guidelines for creating delightful, user-friendly mini app experiences.
  </Card>
</CardGroup>


