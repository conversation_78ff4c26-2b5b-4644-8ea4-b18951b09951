---
title: Common Issues & Debugging
description: Frequent issues encountered during Mini App development and their solutions
---

## Prerequisites & Setup Verification

Ensure your Mini App has the foundational requirements in place.

### Required Files and Structure

```text
your-domain.com/
├── .well-known/
│   └── farcaster.json          # Required manifest file
├── your-app/
│   ├── index.html              # Your app entry point
│   └── ...                     # Your app files
```

### Environment Setup Checklist

- Domain is accessible via HTTPS  
- Manifest file exists at `/.well-known/farcaster.json`  
- All image URLs are publicly accessible

# Debugging
Use Base Build's built-in Preview Tool for foundational debugging.

<Card title="Preview Tool" img="/images/base-build/preview-tool-overview.png">
Start debugging your app using the [Preivew tool](https://base.dev/preview)
</Card>

The **Preview tool** will help you:  

- Validate your app's manifest and metadata  
- Test how your app will appear in the Base app  
- Verify ownership and account association  

The Preview tool provides clear visual cues:  
- ✅ Green check marks when things are set up correctly  
- ❌ Red indicators when something needs your attention  

### Components of the Preview Tool  

The Preview tool has three main components:  

- **Console**: Preview your app and review logs to make informed decisions about performance.  
- **Account Association**: Confirm your app is linked to the correct account, signatures are valid, and the domain matches what’s specified in the manifest.  
- **Metadata**: Ensure your Mini App renders exactly as expected by verifying required fields like name, icon, tags, and splash image.  


### Basic Validation Steps

1. Test manifest accessibility: visit `https://yourdomain.com/.well-known/farcaster.json`  
2. Validate JSON syntax with JSONLint  
3. Ensure your app loads without console errors

## Quick Diagnostic Workflow

<Tip>The best way to validate your app works is by using Base Build's built-in [Preview tool](https://base.dev/preview)</Tip>

- Not appearing in search? → App Discovery & Indexing Issues
- Not rendering as an embed? → Embed Rendering Issues
- Wallet connection problems? → Wallet Connection Problems
- Need mobile testing tools? → Mobile Testing & Debugging
- Changes not appearing? → Manifest Configuration Problems
- App closes on gestures? → Gesture Conflicts and App Dismissal Issues

## Detailed Problem Solutions

### 1. App Discovery & Indexing Issues

Problem: Your Mini App doesn't appear in search results or app catalogs.

Root cause: Missing or incomplete manifest configuration.

Solution: Ensure your manifest includes all required fields (see Manifest feature guide).

Critical requirements:

- `primaryCategory` is required for searchability and category pages  
- `accountAssociation` is required for verification

App Indexing Requirements:

1. Complete your manifest setup  
2. Share your Mini App URL in a post  
3. Indexing can take up to 10 minutes  
4. Verify appearance in app catalogs

Caching Issues — Changes Not Appearing:

Farcaster clients may cache manifest data for up to 24 hours. Re‑share to trigger a refresh and allow ~10 minutes.

### 2. Manifest Configuration Problems

Image Display Issues:

1. Test image accessibility in incognito  
2. Verify image format (PNG, JPG, WebP supported)  
3. Check dimensions  
4. Ensure HTTPS URLs only

### 3. Embed Rendering Issues

Problem: Your Mini App URL doesn't render as a rich embed when shared.

Root cause: Incorrect or missing `fc:frame` metadata.

Solution: Use `name="fc:frame"` meta tag in `<head>` and validate using the Embed Tool.

### 4. Wallet Connection Problems

Always use the user's connected wallet for optimal experience. You can do this either by using [OnchainKit's Wallet component](/onchainkit/wallet/wallet) or Wagmi hooks. Below is a Wagmi hook example:

```tsx App.tsx
import { useAccount } from 'wagmi';

function MyComponent() {
  const { address, isConnected } = useAccount();
  
  const walletConnected = isConnected;
  
  const userAddress = address; // Cryptographically verified
  
  return (
    <div>
      {walletConnected && (
        <p>Wallet: {userAddress}</p>
      )}
      {/* Use wallet data for secure operations */}
    </div>
  );
}
```



### 5. Gesture Conflicts and App Dismissal Issues

Disable native gestures when calling ready if you use swipe/drag interactions:

```ts App.tsx
await sdk.actions.ready({ disableNativeGestures: true });
```

### 6. Mobile Testing & Debugging

**Eruda Mobile Console Setup:**

Add Eruda for mobile console debugging during development:

```tsx App.tsx
import { useEffect } from 'react';

export default function App() {
  useEffect(() => {
    // Only load Eruda in development and not on localhost
    if (typeof window !== 'undefined' && 
        process.env.NODE_ENV === 'development' && 
        !window.location.hostname.includes('localhost')) {
      import('eruda').then((eruda) => eruda.default.init());
    }
  }, []);

  return (
    <div>
      {/* Your app content */}
    </div>
  );
}
```

**Mobile Testing Workflow:**

1. Deploy to production or use ngrok for local testing
2. Share the mini app in a Farcaster DM to yourself  
3. Open in mobile client (Base App, Farcaster)
4. Use Eruda console for debugging on mobile
5. Test across multiple clients for compatibility

**Testing Checklist:**

- [ ] App loads correctly on mobile devices
- [ ] Touch interactions work properly
- [ ] Viewport is correctly sized
- [ ] Images load and display correctly
- [ ] Console shows no critical errors

## Advanced Troubleshooting

**CBW Validator Tool:**

Use the Coinbase Wallet validator for Base App compatibility analysis. This AI-powered tool can identify unsupported patterns and suggest improvements.

**Complete Manifest Example:**

```json farcaster.json
{
  "accountAssociation": {
    "header": "your-farcaster-header",
    "payload": "your-farcaster-payload", 
    "signature": "your-farcaster-signature"
  },
    "baseBuilder": {
    "allowedAddresses": ["0x..."]
  },
  "frame": {
    "name": "Your Mini App",
    "iconUrl": "https://yourapp.com/icon.png",
    "homeUrl": "https://yourapp.com",
    "imageUrl": "https://yourapp.com/og.png",
    "buttonTitle": "Launch App", 
    "description": "Your app description under 130 characters",
    "primaryCategory": "social",
    "tags": ["tag1", "tag2"]
  }
}
```

## Success Verification

Basic functionality and discovery/sharing checklists: confirm load, images, wallet, manifest endpoint, embed rendering, and search presence.

## Getting Additional Help

- [Base Build Preview Tool](https://base.dev/preview)  
- JSONLint  
- [Eruda](https://github.com/liriliri/eruda)  
- Base Discord — #minikit channel



