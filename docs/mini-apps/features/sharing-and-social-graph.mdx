---
title: Sharing & Social Graph
description: Enable native share flows and social navigation
---

## Adding Share Functionality 

Prompt users to share during key accomplishment moments using MiniKit’s compose hook.

```ts ComposeCastButton.tsx
import { useComposeCast } from '@coinbase/onchainkit/minikit';

export default function ComposeCastButton() {
  const { composeCast } = useComposeCast();

  const handleCompose = () => {
    composeCast({ text: 'Just minted an awesome NFT using @coinbase OnchainKit!' });
  };

  const handleComposeWithEmbed = () => {
    composeCast({
      text: 'Check out this amazing Mini App!',
      embeds: ['https://your-mini-app-url.com'],
    });
  };

  return (
    <div>
      <button onClick={handleCompose}>Share Achievement</button>
      <button onClick={handleComposeWithEmbed}>Share Frame</button>
    </div>
  );
}
```

<Tip>
Strategic sharing moments include: post‑achievement, post‑mint, after beating a challenge, or reaching milestones.
</Tip>

## View Casts and Profiles

Link users into casts and profiles directly from your app via MiniKit hooks.

<CardGroup cols={2}>
  <Card title="useViewCast" icon="book-open" href="/mini-apps/technical-reference/minikit/hooks/useViewCast" />
  <Card title="useViewProfile" icon="book-open" href="/mini-apps/technical-reference/minikit/hooks/useViewProfile" />
</CardGroup>

## Best Practices

- Encourage meaningful, contextual shares
- Avoid spammy prompts; tie sharing to value
- Make shared previews visually consistent with your UI

Further reading:

- [Embeds & Previews](/mini-apps/features/embeds-and-previews)



