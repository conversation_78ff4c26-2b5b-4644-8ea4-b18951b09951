---
title: Manifest
description: Define and configure your Mini App’s manifest to power search, discovery, and rich embeds in the Base App.
---

> **What you’ll learn**  
> By the end of this guide, you’ll be able to:
> - Define a complete `farcaster.json` manifest that meets Base App requirements.  
> - Map each manifest property to its role in search, discovery, and embeds.  
> - Validate images, text, and URLs against required constraints.  
> - Link your manifest to your domain using `accountAssociation`.  
> - Control indexing for staging and production with `noindex`. 

## Overview
<Tip>The best way to validate your manifest is by using Base Build's built-in [Preview tool](https://base.dev/preview)</Tip>

Your `/.well-known/farcaster.json` file is the manifest for your Mini App. It contains all the metadata Base App uses to:

- Display your app in search results and category listings  
- Generate rich, clickable embeds when your app is shared  
- Show your app in users’ saved apps for quick access  

If the manifest is missing, incomplete, or invalid, your app won’t appear in Base App discovery features.

## Location

Serve your manifest over HTTPS at:

```bash Terminal
https://your-domain.com/.well-known/farcaster.json
```

<Tip>
Serve with `Content-Type: application/json` and ensure the file is publicly accessible (no auth).
</Tip>


## How Base App uses Manifest

- **Search indexing**: Your app appears in search after it has been shared at least once. Indexing typically completes in ~10 minutes. 
- **Category placement**: `primaryCategory` determines where your app appears in category browsing. `tags` improve cross-surface visibility and filtering.
- **Embeds**: Open Graph fields (`ogTitle`, `ogDescription`, `ogImageUrl`) and `heroImageUrl` power compelling feed previews that drive clicks.
- **Saved apps**: `name` and `iconUrl` appear when users save your Mini App for quick access.

<Warning>
If your Mini App does not show in search, follow the debugging guide [here](/mini-apps/troubleshooting/common-issues#1-app-discovery--indexing-issues).
</Warning>


## Example manifest

```json farcaster.json
{
  "accountAssociation": {
    "header": "eyJmaWQiOjkxNTIsInR5cGUiOiJjdXN0b2R5Iiwia2V5IjoiMHgwMmVmNzkwRGQ3OTkzQTM1ZkQ4NDdDMDUzRURkQUU5NDBEMDU1NTk2In0",
    "payload": "eyJkb21haW4iOiJhcHAuZXhhbXBsZS5jb20ifQ",
    "signature": "MHgxMGQwZGU4ZGYwZDUwZTdmMGIxN2YxMTU2NDI1MjRmZTY0MTUyZGU4ZGU1MWU0MThiYjU4ZjVmZmQxYjRjNDBiNGVlZTRhNDcwNmVmNjhlMzQ0ZGQ5MDBkYmQyMmNlMmVlZGY5ZGQ0N2JlNWRmNzMwYzUxNjE4OWVjZDJjY2Y0MDFj"
  },
  "baseBuilder": {
    "allowedAddresses": ["0x..."]
  },
  "frame": {
    "version": "1",
    "name": "Example Mini App",
    "homeUrl": "https://ex.co",
    "iconUrl": "https://ex.co/i.png",
    "splashImageUrl": "https://ex.co/l.png",
    "splashBackgroundColor": "#000000",
    "webhookUrl": "https://ex.co/api/webhook",
    "subtitle": "Fast, fun, social",
    "description": "A fast, fun way to challenge friends in real time.",
    "screenshotUrls": [
      "https://ex.co/s1.png",
      "https://ex.co/s2.png",
      "https://ex.co/s3.png"
    ],
    "primaryCategory": "social",
    "tags": ["example", "miniapp", "baseapp"],
    "heroImageUrl": "https://ex.co/og.png",
    "tagline": "Play instantly",
    "ogTitle": "Example Mini App",
    "ogDescription": "Challenge friends in real time.",
    "ogImageUrl": "https://ex.co/og.png",
    "noindex": true
  }
}
```

<Note>
For a visual mapping of how fields render, see the [Mini App specification reference](/mini-apps/features/search-and-discovery#visual-specifications).
</Note>



## Field reference

### Top-level fields

| Property            | Type   | Required | Description                                       |
|---------------------|--------|----------|---------------------------------------------------|
| `accountAssociation`| object | Yes      | Proves domain ownership for your Mini App. |
| `frame`             | object | Yes      | Contains all metadata used by Base App.           |
| `baseBuilder`             | object | Yes      | This verifies ownership and connects your Base Build account.          |


### `accountAssociation` fields

| Property    | Type   | Required | Description                                  |
|-------------|--------|----------|----------------------------------------------|
| `header`    | string | Yes      | Encoded header for the association payload.  |
| `payload`   | string | Yes      | Encoded payload containing your domain.      |
| `signature` | string | Yes      | Signature over the payload.                  |

### `baseBuilder` fields

| Property    | Type   | Required | Description                                  |
|-------------|--------|----------|----------------------------------------------|
| `allowedAddresses`   | string | Yes      | This verifies ownership and connects your Base Build account.  |



### `frame` fields

#### Identity & Launch

Defines your Mini App’s core identity and the URL users land on when they open it.

| Property        | Type   | Required | Description               | Constraints |
|-----------------|--------|----------|---------------------------|-------------|
| `version`       | string | Yes      | Manifest version.         | Must be `"1"`. |
| `name`          | string | Yes      | Mini App name.            | Max 32 chars. |
| `homeUrl`       | string | Yes      | Default launch URL.       | HTTPS URL, max 1024 chars. |
| `iconUrl`       | string | Yes      | Icon image URL.           | HTTPS URL, PNG 1024×1024; transparent background discouraged. |

---


#### Loading Experience

Controls the splash screen visuals and colors shown while your Mini App loads.

| Property                | Type   | Required | Description                | Constraints |
|-------------------------|--------|----------|----------------------------|-------------|
| `splashImageUrl`        | string | Yes      | Loading image.             | HTTPS URL, recommended 200×200px. |
| `splashBackgroundColor` | string | Yes      | Loading background color.  | Hex code (e.g., `#000000`). |

---

#### Discovery & Search

Determines how your Mini App is indexed, categorized, and surfaced across Base App discovery features.

| Property           | Type     | Required | Description                                        | Constraints |
|--------------------|----------|----------|----------------------------------------------------|-------------|
| `primaryCategory`  | string   | Yes       | Controls where your app appears in category browsing. | One of: `games`, `social`, `finance`, `utility`, `productivity`, `health-fitness`, `news-media`, `music`, `shopping`, `education`, `developer-tools`, `entertainment`, `art-creativity`. |
| `tags`             | string[] | Yes       | Search/filter tags.                                | Up to 5; ≤ 20 chars each; lowercase; no spaces/emojis/special chars. |
| `noindex`          | boolean  | No       | Exclude from search results.                       | `true` = exclude, default = include. |
| `requiredChains`   | string[] | No       | CAIP-2 chain IDs.                                  | Supported: `eip155:8453` (Base mainnet), `eip155:84532` (Base Sepolia). |

---

#### Display Information

Provides the descriptive text, screenshots, and promotional images shown on your Mini App’s profile.

| Property         | Type     | Required | Description                         | Constraints |
|------------------|----------|----------|-------------------------------------|-------------|
| `subtitle`       | string   | No       | Short description under name.       | Max 30 chars; avoid emojis/special chars. |
| `description`    | string   | No       | Promo text for app page.            | Max 170 chars; avoid emojis/special chars. |
| `tagline`        | string   | No       | Marketing tagline.                  | Max 30 chars. |
| `heroImageUrl`   | string   | No       | Large promo image.                  | 1200×630px (1.91:1), PNG/JPG. |
| `screenshotUrls` | string[] | No       | Visual previews.                    | Max 3; portrait 1284×2778px recommended. |

---

#### Embeds & Social Sharing

Configures how your Mini App appears when shared in feeds or on social platforms.

| Property                  | Type   | Required | Description               | Constraints |
|---------------------------|--------|----------|---------------------------|-------------|
| `ogTitle`                 | string | No       | Open Graph title.         | Max 30 chars. |
| `ogDescription`           | string | No       | Open Graph description.   | Max 100 chars. |
| `ogImageUrl`              | string | No       | Open Graph image.         | 1200×630px (1.91:1), PNG/JPG. |
| `imageUrl` (deprecated)   | string | No       | Legacy default feed image.| Prefer `ogImageUrl`. |
| `buttonTitle` (deprecated)| string | No       | Legacy feed button text.  | Prefer embed button config in OG. |

---

#### Advanced Capabilities

Specifies technical integrations, event handling, and special permissions your Mini App requires.

| Property               | Type     | Required | Description                 | Constraints |
|------------------------|----------|----------|-----------------------------|-------------|
| `webhookUrl`           | string   | No       | POST events endpoint.       | HTTPS URL, max 1024 chars. Required if using notifications. |


<Warning>
Choose your `primaryCategory` carefully — it determines where your app appears in Base App’s category browsing and rankings.
</Warning>


## Validation checklist

- Manifest is served over HTTPS at `/.well-known/farcaster.json`  
- All required fields are present 
- Image sizes match the constraints (icon, OG, hero, screenshots)  
- Text fields respect character and formatting limits  
- `"noindex": true` is used only for staging/development  


<Tip>
Test your manifest with a simple `curl` to confirm accessibility and headers: `curl -sI https://your-domain.com/.well-known/farcaster.json`.
</Tip>


## Development vs. production

- Set `"noindex": true` for development or staging environments to prevent search indexing.  
- Remove or set `"noindex": false` for production so users can discover your app.  

<Note>
Your app appears in search after the first share is detected. Indexing usually completes in ~10 minutes.
</Note>


## Common issues

- Missing required fields → app won’t index in search  
- Image format/size mismatches → broken or distorted embeds  
- Invalid `primaryCategory` → app won’t appear in category browsing  
- Overlong strings → truncation or rejection at indexing  
- Manifest not publicly accessible → discovery fails  


## Next steps

- Review discovery mechanics and ranking in [Mini App Search and Discovery](/mini-apps/features/search-and-discovery)  
- Learn how embeds are generated and tested in [Sharing Your Mini App](/mini-apps/features/embeds-and-previews)  



