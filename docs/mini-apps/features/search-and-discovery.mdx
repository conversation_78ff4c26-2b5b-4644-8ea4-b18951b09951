---
title: Search & Discovery
description: Learn how users discover and access Mini Apps in the Base ecosystem, including discovery surfaces, ranking systems, and optimization strategies for maximum visibility.
---

> **What you’ll learn**  
> By the end of this guide, you’ll be able to:
> - Get your Mini App indexed in Base App search and understand how indexing works.  
> - Choose the right categories and implement strategies to improve visibility across discovery surfaces.  
> - Apply best practices to increase discoverability across Base App surfaces.  

## Search

### How Search Works
Users discover Mini Apps through direct search queries in Base App.

<Frame caption="Here in The Base App you can see searching based on partial titles">
<img src="/images/minikit/search.jpg" alt="search bar in base app" height="200"/>
</Frame>

Your Mini App will only appear in search results after it has been indexed. To ensure indexing, share your Mini App at least once in Base App. Indexing typically takes ~10 minutes after the first share.

### Managing Search Indexing

Development Environment: add `"noindex": true` to prevent dev/staging from appearing in search. Remove or set false for production.

**Removing from Search:**
To remove your Mini App from search results, invalidate your manifest (removes from all discovery).

<Warning>
If your Mini App does not show in search please follow the debugging guide [here](/mini-apps/troubleshooting/common-issues#1-app-discovery--indexing-issues)
</Warning>

## Discovery Surfaces


### Saved Apps
Personal launcher and quick access hub

<Frame caption="Saved Apps">
<img src="/images/minikit/my-apps.jpg" alt="saved apps" height="200"/>
</Frame>

Appears here:
- User's saved Mini Apps
- Recently used applications

Prompt users to save via the Add Frame flow at key value moments.

### App Categories
Browsable directory organized by interest

<Frame caption="App Categories">
<img src="/images/minikit/categories.jpg" alt="app categories" height="100"/>
</Frame>

<Warning>
Choose your primaryCategory carefully as it determines where your app appears in Base App's category browsing.
</Warning>

<Frame caption="Ranking for the Social Category">
<img src="/images/minikit/ranking.jpeg" alt="app categories" className="h-18 w-auto" />
</Frame>

<Tip> The Base app uses aggregated data (7-day rolling window) to generate dynamic category rankings.</Tip>

## Visual Specifications

For detailed visual mapping of how metadata translates to UI elements, see the [Figma specification file](https://www.figma.com/design/4wx6s24NB0KLgprQAyMT8R/TBA-Mini-App-Specs).

## Optimization Strategies



### Category Optimization
- **Choose primaryCategory strategically** based on your target audience and competition
- **Monitor category rankings** and adjust strategy based on performance
- **Consider seasonal trends** that might affect category popularity

### Metadata Best Practices
- **High-quality icon** (1024×1024, clear and recognizable at small sizes)
- **Compelling description** under 130 characters that clearly communicates value
- **Relevant tags** that match user search behavior
- **OG image optimized** for social sharing (1200×630) with clear visual hierarchy

## Build for Discovery: Checklist

- [ ] High-quality icon (1024×1024, clear at small sizes)
- [ ] Compelling description under 130 characters
- [ ] Relevant category selection for your target audience
- [ ] OG image optimized for social sharing (1200×630)
- [ ] Test metadata rendering across different clients
- [ ] Implement proper manifest files with correct categorization
- [ ] Choose relevant categories (primaryCategory)
- [ ] Create shareable moments that naturally encourage shares
- [ ] Design compelling embeds with clear CTAs
- [ ] Encourage saves for easy return access

## Complete Implementation Guide

For step-by-step implementation including code examples, see:
- [Manifest Configuration](/mini-apps/features/manifest)
- [Embed Implementation](/mini-apps/features/embeds-and-previews)

Further reading:

- [Sharing & Embeds](/mini-apps/features/embeds-and-previews)
- [Manifest](/mini-apps/features/manifest)



