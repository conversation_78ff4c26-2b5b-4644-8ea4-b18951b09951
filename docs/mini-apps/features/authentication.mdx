---
title: Authentication
description: Let users start fast and ask for a wallet only when needed
---

## Authentication guidance from Base App

<Warning>
Save authentication that requires an interaction for interactions that require it (e.g., buying something, viewing personalized pages).
</Warning>

Supported approaches:

<Tabs>
<Tab title="Sign In with Farcaster / Quick Auth">
Base App natively supports SIWF in-app, enabling social identity without leaving the app. Quick Auth can issue a JWT to persist session state.

**User Experience in Base App:**
- **Create Account Users** (new Farcaster accounts created during Base App onboarding): Users see a "Login request" tray with the SIWF message and can sign it directly with their passkey
- **Connect Account Users** (existing Farcaster accounts connected during onboarding): Users are prompted to deeplink to Farcaster one-time only to register their wallet as an auth address, then enjoy seamless in-app sign-in thereafter
</Tab>

<Tab title="Wallet Auth">
Base App provides an in‑app smart wallet that doesn't require app switching. Use wallet auth for a persisted session when necessary, but avoid gating initial exploration behind connect.
</Tab>

<Tab title="Context Data">
All hosts return context data (including user). Use it for analytics or lightweight session hints, but **do not treat as primary auth** since context data can be spoofed by developers who create their own mini app hosts.
</Tab>
</Tabs>


## Implementation Example

```tsx App.tsx
import { useMiniKit, useAuthenticate } from '@coinbase/onchainkit/minikit';

function MyComponent() {
  const { context } = useMiniKit();
  const { user } = useAuthenticate();

  // ✅ Safe: Use context for analytics only
  const userFid = context.user.fid; // For analytics tracking
  
  // ✅ Safe: Use cryptographic verification for auth
  const verifiedUser = user; // From SIWF or wallet auth
  
  // ❌ Unsafe: Don't rely on context for primary auth
  // const isAuthenticated = !!context.user.fid; // Can be spoofed!
  
  return (
    <div>
      {/* Use verified user data for secure operations */}
    </div>
  );
}
```

<Info>
For a complete example of using Quick Auth with MiniKit, see [here](https://github.com/coinbase/onchainkit/blob/main/examples/minikit-example/app/components/UserInfo.tsx).
</Info>

## Best practices

- Gate wallet only at the point of onchain action
- Prefer SIWF/Quick Auth for low‑friction identity
- Use context for analytics; avoid using it as primary auth
- Handle Base App's different authentication flows gracefully
- Always use cryptographic verification for security-critical operations

Further reading:

<CardGroup cols={2}>
  <Card title="useAuthenticate" icon="book-open" href="/mini-apps/technical-reference/minikit/hooks/useAuthenticate" />
  <Card title="Provider & Initialization" icon="cog" href="/mini-apps/technical-reference/minikit/provider-and-initialization" />
</CardGroup>



