---
title: 'Components'
description: 'Ready-to-use components to accelerate the development of your mini apps.'
---

OnchainKit is a React-based design system for accelerating the development of your apps. It provides pre-built, reusable full-stack components that follow Base's design guidelines, ensuring consistency and high-quality user experience.

### Get Started

Install the UI Kit with NPM:

```bash Terminal
npx create-onchain@latest --mini
```

Explore the full component library and usage examples in the [OnchainKit docs](/onchainkit/getting-started).