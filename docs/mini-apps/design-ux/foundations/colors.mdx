---
title: "Colors"
description: "Effective use of color helps communicate clearly, represent your brand, and create cohesive mini app experiences."
---

![colors-overview](/images/design-guidelines/colors-overview.png)

## Basics

**Primary Colors**: Your primary color should represent your brand. 
- Call-to-action buttons
- Active states
- Key interactive elements
- Brand recognition

**Secondary Colors**: Supporting colors that complement your primary.
- Secondary actions
- Background accents
- Supporting UI elements
- Visual variety

**Neutral Colors**:Essential for text, backgrounds, and structure:
- **Text colors**: High contrast for readability
- **Background colors**: Subtle variations for depth
- **Border colors**: Subtle separation between elements

[Tailwind CSS](https://tailwindcss.com/docs/colors) offers an excellent starting color system.

## Themes

### Light and Dark Modes

![themes](/images/design-guidelines/colors-theme.png)

Design for both light and dark modes to provide users with their preferred viewing experience:

- **Light mode**: Clean, bright interface for daytime use
- **Dark mode**: Reduced eye strain for low-light environments
- **System preference**: Respect user's OS theme setting
- **Manual toggle**: Allow users to override system preference


### Theme Considerations
- **Consistent contrast**: Maintain proper contrast ratios in both themes
- **Color meaning**: Ensure semantic colors work in both contexts
- **Brand consistency**: Keep brand colors recognizable across themes
- **Smooth transitions**: Add transitions for theme changes
- **User preference**: Respect system preferences by default

## Best Practices
- Start with a limited color palette
- Define clear usage guidelines
- Document your color system
- Test thoroughly before implementation

## Implementation Examples

#### Standard CSS Approach
Define semantic color names with comprehensive theming support using CSS custom properties:

```css
:root {
  /* Base colors */
  --color-primary: #578BFA;
  --color-primary-hover: #4A7CE8;
  --color-primary-active: #3D6FD6;
  --color-primary-light: #E6F0FF;
  --color-primary-dark: #1E40AF;
  
  /* Semantic colors */
  --color-success: #10B981;
  --color-success-light: #D1FAE5;
  --color-warning: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-error: #EF4444;
  --color-error-light: #FEE2E2;
  --color-info: #3B82F6;
  --color-info-light: #DBEAFE;
  
  /* Text colors */
  --color-text-primary: #1A1A1A;
  --color-text-secondary: #666666;
  --color-text-tertiary: #9CA3AF;
  --color-text-inverse: #FFFFFF;
  
  /* Background colors */
  --color-background: #FFFFFF;
  --color-background-secondary: #F8F9FA;
  --color-background-tertiary: #F3F4F6;
  --color-background-elevated: #FFFFFF;
  
  /* Border colors */
  --color-border: #E5E7EB;
  --color-border-light: #F3F4F6;
  --color-border-strong: #D1D5DB;
  
  /* Shadow colors */
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-strong: rgba(0, 0, 0, 0.25);
}

[data-theme="dark"] {
  /* Text colors */
  --color-text-primary: #FFFFFF;
  --color-text-secondary: #A3A3A3;
  --color-text-tertiary: #6B7280;
  --color-text-inverse: #1A1A1A;
  
  /* Background colors */
  --color-background: #0F0F0F;
  --color-background-secondary: #1A1A1A;
  --color-background-tertiary: #262626;
  --color-background-elevated: #1A1A1A;
  
  /* Border colors */
  --color-border: #374151;
  --color-border-light: #262626;
  --color-border-strong: #4B5563;
  
  /* Shadow colors */
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-strong: rgba(0, 0, 0, 0.5);
}
```

#### Tailwind CSS with Custom Theme
```javascript
// tailwind.config.js
module.exports = {
  darkMode: 'class', // or 'media'
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#E6F0FF',
          100: '#CCE1FF',
          500: '#578BFA',
          600: '#4A7CE8',
          700: '#3D6FD6',
          900: '#1E40AF',
        },
        background: {
          light: '#FFFFFF',
          dark: '#0F0F0F',
        },
        text: {
          primary: {
            light: '#1A1A1A',
            dark: '#FFFFFF',
          },
          secondary: {
            light: '#666666',
            dark: '#A3A3A3',
          }
        }
      }
    }
  }
}
```

## Testing Your Color System

- Test all color combinations
- Verify contrast ratios using tools like [WebAIM](https://webaim.org/resources/contrastchecker/)
- Check in different lighting conditions
- Test on various devices and screens
- Validate cultural appropriateness




