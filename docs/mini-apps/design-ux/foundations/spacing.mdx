---
title: "Spacing"
description: "Consistent spacing creates visual rhythm, improves readability, and enhances user experience."
---

![spacing](/images/design-guidelines/spacing-overview.png)

## Best Practices

### Visual Hierarchy
- **More space**: Creates separation and importance
- **Less space**: Groups related elements together
- **Consistent spacing**: Creates visual rhythm and predictability

### Breathing Room
- Give content room to breathe
- Avoid cramped layouts
- Balance white space with content density
- Consider mobile screen constraints

## Spacing System

### Base Unit
Start with a base spacing unit (typically 4px or 8px):
- **4px base**: More granular control, better for mobile
- **8px base**: Easier mental math, good for desktop
- **Choose one**: Maintain consistency throughout

### Scale
Create a consistent scale:
```css
:root {
  --space-xs: 4px;   /* 0.25rem */
  --space-sm: 8px;   /* 0.5rem */
  --space-md: 16px;  /* 1rem */
  --space-lg: 24px;  /* 1.5rem */
  --space-xl: 32px;  /* 2rem */
  --space-2xl: 48px; /* 3rem */
  --space-3xl: 64px; /* 4rem */
}
```

## Spacing Patterns

### Component Spacing
- **Internal padding**: Space within components
- **External margins**: Space between components
- **Gap**: Space between flex/grid items
- **Border spacing**: Space around borders

### Layout Spacing
- **Container padding**: Space around main content
- **Section spacing**: Space between major sections
- **Column gaps**: Space between grid columns
- **Row gaps**: Space between grid rows

## Mobile-First Considerations

### Touch Targets
- **Minimum size**: 44px for touch targets
- **Spacing**: At least 8px between touch targets
- **Thumb zones**: Consider reachability on mobile
- **One-handed use**: Optimize for single-hand operation

### Screen Real Estate
- **Compact layouts**: Efficient use of limited space
- **Progressive disclosure**: Show more on larger screens
- **Responsive spacing**: Adjust for different screen sizes
- **Content priority**: Most important content first

## Spacing Guidelines

### Text Spacing
- **Line height**: 1.4-1.6 for body text
- **Paragraph spacing**: 1-1.5x font size
- **Heading spacing**: 1.5-2x font size
- **List spacing**: Consistent item spacing

### Component Spacing
- **Button padding**: 12px-16px vertical, 16px-24px horizontal
- **Input padding**: 12px-16px all around
- **Card padding**: 16px-24px
- **Modal padding**: 24px-32px

### Layout Spacing
- **Container max-width**: 1200px with auto margins
- **Section spacing**: 48px-64px between sections
- **Grid gaps**: 16px-24px between columns
- **Mobile margins**: 16px-24px on mobile

## Implementation Examples

### CSS Custom Properties
```css
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

.component {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}
```

### Utility Classes
```css
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
```

## Responsive Spacing

### Breakpoint Adjustments
```css
/* Mobile first */
.container {
  padding: var(--spacing-md);
}

/* Tablet and up */
@media (min-width: 768px) {
  .container {
    padding: var(--spacing-lg);
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .container {
    padding: var(--spacing-xl);
  }
}
```

### Fluid Spacing
```css
/* Fluid spacing that scales with viewport */
.section {
  padding: clamp(16px, 4vw, 48px);
}
```

## Common Mistakes

### Avoid These Issues
- **Inconsistent spacing**: Using random spacing values
- **Too tight**: Cramped layouts that feel claustrophobic
- **Too loose**: Excessive white space that wastes screen real estate
- **No system**: Ad-hoc spacing without a defined system

## Testing Your Spacing

- Review layouts at different screen sizes
- Check spacing consistency across components
- Verify touch target sizes on mobile
- Test with different content lengths

