---
title: "Mobile First"
description: "Mobile-first design principles and responsive strategies for mini apps"
---

Mini apps are primarily used on mobile, so adopting a mobile-first design is key to their success.

![mobile-first](/images/design-guidelines/mobile-first.png)

## Best Practices

- Use legible font sizes, line heights, and adequate contrast for small screens.
- Help people concentrate on primary tasks and content by limiting the number of onscreen controls while making secondary details and actions discoverable with minimal interaction.
- Use tab navigation to simplify movement within the app.
- Ensure layouts adjust smoothly to various screen sizes and orientations.
- Design for vertical scrolling rather than horizontal, as it’s more natural on mobile.

## Mobile Constraints

### Screen Size
- **Small viewport**: Limited screen real estate
- **Portrait orientation**: Vertical layout considerations
- **Thumb navigation**: One-handed operation
- **Content priority**: Most important content first

### Touch Interactions
- **Touch targets**: Minimum 44px for touch areas
- **Gesture support**: Swipe, pinch, tap interactions
- **Hover states**: Not available on touch devices
- **Focus management**: Keyboard and screen reader support

### Performance and Loading State
- App should load quickly (&lt;2s for main views)
- Minimize spinners
- Handle errors gracefully

## Testing Mobile Experience

- **Real devices**: Test on actual mobile devices
- **Different screen sizes**: Various phone and tablet sizes
- **Different orientations**: Portrait and landscape
- **Touch interactions**: Verify touch targets work

## Tools and Resources

- **Chrome DevTools**: Mobile device simulation
- **Real devices**: Physical device testing
- **Performance tools**: GTmetrix, Pingdom
