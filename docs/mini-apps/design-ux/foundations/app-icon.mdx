---
title: "App Icon"
description: "A memorable icon that represents your mini app helps people recognize your app at a glance."
---

![app-icon](/images/design-guidelines/app-icon-overview.png)

### Placements

Your app icon is the first impression users have of your mini app. It appears in the Base App discovery interface and throughout the social experiences.

![app-icon](/images/design-guidelines/app-icon-placements.png)

The following locations show where your app icon will appear in the Base app:
  - Search
  - Home
  - App category
  - Browser header


### Best Practices

**Clarity at Small Sizes**
- Icons should be recognizable at 16x16px
- Avoid fine details that become unclear when scaled down
- Use bold, simple shapes and high contrast

**Brand Recognition**
- Reflect your app's core functionality or brand identity
- Consider how the icon works in both light and dark themes
- Ensure it stands out among other mini apps

**Consistency**
- Maintain visual consistency with your app's overall design language
- Use the same color palette and style as your app interface
- Consider creating variations for different contexts

### Specifications

`iconUrl` in farcaster.json specifies your app’s icon. It should follow the guidelines below to ensure proper display in the Base App:

- **Dimension**:1024x1024px 
- **File Format**: png. 
- No alpha

 ### Other App Metadata

In addition to the app icon, there are other app metadata that determine the first impression of your app. Aim for your metadata to provide helpful context, and properly set expectations with new users on what your app does.

![app-metadata](/images/design-guidelines/metadata-guidelines.png)

Learn more about how to best leverage the app metadata [here](https://docs.base.org/mini-apps/features/embeds-and-previews)

### Testing Your App Metadata

- Use [Base Mini App Preview Tool](https://www.base.dev/preview) to validate your metadata and check how your icon appears.
- Once published on the Base app, check how it renders in different placements.
