---
title: Onchain Interactions
description: Best practices for seamless onchain experiences.
---

### Authentication
Avoid asking users to re-authenticate. Instead, use the account they’ve already connected to Base App to ensure a seamless experience. Learn more [here](http://docs.base.org/mini-apps/features/Authentication).

### Gasless Transactions
Enable users to transact on your apps without needing gas by using wallet solutions that support gas sponsorship like [Base Account and Paymaster](/base-account/improve-ux/sponsor-gas/paymasters).

### Batch Transactions
Where applicable, use batch transactions (EIP-5792) to reduce friction and avoid users needing to sign multiple transactions for a single action.

### Legible Identity
Never display wallet addresses as default usernames. Instead, leverage identity solutions like [ENS](https://docs.ens.domains/web/quickstart/) and [Basenames](/base-account/basenames/basenames-onchainkit-tutorial).
