---
title: "Requirements for Featured Placement"
sidebarTitle: "Requirements"

---
<Info>
  To submit your app for featured placement, fill out the [submission form](https://buildonbase.deform.cc/getstarted/).
</Info>

In order to be eligible for featured placement, your app must meet the requirements outlined in this document. 

## Complete Metadata

Metadata includes your manifest and embed metadata. Complete, valid metadata is required for indexing, category placement, and high‑quality embeds.

**Acceptance Criteria**
- Manifest is publicly accessible at `/.well-known/farcaster.json` 
- Required fields are present and valid (`accountAssociation`, `frame`, `primaryCategory`, `tags`)
- Images meet size/format constraints; text fields respect length limits

**How to Implement**
- Follow the [Manifest guide](/mini-apps/features/manifest)
- Implement [embed metadata](/mini-apps/features/embeds-and-previews#implementation)

<Note>
  [Coming Soon] Validate your manifest using our preview tools at <a href="https://base.dev/preview">base.dev/preview</a>.
</Note>

## In-app Authentication

Users must remain in the Base app throughout the authentication flow. Eliminate flows that bounce users out of the Base app.

**Acceptance Criteria**
- No external redirects
- No email / phone verification
- Users can explore before sign‑in when possible

**How to Implement**
- Follow the [Authentication guide](/mini-apps/features/authentication)
- Prefer in‑app SIWF/Quick Auth or wallet auth; 

## Client-Agnostic

There must be no client‑specific behaviors or wording that degrade the experience in the Base app. You must also ensure that you don't redirect the user to another client for functionality supported in the Base app.

**Acceptance criteria**
- Do not hardcode client‑specific URLs (e.g., Farcaster‑only links)
- Use neutral language in UI (e.g. use "Share to Feed" instead of "Share to Farcaster")
- Eliminate buttons that deeplink to other clients for features supported in the Base app

**How to Implement**
- Update all links according to the [Links](/mini-apps/features/links) guide
- Review the [Base App Compatability](/mini-apps/troubleshooting/base-app-compatibility) guide for functionality not supported in the Base app. All other functionality must keep users in the Base app.

## Gasless

Sponsor transaction fees to remove friction and reduce drop‑off for new users. For mini apps on Base, we recommend using the [Base Paymaster](/onchainkit/paymaster/quickstart-guide).

**Acceptance criteria**
- Transactions are sponsored via a paymaster

**How to Implement**
- Recommended: [Base Paymaster](/onchainkit/paymaster/quickstart-guide)

<Note>
  [Coming Soon] Claim free gas credits on <a href="https://base.dev">base.dev</a>.
</Note>


## Batch Transactions (EIP-5792)

Batch sequential actions where applicable to minimize signatures and reduce friction. Use EIP‑5792 capabilities to send multiple calls in one request.

**Acceptance criteria**
- Where applicable, combine sequential actions into a single batch (e.g. approve + swap)

**How to Implement**
- See [Batch Transactions](/base-account/improve-ux/batch-transactions)
- Provider APIs: [`wallet_sendCalls`](/base-account/reference/core/provider-rpc-methods/wallet_sendCalls), [`wallet_getCapabilities`](/base-account/reference/core/provider-rpc-methods/wallet_getCapabilities)

## Mainstream Ready

Design for mainstream users and assume no prior crypto experience. Use familiar language, avoid crypto jargon, and explain benefits rather than mechanisms. Keep copy clear, simple, and confidence‑building.

**Acceptance criteria**
- Avoid crypto jargon (e.g., “Sign in”, not “Connect Wallet”)
- Display usernames, not wallet addresses unless necessary
- Plain‑language copy that explains benefits, not mechanisms
- Replace client‑specific terms with neutral ones (see Client‑agnostic)

**How to Implement**
- Follow voice/tone in [Best Practices](/mini-apps/design-ux/best-practices)
- Provide context and value props on the first screen

## User onboarding

Use the `context` object to make users instantly feel at home with profile pictures, usernames, and other context data. Help new users orient quickly with a lightweight, **optional** onboarding flow or a clear tutorial CTA on the home screen. Defer authentication until needed (if needed at all); offer a guest mode when possible.

**Acceptance criteria**
- All users can explore the at least part of the app without authenticating
- Lightweight, optional onboarding or prominent tutorial CTA from the homepage
- Display profile pictures, usernames, and other context datas instantly

**How to Implement**
- See [Optimize Onboarding](/mini-apps/growth/optimize-onboarding)
- Provide quick‑start steps or a short guided tour


## Design guidelines

Design for compact, touch‑first contexts with clear primary actions and accessible, responsive layouts. Keep interfaces concise and focused.

**Acceptance criteria**
- Respect mobile safe areas; concise interfaces with clear primary actions
- Legible text, accessible contrast, responsive layouts

**How to Implement**
- Follow [Best Practices](/mini-apps/design-ux/best-practices)
- UI components: [OnchainKit UI](/mini-apps/design-ux/onchainkit)



