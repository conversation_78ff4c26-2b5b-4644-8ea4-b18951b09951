# https://docs.base.org/mini-apps/llms.txt

## Mini Apps Documentation

> Mini Apps are social-native, instant-launch web apps that run inside Base App. This section provides instruction on how to get started with MiniKit and OnchainKit as well as best practices developers should follow. 

## Introduction
- [Overview](https://docs.base.org/mini-apps/overview.md) — What Mini Apps are and why they matter

## Quickstart
- [New Apps: Install](https://docs.base.org/mini-apps/quickstart/new-apps/install.md) — Scaffold a MiniKit project and run it locally
- [Existing Apps: Integrate](https://docs.base.org/mini-apps/quickstart/existing-apps/install.md) — Add MiniKit to an existing Next.js app
- [Launch Cehecklist](https://docs.base.org/mini-apps/quickstart/launch-checklist.md) - Steps to follow to ensure your mini app is best-in-class

## Design Guidelines
- [Best Practices](https://docs.base.org/mini-apps/design-ux/best-practices.md) — UX patterns for social-native apps
- [OnchainKit UI](https://docs.base.org/mini-apps/design-ux/onchainkit.md) — Using OnchainKit components with MiniKit

## Growth Playbook
- [Optimize Onboarding](https://docs.base.org/mini-apps/growth/optimize-onboarding.md) — Reduce friction and increase conversion
- [Build Viral Mini Apps](https://docs.base.org/mini-apps/growth/build-viral-mini-apps.md) — Social distribution patterns

## Features
- [Manifest](https://docs.base.org/mini-apps/features/manifest.md) — Declare capabilities and metadata for discovery
- [Authentication](https://docs.base.org/mini-apps/features/Authentication.md) — Auth flows and secure user identity

## Troubleshooting
- [Common Issues](https://docs.base.org/mini-apps/troubleshooting/common-issues.md) — Fix frequent setup and runtime problems
- [Base App Compatibility](https://docs.base.org/mini-apps/troubleshooting/base-app-compatibility.md) — Base App client-specific behaviors for issues where an app works in the Farcaster client but not Base App

## Technical Reference
- [MiniKit Overview](https://docs.base.org/mini-apps/technical-reference/minikit/overview.md) — Architecture, provider, CLI, and hooks
- [Hooks: useMiniKit](https://docs.base.org/mini-apps/technical-reference/minikit/hooks/useMiniKit.md) — Access frame context and client features

## Optional
- [Search & Discovery](https://docs.base.org/mini-apps/features/search-and-discovery.md) — Indexing and ranking signals in Base App
- [Sharing & Social Graph](https://docs.base.org/mini-apps/features/sharing-and-social-graph.md) — Viral distribution patterns


