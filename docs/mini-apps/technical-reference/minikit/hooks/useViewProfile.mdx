---
title: useViewProfile
description: Navigate to Farcaster profiles from your Mini App
---

Defined in [@coinbase/onchainkit](https://github.com/coinbase/onchainkit)

<Info>
Opens Farcaster user profiles within the host application. Defaults to the current user's profile if no FID is specified.
</Info>

## Parameters

<ParamField body="fid" type="string" optional>
The Farcaster ID of the profile to view. If not provided, defaults to the current user's FID from the MiniKit context.
</ParamField>

## Returns

<ResponseField name="viewProfile" type="() => void">
Function that opens the specified Farcaster profile when called.
</ResponseField>

<RequestExample>
```tsx components/ProfileButton.tsx
import { useViewProfile } from '@coinbase/onchainkit/minikit';

export default function ProfileButton() {
  const viewProfile = useViewProfile(); // Uses current user's FID

  return (
    <button onClick={viewProfile}>
      View My Profile
    </button>
  );
}
```

```tsx components/UserCard.tsx
import { useViewProfile } from '@coinbase/onchainkit/minikit';

export default function UserCard({ userFid, userName }) {
  const viewProfile = useViewProfile(userFid);

  return (
    <div className="user-card">
      <h3>{userName}</h3>
      <button onClick={viewProfile}>
        View Profile
      </button>
    </div>
  );
}
```

```tsx components/Leaderboard.tsx
import { useViewProfile } from '@coinbase/onchainkit/minikit';

export default function Leaderboard({ players }) {
  return (
    <div className="leaderboard">
      <h2>Top Players</h2>
      {players.map((player, index) => (
        <PlayerRow 
          key={player.fid}
          player={player}
          rank={index + 1}
        />
      ))}
    </div>
  );
}

function PlayerRow({ player, rank }) {
  const viewProfile = useViewProfile(player.fid);

  return (
    <div className="player-row">
      <span className="rank">#{rank}</span>
      <span className="name">{player.name}</span>
      <span className="score">{player.score}</span>
      <button 
        onClick={viewProfile}
        className="profile-btn"
      >
        View Profile
      </button>
    </div>
  );
}
```

```tsx components/SocialActions.tsx
import { useViewProfile, useMiniKit } from '@coinbase/onchainkit/minikit';

export default function SocialActions() {
  const { context } = useMiniKit();
  const viewMyProfile = useViewProfile(); // Current user
  const viewHostProfile = useViewProfile(context.client.clientFid); // Host app profile

  return (
    <div className="social-actions">
      <button onClick={viewMyProfile}>
        My Profile
      </button>
      
      <button onClick={viewHostProfile}>
        View {context.client.clientFid === '309857' ? 'Base App' : 'Host'} Profile
      </button>
    </div>
  );
}
```
</RequestExample>

## Usage Patterns

### User Discovery
Enable users to explore profiles of other participants:

```tsx components/ProfileList.tsx
const ProfileList = ({ users }) => {
  return (
    <div className="user-list">
      {users.map(user => (
        <UserProfileCard 
          key={user.fid}
          fid={user.fid}
          name={user.name}
        />
      ))}
    </div>
  );
};

const UserProfileCard = ({ fid, name }) => {
  const viewProfile = useViewProfile(fid);
  
  return (
    <div onClick={viewProfile} className="profile-card">
      <h4>{name}</h4>
      <span>FID: {fid}</span>
    </div>
  );
};
```

### Social Gaming
Connect players in multiplayer experiences:

```tsx components/GameLobby.tsx
const GameLobby = ({ players }) => {
  return (
    <div className="game-lobby">
      <h3>Players in Game</h3>
      {players.map(player => (
        <PlayerChip key={player.fid} player={player} />
      ))}
    </div>
  );
};

const PlayerChip = ({ player }) => {
  const viewProfile = useViewProfile(player.fid);
  
  return (
    <div className="player-chip" onClick={viewProfile}>
      {player.username}
    </div>
  );
};
```

### Creator Attribution
Link to content creators and collaborators:

```tsx components/ContentAttribution.tsx
const ContentAttribution = ({ creator }) => {
  const viewCreatorProfile = useViewProfile(creator.fid);
  
  return (
    <div className="attribution">
      <span>Created by</span>
      <button 
        onClick={viewCreatorProfile}
        className="creator-link"
      >
        {creator.name}
      </button>
    </div>
  );
};
```

## Best Practices

### User Experience
- **Clear call-to-action**: Use descriptive button text like "View Profile" or user names
- **Visual feedback**: Indicate clickable profile elements with appropriate styling
- **Context awareness**: Show relevant profile actions based on the user's relationship

### Performance Optimization
- **Memoize profile handlers**: Use the same hook instance for the same FID
- **Batch profile data**: Load profile information efficiently when displaying multiple users

```tsx components/ProfileActions.tsx
import { useMemo } from 'react';

const ProfileActions = ({ userFid }) => {
  const viewProfile = useViewProfile(userFid);
  
  // Memoize to avoid recreating the handler
  const handleProfileView = useMemo(() => viewProfile, [viewProfile]);
  
  return (
    <button onClick={handleProfileView}>
      View Profile
    </button>
  );
};
```

### Accessibility
- **Keyboard navigation**: Ensure profile links are keyboard accessible
- **Screen reader support**: Use semantic HTML and ARIA labels
- **Focus management**: Handle focus appropriately when returning from profile views

```tsx components/AccessibleProfileLink.tsx
const AccessibleProfileLink = ({ fid, userName }) => {
  const viewProfile = useViewProfile(fid);
  
  return (
    <button 
      onClick={viewProfile}
      aria-label={`View ${userName}'s Farcaster profile`}
    >
      {userName}
    </button>
  );
};
```

<Info>
Profile viewing behavior may vary between Farcaster clients. In Base App, profiles open within the app context. In other clients, the experience may differ based on their implementation.
</Info>

<Warning>
Always validate FIDs before passing them to `useViewProfile`. Invalid FIDs may cause errors or unexpected behavior in the host application.
</Warning>


