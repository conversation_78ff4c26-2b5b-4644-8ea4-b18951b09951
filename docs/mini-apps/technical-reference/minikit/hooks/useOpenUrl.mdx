---
title: useOpenUrl
description: Open URLs within or outside the frame context
---

Defined in [@coinbase/onchainkit](https://github.com/coinbase/onchainkit)

<Info>
Opens URLs in the appropriate context - either within the Mini App environment or in external browsers. Handles cross-client compatibility automatically.
</Info>

## Returns

<ResponseField name="openUrl" type="(url: string) => void">
Function that opens the specified URL. Behavior depends on the URL type and client capabilities.

<ParamField body="url" type="string" required>
The URL to open. Can be http/https links, deep links, or other URI schemes.
</ParamField>
</ResponseField>

<RequestExample>
```tsx components/ExternalLinks.tsx
import { useOpenUrl } from '@coinbase/onchainkit/minikit';

export default function ExternalLinks() {
  const openUrl = useOpenUrl();

  return (
    <div className="external-links">
      <button onClick={() => openUrl('https://base.org')}>
        Visit Base.org
      </button>
      <button onClick={() => openUrl('https://twitter.com/base')}>
        Follow on Twitter
      </button>
      <button onClick={() => openUrl('https://discord.gg/basechain')}>
        Join Discord
      </button>
    </div>
  );
}
```

```tsx components/SmartLink.tsx
import { useOpenUrl } from '@coinbase/onchainkit/minikit';

export default function SmartLink({ href, children, className }) {
  const openUrl = useOpenUrl();

  const handleClick = (e) => {
    e.preventDefault();
    openUrl(href);
  };

  return (
    <a 
      href={href} 
      onClick={handleClick}
      className={className}
    >
      {children} ↗
    </a>
  );
}
```
</RequestExample>

<Warning>
URLs opened with `useOpenUrl` may open in different contexts depending on the client. Don't rely on specific opening behavior for critical functionality.
</Warning>

<Info>
`useOpenUrl` automatically handles cross-client compatibility and provides the best user experience for external navigation.
</Info>


