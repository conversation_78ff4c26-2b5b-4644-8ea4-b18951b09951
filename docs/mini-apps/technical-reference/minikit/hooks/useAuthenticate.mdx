---
title: useAuthenticate
description: Cryptographically authenticate users with Sign In with Farcaster
---

Defined in [@coinbase/onchainkit](https://github.com/coinbase/onchainkit)

<Info>
Provides cryptographically secure user authentication using Sign In with Farcaster (SIWF). Returns verified user identity and signature for secure operations.
</Info>

## Returns

<ResponseField name="user" type="AuthenticatedUser | null">
Authenticated user object with verified identity, or null if not authenticated.

<Expandable title="AuthenticatedUser properties">
<ResponseField name="fid" type="string">
Verified Farcaster ID of the authenticated user.
</ResponseField>

<ResponseField name="signature" type="string">
Cryptographic signature proving user identity.
</ResponseField>

<ResponseField name="message" type="string">
The signed message used for authentication.
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="authenticate" type="() => Promise<AuthenticatedUser | null>">
Function to trigger the authentication flow. Returns verified user data on success.
</ResponseField>

<RequestExample>
```tsx components/AuthButton.tsx
import { useAuthenticate } from '@coinbase/onchainkit/minikit';
import { useState } from 'react';

export default function AuthButton() {
  const { user, authenticate } = useAuthenticate();
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const handleAuth = async () => {
    setIsAuthenticating(true);
    try {
      const authenticatedUser = await authenticate();
      if (authenticatedUser) {
        console.log('Authenticated user:', authenticatedUser.fid);
        // Save to your backend
        await saveUserSession(authenticatedUser);
      }
    } catch (error) {
      console.error('Authentication failed:', error);
    } finally {
      setIsAuthenticating(false);
    }
  };

  if (user) {
    return (
      <div>
        <p>✅ Authenticated as FID: {user.fid}</p>
        <button onClick={() => window.location.reload()}>
          Sign Out
        </button>
      </div>
    );
  }

  return (
    <button 
      onClick={handleAuth}
      disabled={isAuthenticating}
    >
      {isAuthenticating ? 'Authenticating...' : 'Sign In with Farcaster'}
    </button>
  );
}
```

```tsx components/ProtectedFeature.tsx  
import { useAuthenticate } from '@coinbase/onchainkit/minikit';

export default function ProtectedFeature() {
  const { user, authenticate } = useAuthenticate();

  if (!user) {
    return (
      <div className="auth-required">
        <h3>Authentication Required</h3>
        <p>Please sign in to access this feature</p>
        <button onClick={authenticate}>
          Sign In with Farcaster
        </button>
      </div>
    );
  }

  return (
    <div className="protected-content">
      <h3>Welcome, {user.fid}!</h3>
      <p>This is a protected feature only available to authenticated users.</p>
    </div>
  );
}
```
</RequestExample>


### Context vs Authentication
Use the right tool for the job:

```tsx components/SecurityExample.tsx
import { useAuthenticate, useMiniKit } from '@coinbase/onchainkit/minikit';

export default function SecurityExample() {
  const { user } = useAuthenticate(); // For security
  const { context } = useMiniKit();   // For UX 

  return (
    <div>
      {/* Safe: UX personalization with context */}
      {context.user.fid && (
        <p>Hi there, user {context.user.fid}!</p>
      )}
      
      {/* Safe: Security with authentication */}
      {user && (
        <SecureUserDashboard verifiedFid={user.fid} />
      )}
    </div>
  );
}
```



<Info>
`useAuthenticate` provides cryptographic proof of user identity. Always verify signatures server-side for security-critical operations. Use `useMiniKit` context only for UX hints and analytics.
</Info>


