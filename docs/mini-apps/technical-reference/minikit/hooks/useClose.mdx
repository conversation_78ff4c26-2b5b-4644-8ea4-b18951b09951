---
title: useClose
description: Programmatically close the Mini App frame
---

Defined in [@coinbase/onchainkit](https://github.com/coinbase/onchainkit)

<Info>
Allows Mini Apps to close themselves programmatically. Useful for completion flows, cancellation actions, or after successful operations.
</Info>

## Returns

<ResponseField name="close" type="() => void">
Function that closes the Mini App frame and returns the user to the host application.
</ResponseField>

<RequestExample>
```tsx components/CloseButton.tsx
import { useClose } from '@coinbase/onchainkit/minikit';

export default function CloseButton() {
  const close = useClose();

  return (
    <button onClick={close} className="close-btn">
      ✕ Close
    </button>
  );
}
```

```tsx components/PurchaseFlow.tsx
import { useClose } from '@coinbase/onchainkit/minikit';
import { useState } from 'react';

export default function PurchaseFlow() {
  const close = useClose();
  const [isComplete, setIsComplete] = useState(false);

  const handlePurchase = async () => {
    try {
      await processPurchase();
      setIsComplete(true);
      
      // Auto-close after successful purchase
      setTimeout(() => {
        close();
      }, 2000);
    } catch (error) {
      console.error('Purchase failed:', error);
    }
  };

  if (isComplete) {
    return (
      <div className="success-screen">
        <h2>✅ Purchase Complete!</h2>
        <p>Closing in 2 seconds...</p>
        <button onClick={close}>Close Now</button>
      </div>
    );
  }

  return (
    <div className="purchase-flow">
      <button onClick={handlePurchase}>Complete Purchase</button>
      <button onClick={close}>Cancel</button>
    </div>
  );
}
```

```tsx components/ConfirmClose.tsx
import { useClose } from '@coinbase/onchainkit/minikit';
import { useState } from 'react';

export default function ConfirmClose() {
  const close = useClose();
  const [showConfirm, setShowConfirm] = useState(false);

  const handleCloseRequest = () => {
    setShowConfirm(true);
  };

  const confirmClose = () => {
    // Save any pending data
    savePendingChanges();
    close();
  };

  if (showConfirm) {
    return (
      <div className="confirm-dialog">
        <h3>Close Mini App?</h3>
        <p>Any unsaved changes will be lost.</p>
        <div className="actions">
          <button onClick={confirmClose}>Yes, Close</button>
          <button onClick={() => setShowConfirm(false)}>Cancel</button>
        </div>
      </div>
    );
  }

  return (
    <div className="app-content">
      <button onClick={handleCloseRequest}>Exit</button>
      {/* Your app content */}
    </div>
  );
}
```
</RequestExample>

## Usage Patterns

### Completion Flows
Close automatically after successful operations:

```tsx components/CompletionFlow.tsx
const handleGameComplete = async () => {
  await saveScore(finalScore);
  
  // Show completion screen briefly
  setShowCompletion(true);
  
  // Auto-close after celebration
  setTimeout(close, 3000);
};
```

### Navigation Replacement
Use close instead of navigation for simple flows:

```tsx components/NavigationReplacement.tsx
// Instead of navigating back, close the frame
const handleCancel = () => {
  if (hasUnsavedChanges) {
    confirmAndClose();
  } else {
    close();
  }
};
```

### Error Recovery
Provide escape routes for error states:

```tsx components/ErrorScreen.tsx
if (hasUnrecoverableError) {
  return (
    <div className="error-screen">
      <h2>Something went wrong</h2>
      <p>Please try again later</p>
      <button onClick={close}>Close</button>
    </div>
  );
}
```

## Best Practices

### User Experience
- **Confirm important actions**: Ask before closing if user has unsaved work
- **Provide feedback**: Show completion states before auto-closing
- **Quick escape**: Always provide a way to close, especially in error states

### Technical Considerations
- **Save state**: Persist important data before closing
- **Clean up**: Cancel ongoing requests or timers
- **Analytics**: Track close events for UX insights

```tsx components/HandleClose.tsx
const handleClose = () => {
  // Clean up
  cancelPendingRequests();
  clearIntervals();
  
  // Track analytics
  analytics.track('mini_app_closed', {
    session_duration: Date.now() - sessionStart,
    completion_state: currentState
  });
  
  // Close
  close();
};
```

<Info>
`useClose` provides a clean exit for Mini Apps. Use it thoughtfully to create polished user experiences that feel native to the host application flow.
</Info>


