---
title: useMiniKit
description: Access frame context and control readiness state
---

Defined in [@coinbase/onchainkit](https://github.com/coinbase/onchainkit)

<Info>
Primary hook for accessing MiniKit frame context and managing frame readiness. This hook provides essential Mini App state and user context information.
</Info>

## Returns

<ResponseField name="context" type="MiniKitContext">
Frame context information provided by the host application.

<Expandable title="MiniKitContext properties">
<ResponseField name="user" type="object">
User information from the host application.

<Expandable title="User properties">
<ResponseField name="fid" type="string">
Farcaster ID of the current user. Can be spoofed - use for analytics only.
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="client" type="object">
Information about the host client application.

<Expandable title="Client properties">
<ResponseField name="added" type="boolean">
Whether the user has saved this Mini App to their collection.
</ResponseField>

<ResponseField name="clientFid" type="string">
Farcaster ID of the host client. Base App: "309857".
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="location" type="string">
Where the Mini App was launched from (e.g., "cast", "launcher", "notification").
</ResponseField>
</Expandable>
</ResponseField>

<ResponseField name="isFrameReady" type="boolean">
Whether the frame has signaled readiness to the host application.
</ResponseField>

<ResponseField name="setFrameReady" type="() => void">
Function to signal frame readiness to the host application. Call this once your Mini App has finished loading.
</ResponseField>

<RequestExample>
```tsx app/App.tsx
'use client';

import { useMiniKit } from '@coinbase/onchainkit/minikit';
import { useEffect } from 'react';

export default function MyMiniApp() {
  const { context, isFrameReady, setFrameReady } = useMiniKit();

  useEffect(() => {
    if (!isFrameReady) {
      setFrameReady();
    }
  }, [setFrameReady, isFrameReady]);

  return (
    <div>
      <h1>Welcome, User {context.user.fid}!</h1>
      <p>Launched from: {context.location}</p>
      {context.client.added && (
        <p>✅ You've saved this app!</p>
      )}
    </div>
  );
}
```

```tsx components/ClientSpecificFeatures.tsx
import { useMiniKit } from '@coinbase/onchainkit/minikit';

export default function ClientSpecificFeatures() {
  const { context } = useMiniKit();
  
  const isBaseApp = context.client.clientFid === '309857';
  const isFarcaster = context.client.clientFid === '1';

  return (
    <div>
      {isBaseApp && (
        <div>Base App specific features</div>
      )}
      {isFarcaster && (
        <div>Farcaster specific features</div>
      )}
    </div>
  );
}
```

```tsx components/AnalyticsTracker.tsx
import { useMiniKit } from '@coinbase/onchainkit/minikit';
import { useEffect } from 'react';

export default function AnalyticsTracker() {
  const { context } = useMiniKit();

  useEffect(() => {
    // ✅ Safe: Use context for analytics
    analytics.track('mini_app_opened', {
      userFid: context.user.fid,
      client: context.client.clientFid,
      launchLocation: context.location,
      hasAddedApp: context.client.added
    });
  }, [context]);

  return <div>App content...</div>;
}
```
</RequestExample>

## Usage Notes

### Frame Readiness
Always call `setFrameReady()` once your Mini App has finished initial loading:

```tsx components/FrameReady.tsx
useEffect(() => {
  if (!isFrameReady) {
    setFrameReady();
  }
}, [setFrameReady, isFrameReady]);
```

### Context Data Security

<Warning>
Context data can be spoofed by malicious actors. Never use context data for authentication or security-critical operations. Use `useAuthenticate` for verified user identity.
</Warning>

```tsx
// ❌ Don't use for authentication
const isAuthenticated = !!context.user.fid; // Can be spoofed!

// ✅ Use for analytics and UX hints only
const userHint = context.user.fid; // For analytics tracking
```

### Client Detection
Use client detection to provide platform-specific experiences:

```tsx components/ClientDetection.tsx
const isBaseApp = context.client.clientFid === '309857';
const isFarcaster = context.client.clientFid === '1';

if (isBaseApp) {
  // Enable Base App specific features
}
```

<Info>
The `useMiniKit` hook must be used within a component that's wrapped by `MiniKitProvider`. This hook provides the foundation for all Mini App functionality and should be one of the first hooks you use in your application.
</Info>


