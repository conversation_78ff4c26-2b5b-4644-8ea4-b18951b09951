---
title: useViewCast
description: View a cast by its hash from your Mini App
---

Defined in [@coinbase/onchainkit](https://github.com/coinbase/onchainkit)

<Info>
Opens a specific Farcaster cast within the host application using the cast's unique hash identifier.
</Info>

## Parameters

<ParamField body="hash" type="string" required>
The unique hash identifier of the cast to view. This is the cryptographic hash that uniquely identifies a cast on the Farcaster network.
</ParamField>

## Returns

<ResponseField name="viewCast" type="() => void">
Function that opens the specified cast when called.
</ResponseField>

<RequestExample>
```tsx components/CastReference.tsx
import { useViewCast } from '@coinbase/onchainkit/minikit';

export default function CastReference({ castHash, castAuthor }) {
  const viewCast = useViewCast(castHash);

  return (
    <div className="cast-reference">
      <p>Referenced cast by {castAuthor}</p>
      <button onClick={viewCast}>
        View Original Cast
      </button>
    </div>
  );
}
```

```tsx components/GameAchievement.tsx
import { useViewCast, useComposeCast } from '@coinbase/onchainkit/minikit';
import { useState } from 'react';

export default function GameAchievement({ achievement }) {
  const [sharedCastHash, setSharedCastHash] = useState(null);
  
  const composeCast = useComposeCast();
  const viewSharedCast = useViewCast(sharedCastHash);

  const shareAchievement = () => {
    composeCast({
      text: `🎮 Just unlocked "${achievement.name}" in this Mini App! Can you beat my score?`,
      embeds: [window.location.href]
    });
    
    // Store cast hash when user shares (this would come from your backend)
    // setSharedCastHash(returnedCastHash);
  };

  return (
    <div className="achievement-card">
      <h3>🏆 {achievement.name}</h3>
      <p>{achievement.description}</p>
      
      <div className="actions">
        <button onClick={shareAchievement}>
          Share Achievement
        </button>
        
        {sharedCastHash && (
          <button onClick={viewSharedCast}>
            View My Share
          </button>
        )}
      </div>
    </div>
  );
}
```

```tsx components/ThreadNavigation.tsx
import { useViewCast } from '@coinbase/onchainkit/minikit';

export default function ThreadNavigation({ parentCastHash, replyCastHashes }) {
  const viewParentCast = useViewCast(parentCastHash);

  return (
    <div className="thread-navigation">
      <div className="parent-cast">
        <h4>Original Discussion</h4>
        <button onClick={viewParentCast}>
          View Parent Cast
        </button>
      </div>
      
      <div className="replies">
        <h4>Replies in Thread</h4>
        {replyCastHashes.map((hash, index) => (
          <ReplyItem key={hash} castHash={hash} replyNumber={index + 1} />
        ))}
      </div>
    </div>
  );
}

function ReplyItem({ castHash, replyNumber }) {
  const viewCast = useViewCast(castHash);
  
  return (
    <button onClick={viewCast} className="reply-item">
      Reply #{replyNumber}
    </button>
  );
}
```

```tsx Community Highlights
import { useViewCast } from '@coinbase/onchainkit/minikit';

export default function CommunityHighlights({ featuredCasts }) {
  return (
    <div className="community-highlights">
      <h2>Community Highlights</h2>
      <div className="featured-casts">
        {featuredCasts.map(cast => (
          <FeaturedCast key={cast.hash} cast={cast} />
        ))}
      </div>
    </div>
  );
}

function FeaturedCast({ cast }) {
  const viewCast = useViewCast(cast.hash);
  
  return (
    <div className="featured-cast-card">
      <div className="cast-preview">
        <h4>{cast.author}</h4>
        <p>{cast.preview}</p>
        <span className="engagement">{cast.likes} likes · {cast.recasts} recasts</span>
      </div>
      
      <button onClick={viewCast} className="view-cast-btn">
        View Full Cast
      </button>
    </div>
  );
}
```
</RequestExample>

## Usage Patterns

### Content Attribution
Reference original casts that inspired your Mini App content:

```tsx components/AttributionFooter.tsx
const AttributionFooter = ({ originalCastHash, authorName }) => {
  const viewOriginalCast = useViewCast(originalCastHash);
  
  return (
    <footer className="content-attribution">
      <span>Inspired by cast from {authorName}</span>
      <button onClick={viewOriginalCast}>
        View Original
      </button>
    </footer>
  );
};
```

### Discussion Threading
Navigate cast conversations and replies:

```tsx components/DiscussionThread.tsx
const DiscussionThread = ({ threadCasts }) => {
  return (
    <div className="discussion-thread">
      {threadCasts.map((cast, index) => (
        <ThreadItem 
          key={cast.hash}
          cast={cast}
          isRoot={index === 0}
        />
      ))}
    </div>
  );
};

const ThreadItem = ({ cast, isRoot }) => {
  const viewCast = useViewCast(cast.hash);
  
  return (
    <div className={`thread-item ${isRoot ? 'root' : 'reply'}`}>
      <span>{cast.author}: {cast.preview}</span>
      <button onClick={viewCast}>View</button>
    </div>
  );
};
```

### Social Proof
Showcase community engagement with your Mini App:

```tsx components/SocialProof.tsx
const SocialProof = ({ testimonialCasts }) => {
  return (
    <section className="social-proof">
      <h3>What Users Are Saying</h3>
      {testimonialCasts.map(cast => (
        <TestimonialCard key={cast.hash} cast={cast} />
      ))}
    </section>
  );
};

const TestimonialCard = ({ cast }) => {
  const viewCast = useViewCast(cast.hash);
  
  return (
    <div className="testimonial" onClick={viewCast}>
      <blockquote>"{cast.text}"</blockquote>
      <cite>— {cast.author}</cite>
    </div>
  );
};
```

## Best Practices

### Hash Validation
Always validate cast hashes before using them:

```tsx components/SafeCastViewer.tsx
const validateCastHash = (hash) => {
  // Cast hashes are typically 40-character hexadecimal strings
  return /^0x[a-fA-F0-9]{40}$/.test(hash);
};

const SafeCastViewer = ({ castHash }) => {
  const viewCast = useViewCast(castHash);
  
  const handleViewCast = () => {
    if (validateCastHash(castHash)) {
      viewCast();
    } else {
      console.error('Invalid cast hash:', castHash);
    }
  };
  
  return (
    <button onClick={handleViewCast}>
      View Cast
    </button>
  );
};
```

### Performance Optimization
Cache cast metadata to avoid repeated lookups:

```tsx components/CastLibrary.tsx
import { useMemo } from 'react';

const CastLibrary = ({ castHashes }) => {
  const sortedCasts = useMemo(() => {
    return castHashes.sort((a, b) => {
      // Sort by timestamp or other criteria
      return a.timestamp - b.timestamp;
    });
  }, [castHashes]);
  
  return (
    <div className="cast-library">
      {sortedCasts.map(cast => (
        <CastItem key={cast.hash} castHash={cast.hash} />
      ))}
    </div>
  );
};
```

### User Experience
Provide context about the cast before opening:

```tsx components/ContextualCastLink.tsx
const ContextualCastLink = ({ castHash, context }) => {
  const viewCast = useViewCast(castHash);
  
  return (
    <div className="contextual-cast-link">
      <p className="context">{context}</p>
      <button onClick={viewCast} className="cast-link">
        View Related Cast →
      </button>
    </div>
  );
};
```

<Warning>
Cast hashes must be valid Farcaster cast identifiers. Invalid hashes may cause errors or unexpected behavior in the host application.
</Warning>

<Info>
Viewing casts provides a seamless way to reference Farcaster content from your Mini App. Use this for community highlights, content attribution, and threading discussions related to your app.
</Info>


