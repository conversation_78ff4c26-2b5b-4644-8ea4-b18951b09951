---
title: useComposeCast
description: Open the cast composer with prefilled content
---

Defined in [@coinbase/onchainkit](https://github.com/coinbase/onchainkit)

<Info>
Opens the native cast composer with prefilled text and embeds. Essential for viral growth and social sharing within Mini Apps.
</Info>

## Returns

<ResponseField name="composeCast" type="(params: ComposeCastParams) => void">
Function that opens the cast composer with specified content.

<Expandable title="ComposeCastParams properties">
<ParamField body="text" type="string" required>
The text content to prefill in the composer. Keep concise and engaging.
</ParamField>

<ParamField body="embeds" type="string[]">
Array of URLs to embed in the cast. Usually includes your Mini App URL for sharing.
</ParamField>
</Expandable>
</ResponseField>

<RequestExample>
```tsx Basic Text Sharing
import { useComposeCast } from '@coinbase/onchainkit/minikit';

export default function ShareButton() {
  const { composeCast } = useComposeCast();

  const handleShare = () => {
    composeCast({
      text: 'Just completed the daily puzzle! 🧩'
    });
  };

  return (
    <button onClick={handleShare}>
      Share Achievement
    </button>
  );
}
```

```tsx Share with App Embed
import { useComposeCast } from '@coinbase/onchainkit/minikit';

export default function ShareAppButton() {
  const { composeCast } = useComposeCast();

  const handleShareApp = () => {
    composeCast({
      text: 'Check out this amazing Mini App!',
      embeds: [window.location.href]
    });
  };

  return (
    <button onClick={handleShareApp}>
      Share Mini App
    </button>
  );
}
```

```tsx Strategic Achievement Sharing
import { useComposeCast } from '@coinbase/onchainkit/minikit';

export default function GameComplete({ score, level }) {
  const { composeCast } = useComposeCast();

  const shareAchievement = () => {
    composeCast({
      text: `🎉 Just hit level ${level} with ${score} points!`,
      embeds: [
        window.location.href,
        'https://yourgame.com/achievements/' + achievementId
      ]
    });
  };

  const shareGameInvite = () => {
    composeCast({
      text: 'Want to challenge me? Try to beat my high score! 🏆',
      embeds: [window.location.href]
    });
  };

  return (
    <div className="achievement-share">
      <h2>Congratulations! 🎉</h2>
      <p>Level {level} completed with {score} points</p>
      
      <div className="share-options">
        <button onClick={shareAchievement}>
          Share Achievement
        </button>
        <button onClick={shareGameInvite}>
          Challenge Friends
        </button>
      </div>
    </div>
  );
}
```

```tsx Dynamic Content Sharing
import { useComposeCast } from '@coinbase/onchainkit/minikit';
import { useState } from 'react';

export default function CustomShareDialog() {
  const { composeCast } = useComposeCast();
  const [shareText, setShareText] = useState('');

  const handleCustomShare = () => {
    if (!shareText.trim()) return;
    
    composeCast({
      text: shareText,
      embeds: [window.location.href]
    });
    
    // Clear after sharing
    setShareText('');
  };

  return (
    <div className="share-dialog">
      <textarea 
        value={shareText}
        onChange={(e) => setShareText(e.target.value)}
        placeholder="What would you like to share?"
        maxLength={280}
      />
      
      <div className="share-actions">
        <span>{280 - shareText.length} characters remaining</span>
        <button 
          onClick={handleCustomShare}
          disabled={!shareText.trim()}
        >
          Share Cast
        </button>
      </div>
    </div>
  );
}
```
</RequestExample>

## Strategic Sharing Patterns

### Achievement Moments
Share at moments of user accomplishment:

```tsx examples/AchievementMoments.tsx
// After quiz completion
composeCast({
  text: "I'm a Ravenclaw! 🦅 What house are you?",
  embeds: [quizUrl]
});

// After NFT mint
composeCast({
  text: "Just minted my first collectible! 🎨",
  embeds: [mintUrl, nftImageUrl]
});

// After game milestone
composeCast({
  text: "Finally beat level 50! This game is addictive 🎮",
  embeds: [gameUrl]
});
```

### Viral Growth Mechanics
Design shares that encourage interaction:

```tsx examples/ViralGrowthMechanics.tsx
// Challenge pattern
composeCast({
  text: "Beat my time of 2:34 if you can! ⏱️",
  embeds: [challengeUrl]
});

// Social proof pattern  
composeCast({
  text: "Join 50,000+ players already playing!",
  embeds: [gameUrl]
});

// FOMO pattern
composeCast({
  text: "Limited edition drop ends in 2 hours! 🔥",
  embeds: [dropUrl]
});
```

### Content Personalization
Customize shares based on user activity:

```tsx examples/ContentPersonalization.tsx
import { useMiniKit, useComposeCast } from '@coinbase/onchainkit/minikit';

export default function PersonalizedShare() {
  const { context } = useMiniKit();
  const { composeCast } = useComposeCast();
  
  const sharePersonalized = (achievement) => {
    const isNewUser = !context.client.added;
    
    const text = isNewUser 
      ? `Just discovered this amazing ${achievement.category} app!`
      : `Another ${achievement.type} completed! ${achievement.streak} day streak 🔥`;
      
    composeCast({
      text,
      embeds: [window.location.href]
    });
  };

  return (
    <button onClick={() => sharePersonalized(userAchievement)}>
      Share Progress
    </button>
  );
}
```

## Best Practices

### Text Content
- **Keep it concise**: Farcaster has character limits
- **Include emotional context**: Use emojis and excitement
- **Add clear value**: Explain why others should care
- **Include call-to-action**: "Try it yourself", "Beat my score"

### Embed Strategy
- **Always include your app URL** for discoverability
- **Add relevant media**: Images, videos, other content
- **Test embed rendering**: Ensure metadata displays correctly

### Timing Optimization
- **Post-achievement**: When users feel accomplished
- **Social moments**: When friends are likely online  
- **Value demonstration**: After showing app benefits
- **Avoid interruption**: Don't break user flow

<Warning>
The composer opens in a native overlay or new window depending on the client. Users can modify the text before posting, so don't rely on exact text for tracking. Use URL parameters or unique embeds for attribution tracking.
</Warning>

<Info>
`useComposeCast` is one of the most powerful hooks for viral growth. Strategic implementation of sharing at the right moments can significantly increase your Mini App's reach and user acquisition.
</Info>


