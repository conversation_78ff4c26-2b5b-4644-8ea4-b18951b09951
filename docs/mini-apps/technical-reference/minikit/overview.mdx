---
title: Overview
description: The best way to build Mini Apps on Base.
---

MiniKit is the official SDK for building Mini Apps that work seamlessly across Base App and other Farcaster clients. It provides React hooks, context management, and wallet integration that eliminates the complexity of building social-native applications.

## Why MiniKit?

<CardGroup cols={2}>
<Card title="Seamless Integration" icon="link">
Part of OnchainKit allowing for complete onchain app development with shared providers and configuration.
</Card>

<Card title="Social-Native" icon="users">
Built specifically for Farcaster's frame-based architecture with native social features.
</Card>

<Card title="Wallet Abstraction" icon="wallet">
Simplified wallet connections and transaction flows with automatic provider detection.
</Card>

<Card title="Cross-Client" icon="mobile">
Works across Base App, Farcaster, and other clients with consistent behavior.
</Card>
</CardGroup>

## Architecture

MiniKit consists of three main components:

### `<OnchainKitProvider miniKit={{ enabled: true }}>`
React context provider that configures:
- Wagmi and react-query setup
- Chain and API key configuration
- Theme and appearance settings
- Farcaster connector integration

### Hooks
React hooks for frame interactions:
- **Frame Management**: `useMiniKit`, `useAddFrame`, `useClose`
- **Navigation**: `useOpenUrl`, `useViewProfile`, `useViewCast`
- **Social**: `useComposeCast`, `useNotification`
- **Authentication**: `useAuthenticate`
- **UI**: `usePrimaryButton`

### CLI Tools
Command-line utilities for:
- Project scaffolding with `npx create-onchain --mini`
- Manifest generation with `npx create-onchain --manifest`
- Development workflow automation

## Getting Started

<CardGroup cols={2}>
<Card title="New Project" icon="plus" href="/mini-apps/quickstart/new-apps/install">
Create a new Mini App from scratch using the CLI scaffolding tool.
</Card>

<Card title="Existing App" icon="code" href="/mini-apps/quickstart/existing-apps/install">
Add MiniKit to your existing Next.js application.
</Card>
</CardGroup>

## Key Concepts

### Frame Lifecycle
Mini Apps run within Farcaster frames and must signal readiness:
1. **Initialize** MiniKitProvider in your app root
2. **Signal readiness** with `setFrameReady()` in your main component
3. **Handle interactions** through MiniKit hooks

### Context vs Authentication
- **Context data** (`useMiniKit().context`) provides user info but can be spoofed
- **Authentication** (`useAuthenticate()`) provides cryptographically verified user identity
- Always use authentication for security-critical operations

### Cross-Client Compatibility
MiniKit automatically detects the client environment and adapts:
- **Base App**: Native wallet integration, enhanced features
- **Farcaster**: Standard Farcaster protocol compliance
- **Other clients**: Graceful fallbacks and compatibility modes

## Technical Reference

<CardGroup cols={2}>
<Card title="Provider & Initialization" icon="settings" href="/mini-apps/technical-reference/minikit/provider-and-initialization">
Configure MiniKitProvider and initialize frame context.
</Card>

<Card title="Hooks Reference" icon="code" href="/mini-apps/technical-reference/minikit/hooks/useMiniKit">
Complete reference for all MiniKit React hooks.
</Card>
</CardGroup>