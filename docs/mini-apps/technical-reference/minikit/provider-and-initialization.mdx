---
title: Provider & Initialization
description: Configure OnchainKitProvider and initialize frame context
---

## OnchainKitProvider Setup

The OnchainKitProvider wraps your app and provides MiniKit context to all child components. It configures wagmi, react-query, and the Farcaster connector automatically.

```tsx app/providers.tsx
import { OnchainKitProvider } from '@coinbase/onchainkit';
import { ReactNode } from 'react';
import { base } from 'wagmi/chains';

export function Providers({ children }: { children: ReactNode }) {
  return (
    <OnchainKitProvider
      apiKey={process.env.NEXT_PUBLIC_ONCHAINKIT_API_KEY}
      chain={base}
      config={{
        appearance: {
          mode: 'auto',
          theme: 'default',
          name: process.env.NEXT_PUBLIC_ONCHAINKIT_PROJECT_NAME,
          logo: process.env.NEXT_PUBLIC_ICON_URL,
        },
      }}
      miniKit={{
        enabled: true
      }}
    >
      {children}
    </OnchainKitProvider>
  );
}
```

<Tip>
The provider configures wagmi and react-query and uses the Farcaster connector when available.
</Tip>

## Provider Configuration

### Required Props

| Prop | Type | Description |
|------|------|-------------|
| `apiKey` | string | Your OnchainKit API key from Coinbase Developer Platform |
| `chain` | Chain | The blockchain network (typically `base`) |

### Optional Configuration

| Property | Type | Description |
|----------|------|-------------|
| `config.appearance.mode` | 'auto' \| 'light' \| 'dark' | Theme mode for UI components |
| `config.appearance.theme` | string | Theme name for styling |
| `config.appearance.name` | string | App name displayed in UI |
| `config.appearance.logo` | string | Logo URL for branding |

## Frame Initialization

Initialize MiniKit in your main component to signal frame readiness:

```tsx app/App.tsx
'use client';

import { useMiniKit } from '@coinbase/onchainkit/minikit';
import { useEffect } from 'react';

export default function App() {
  const { setFrameReady, isFrameReady } = useMiniKit();

  useEffect(() => {
    if (!isFrameReady) {
      setFrameReady();
    }
  }, [setFrameReady, isFrameReady]);

  return (
    <div>
      {/* Your app content */}
    </div>
  );
}
```

## useMiniKit Hook

The `useMiniKit` hook provides access to frame state and user context:

```tsx any-component.tsx
const { 
  setFrameReady, 
  isFrameReady, 
  context 
} = useMiniKit();
```

### Context Properties

| Property | Type | Description |
|----------|------|-------------|
| `context.user.fid` | string | Farcaster ID of the current user |
| `context.client.added` | boolean | Whether user has saved the Mini App |
| `context.location` | string | Where the Mini App was launched from |

<Warning>
Context data can be spoofed and should not be used for authentication. Use `useAuthenticate` for secure user verification.
</Warning>


