/* Danger admonition coloring */

.danger-admonition {
  border: 1px solid rgba(239, 68, 68, 0.2);
  background-color: rgba(254, 242, 242, 0.5);
}

.dark\:danger-admonition:is(.dark *) {
  border-color: rgba(239, 68, 68, 0.3);
  background-color: rgba(239, 68, 68, 0.1);
}

.assistant-entry {
  background-color: #3c8aff !important;
  color: #ffffff !important;
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  box-shadow: none !important;
  font-weight: 600 !important;
}

.assistant-entry:hover {
  transform: translateY(-1px) !important;
  box-shadow: none !important;
}

.assistant-entry:focus-visible {
  outline: 2px solid rgba(0, 0, 0, 0.25) !important;
  outline-offset: 2px !important;
}

.dark .assistant-entry {
  background-color: #3c8aff !important;
  border-color: rgba(0, 0, 0, 0.55) !important;
}

.dark .assistant-entry:focus-visible {
  outline: 2px solid rgba(0, 0, 0, 0.65) !important;
}

.base_header_img {
  margin: auto;
}

.homepage_wrapper {
  width: 75%;
  max-width: 1376px;
  margin: auto;
  margin-bottom: 50px;
}

.home_header {
  padding-bottom: 0 !important;
}

.home_header h1 {
  margin-bottom: 24px;
}

.home_header div p {
  margin-top: 10px;
}

/* Callout heading contrast in dark mode */
.dark .callout :is(h1, h2, h3, h4, h5, h6) {
  color: inherit;
}
