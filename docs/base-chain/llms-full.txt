# https://docs.base.org/base-chain/llms-full.txt

## Base Chain — Deep Guide for LLMs

> Base is a fast, low‑cost Ethereum L2 built on the OP Stack. This guide orients an LLM to deployment, connectivity, network details, tooling, node operations, and security across the Base Chain docs.

### What you can do here
- Deploy smart contracts quickly (testnet and mainnet)
- Connect apps to read/write on Base
- Understand fees, contracts, and bridges
- Operate performant nodes
- Use ecosystem tools (faucets, explorers, oracles, onramps)
- Discover Flashblocks resources
- Follow security disclosure guidance

## Navigation (with brief descriptions)

### Quickstart
- [Why Base](https://docs.base.org/base-chain/quickstart/why-base.md) — Platform value prop
- [Deploy on Base](https://docs.base.org/base-chain/quickstart/deploy-on-base.md) — Deploy contracts (Foundry)
- [Connecting to Base](https://docs.base.org/base-chain/quickstart/connecting-to-base.md) — App connectivity
- [Bridge Token](https://docs.base.org/base-chain/quickstart/bridge-token.md) — Transfer assets to Base

### Network Information
- [Base Contracts](https://docs.base.org/base-chain/network-information/base-contracts.md) — Canonical addresses
- [Network Fees](https://docs.base.org/base-chain/network-information/network-fees.md) — Fee model
- [Ecosystem Contracts](https://docs.base.org/base-chain/network-information/ecosystem-contracts.md) — Third‑party addresses
- [Block Building](https://docs.base.org/base-chain/network-information/block-building.md) — Block production
- [Diffs vs Ethereum](https://docs.base.org/base-chain/network-information/diffs-ethereum-base.md) — Differences from L1
- [Bridges (Mainnet)](https://docs.base.org/base-chain/network-information/bridges-mainnet.md) — Bridges

### Flashblocks
- [Apps](https://docs.base.org/base-chain/flashblocks/apps.md) — Apps using Flashblocks
- [Node Providers](https://docs.base.org/base-chain/flashblocks/node-providers.md) — Infra providers
- [Docs](https://docs.base.org/base-chain/flashblocks/docs.md) — Documentation

### Node Operators
- [Run a Base Node](https://docs.base.org/base-chain/node-operators/run-a-base-node.md) — Node setup
- [Performance Tuning](https://docs.base.org/base-chain/node-operators/performance-tuning.md) — Optimization
- [Snapshots](https://docs.base.org/base-chain/node-operators/snapshots.md) — Snapshot sync
- [Troubleshooting](https://docs.base.org/base-chain/node-operators/troubleshooting.md) — Common fixes

### Tools
- [Base Products](https://docs.base.org/base-chain/tools/base-products.md) — Product listing
- [Onchain Registry API](https://docs.base.org/base-chain/tools/onchain-registry-api.md) — API reference
- [Node Providers](https://docs.base.org/base-chain/tools/node-providers.md) — RPC endpoints
- [Block Explorers](https://docs.base.org/base-chain/tools/block-explorers.md) — Explorers
- [Network Faucets](https://docs.base.org/base-chain/tools/network-faucets.md) — Test ETH
- [Onboarding](https://docs.base.org/base-chain/tools/onboarding.md) — New builder help
- [Data Indexers](https://docs.base.org/base-chain/tools/data-indexers.md) — Indexers
- [Cross Chain](https://docs.base.org/base-chain/tools/cross-chain.md) — Interop
- [Account Abstraction](https://docs.base.org/base-chain/tools/account-abstraction.md) — AA tooling
- [Onramps](https://docs.base.org/base-chain/tools/onramps.md) — Fiat onramps
- [Oracles](https://docs.base.org/base-chain/tools/oracles.md) — Oracle providers
- [Tokens in Wallet](https://docs.base.org/base-chain/tools/tokens-in-wallet.md) — Token inclusion

### Security
- [Security Council](https://docs.base.org/base-chain/security/security-council.md) — Security governance
- [Avoid Malicious Flags](https://docs.base.org/base-chain/security/avoid-malicious-flags.md) — App‑blocklist
- [Report a Vulnerability](https://docs.base.org/base-chain/security/report-vulnerability.md) — Reporting


## Quickstart (excerpts)

Source: `https://docs.base.org/base-chain/quickstart/deploy-on-base.md`

Deploy with Foundry to Base Sepolia:

```bash
forge create src/Contract.sol:Contract \
  --rpc-url $BASE_SEPOLIA_RPC \
  --private-key $PRIVATE_KEY \
  --verify --verifier blockscout --verifier-url https://base-sepolia.blockscout.com/api
```

Source: `https://docs.base.org/base-chain/quickstart/connecting-to-base.md`

Connect a client to Base:

```ts
import { createPublicClient, http } from 'viem'
import { base, baseSepolia } from 'viem/chains'

const client = createPublicClient({ chain: base, transport: http() })
```


## Key Concepts (excerpts)

Source: `https://docs.base.org/base-chain/network-information/diffs-ethereum-base.md`

- OP Stack rollup: Base batches L2 transactions and posts data to Ethereum L1, inheriting L1 security.
- Fees: Total includes L2 execution and L1 data costs. See fee breakdown and estimator.
  - Source: `https://docs.base.org/base-chain/network-information/network-fees.md`
- Canonical contracts: Use published address lists for bridges, system contracts, and registry.
  - Source: `https://docs.base.org/base-chain/network-information/base-contracts.md`
- Bridges: Official and third‑party bridge options for moving assets to/from Base.
  - Source: `https://docs.base.org/base-chain/network-information/bridges-mainnet.md`


## APIs and Tooling (pruned)

- Onchain Registry API — discover verified projects and data on Base
  - Source: `https://docs.base.org/base-chain/tools/onchain-registry-api.md`
- Node Providers — RPC endpoints for Base and Base Sepolia
  - Source: `https://docs.base.org/base-chain/tools/node-providers.md`
- Block Explorers — Basescan and Blockscout URLs
  - Source: `https://docs.base.org/base-chain/tools/block-explorers.md`
- Faucets — Get test ETH for Base Sepolia
  - Source: `https://docs.base.org/base-chain/tools/network-faucets.md`


## Node Operations (excerpts)

Sources:
- `https://docs.base.org/base-chain/node-operators/run-a-base-node.md`
- `https://docs.base.org/base-chain/node-operators/performance-tuning.md`
- `https://docs.base.org/base-chain/node-operators/snapshots.md`
- `https://docs.base.org/base-chain/node-operators/troubleshooting.md`

- Architecture: Operate a rollup (consensus) node paired with an execution client. Ensure both services are healthy and in sync.
- Provisioning: Use SSD storage, reliable network, and adhere to the current requirements listed in the run‑a‑node guide. Prefer containerized or systemd‑managed services for resilience.
- Sync strategy: Start from a published snapshot to reduce time‑to‑sync, or sync from genesis when required for auditing. Choose pruned vs. archive based on workload.
- Monitoring: Track head slot/number, peer count, p2p health, RPC latency, error rates, and disk pressure. Export metrics to your observability stack and set alerts.
- RPC best practices: Expose only necessary methods, enforce authentication and rate limits, and front with a TLS‑terminating proxy. Separate public and internal RPCs.
- Performance tuning: Tune DB/cache sizes, peer limits, and concurrency. Offload heavy queries. See the performance tuning guide for recommended flags and OS settings.
- Maintenance: Keep up with client releases and chain config updates. Rotate logs, verify backups, and periodically re‑validate snapshots.
- Troubleshooting: Use the troubleshooting guide for common sync stalls, p2p isolation, or corrupted DB recovery.

Check sync status via JSON‑RPC:

```bash
curl -s -X POST "$BASE_RPC" -H 'content-type: application/json' \
  --data '{"jsonrpc":"2.0","id":1,"method":"eth_syncing","params":[]}' | jq
```


## Flashblocks (excerpts)

Sources:
- `https://docs.base.org/base-chain/flashblocks/docs.md`
- `https://docs.base.org/base-chain/flashblocks/apps.md`
- `https://docs.base.org/base-chain/flashblocks/node-providers.md`

- Overview: Flashblocks resources aggregate information for builders and infra partners who need reliable, low‑latency access patterns on Base.
- Ecosystem: Review apps that make use of Flashblocks‑related infra and the node providers who support relevant capabilities.
- When to consider: High‑frequency reads/writes, market‑sensitive UX, or services where propagation and data freshness are critical.
- Integration: Work with supported node providers from the listing; follow provider documentation and the Flashblocks docs for setup and operational guidance.
- Validation: Measure end‑to‑end latency and consistency across providers as part of your rollout plan.

Simple latency probe (conceptual):

```ts
const t0 = Date.now()
const block = await client.getBlock()
console.log('latencyMs', Date.now() - t0, 'number', block.number)
```


## Examples (common flows)

Example: Bridge a token to Base (conceptual)

Source: `https://docs.base.org/base-chain/quickstart/bridge-token.md`

```text
1) Visit the official bridge UI
2) Select network (Ethereum → Base)
3) Choose asset and amount
4) Review fees and confirm
```

Example: Read a contract on Base with Viem

Source: `https://docs.base.org/base-chain/quickstart/connecting-to-base.md`

```ts
import { createPublicClient, http } from 'viem'
import { base } from 'viem/chains'

const client = createPublicClient({ chain: base, transport: http() })
const totalSupply = await client.readContract({ address: USDC, abi, functionName: 'totalSupply' })
```

