# https://docs.base.org/base-chain/llms.txt

## Base Chain Documentation

> Base is a fast, low-cost Ethereum L2 for building global onchain apps; start here to deploy, connect, and operate reliably.

## Quickstart
- [Deploy on Base](https://docs.base.org/base-chain/quickstart/deploy-on-base.md) — Set up Foundry, configure RPCs, and deploy your first contract
- [Connecting to Base](https://docs.base.org/base-chain/quickstart/connecting-to-base.md) — Configure providers and clients to read/write on Base

## Network Information 
- [Network Fees](https://docs.base.org/base-chain/network-information/network-fees.md) — L2 execution and L1 data costs on Base
- [Base Contracts](https://docs.base.org/base-chain/network-information/base-contracts.md) — Core contracts and addresses

## Flashblocks
- [FAQ](https://docs.base.org/base-chain/flashblocks/docs.md) — Frequently asked questions about Flashblocks
- [Apps](https://docs.base.org/base-chain/flashblocks/apps.md) — How apps integrate with Flashblocks

## Node Operators
- [Run a Base Node](https://docs.base.org/base-chain/node-operators/run-a-base-node.md) — Setup and operations guide
- [Performance Tuning](https://docs.base.org/base-chain/node-operators/performance-tuning.md) — Optimize node performance

## Tools
- [Onchain Registry API](https://docs.base.org/base-chain/tools/onchain-registry-api.md) — Query Base registry data
- [Network Faucets](https://docs.base.org/base-chain/tools/network-faucets.md) — Obtain testnet ETH for Base Sepolia

## Security 
- [Report a Vulnerability](https://docs.base.org/base-chain/security/report-vulnerability.md) — Security contact and disclosure
- [Security Council](https://docs.base.org/base-chain/security/security-council.md) — Governance and process overview

## Optional
- [Block Explorers](https://docs.base.org/base-chain/tools/block-explorers.md) — Inspect contracts and transactions on Base
- [Ecosystem Contracts](https://docs.base.org/base-chain/network-information/ecosystem-contracts.md) — Common ecosystem contract addresses


