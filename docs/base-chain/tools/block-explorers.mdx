---
title: 'Block Explorers'
description: Documentation for block explorers for the Base network.
---

## <PERSON><PERSON>

The Arkham [Platform](https://platform.arkhamintelligence.com/) supports Base.

Arkham is a crypto intelligence platform that systematically analyzes blockchain transactions, showing users the people and companies behind blockchain activity, with a suite of advanced tools for analyzing their activity.

## Blockscout

A Blockscout explorer is available for [Base](https://base.blockscout.com/).

Blockscout provides tools to help you debug smart contracts and transactions:

- View, verify, and interact with smart contract source code.
- View detailed transaction information

A testnet explorer for [Base Sepolia](https://base-sepolia.blockscout.com/) is also available.

## Etherscan

An Etherscan block explorer is available for [Base](https://basescan.org).

Etherscan provides tools to help you view transaction data and debug smart contracts:

- Search by address, transaction hash, batch, or token
- View, verify, and interact with smart contract source code
- View detailed transaction information
- View L1-to-L2 and L2-to-L1 transactions

A testnet explorer for [Base Sepolia](https://sepolia.basescan.org/) is also available.

## DexGuru

[<PERSON><PERSON>uru](https://base.dex.guru) provides a familiar UI with data on transactions, blocks, account balances and more. Developers can use it to verify smart contracts and debug transactions with interactive traces and logs visualization.

## L2scan Explorer

[L2scan Explorer](https://base.l2scan.co/) is a web-based tool that allows users to analyze Base and other layer 2 networks. It provides a user-friendly interface for viewing transaction history, checking account balances, and tracking the status of network activity.

## OKLink

[OKLink](https://www.oklink.com/base) is a multi-chain blockchain explorer that supports Base and provides the following features for developers:

- Search by address, transaction, block, or token
- View, verify, and interact with smart contract source code
- Access a comprehensive and real-time stream of on-chain data, including large transactions and significant fund movements
- Address labels (i.e. project labels, contract labels, risk labels, black address labels, etc.)

## Routescan

[Routescan](https://routescan.io/) superchain explorer allows you to search for transactions, addresses, tokens, prices and other activities taking place across all Superchain blockchains, including Base.

## Tenderly Explorer

With the [Tenderly](https://tenderly.co/) developer explorer you can get unparalleled visibility into your smart contract code. You can easily view detailed transaction information, spot bugs in your code, and optimize gas spend. Supporting Base mainnet and Base Sepolia testnet, Tenderly Explorer helps you track your smart contracts while providing visibility on a granular level.

