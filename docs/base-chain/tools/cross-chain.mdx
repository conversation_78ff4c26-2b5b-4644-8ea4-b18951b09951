---
title: Cross-chain
description: Documentation for cross-chain communication and messaging on the Base network. This page covers integrating tools like LayerZero with Base for web3 transactions, cross-chain messaging, and secure blockchain communication.
---

import {HeaderNoToc} from "/snippets/headerNoToc.mdx";

## Axelar

[Axelar](https://axelar.network/) is an interchain platform that connects blockchains to enable universal web3 transactions. By integrating with Axelar, applications built on Base can now easily send messages and assets between the 49+ blockchains connected via Axelar.

To learn more about <PERSON><PERSON> visit our [docs](https://docs.axelar.dev/). For complete end-to-end examples demonstrating various Axelar use cases please visit the available [code examples](https://github.com/axelarnetwork/axelar-examples).

<HeaderNoToc title="Supported Networks"/>

- [Base Mainnet](https://docs.axelar.dev/resources/mainnet)
- [Base Testnet](https://docs.axelar.dev/resources/testnet)

#### Axelarscan

To view current transactions and live stats about the Axelar network, please visit the [Axelarscan block explorer](https://axelarscan.io/)



## Crossmint

[Crossmint](https://crossmint.com/?utm_source=backlinks&utm_medium=docs&utm_campaign=base) allows you to create and deploy NFT Collections and enable cross-chain payments. This enables your users and customers to purchase an NFT from a collection deployed on Base using Ethereum or Solana tokens.

Check out [Crossmint Docs](https://docs.crossmint.com/nft-checkout/introduction/?utm_source=backlinks&utm_medium=docs&utm_campaign=base) to learn more about NFT Checkout with Crossmint. To power cross-chain payments, click [here](https://docs.crossmint.com/nft-checkout/pay-button/select-payment-options/?utm_medium=docs&utm_source=backlinks&utm_campaign=base) to get started.

<HeaderNoToc title="Supported Networks"/>

- [Base Mainnet](https://www.crossmint.com/products/nft-checkout/?utm_source=backlinks&utm_medium=docs&utm_campaign=base)
- [Base Sepolia](https://www.crossmint.com/products/nft-checkout/?utm_source=backlinks&utm_medium=docs&utm_campaign=base)



## Chainlink CCIP

[Chainlink CCIP](https://chain.link/cross-chain) is a secure interoperability protocol that allows for securely sending messages, transferring tokens, and initiating actions across different blockchains.

To get started with integrating Chainlink CCIP in your Base project, visit the Chainlink CCIP [documentation](https://docs.chain.link/ccip).

<HeaderNoToc title="Supported Networks"/>

- [Base Mainnet](https://docs.chain.link/ccip/supported-networks/v1_0_0/mainnet#base-mainnet)
- [Base Sepolia](https://docs.chain.link/ccip/supported-networks/v1_2_0/testnet) (Testnet)



## LayerZero

[LayerZero](https://layerzero.network/) is an omnichain interoperability protocol that enables cross-chain messaging. Applications built on Base can use the LayerZero protocol to connect to 35+ supported blockchains seamlessly.

To get started with integrating LayerZero, visit the LayerZero [documentation](https://docs.layerzero.network/v1/developers/evm/evm-guides/send-messages) and provided examples on [GitHub](https://github.com/LayerZero-Labs/solidity-examples).

<HeaderNoToc title="Supported Networks"/>

- [Base Mainnet](https://docs.layerzero.network/v2/developers/evm/technical-reference/deployed-contracts#base)
- [Base Sepolia](https://docs.layerzero.network/v2/developers/evm/technical-reference/deployed-contracts#base-sepolia) (Testnet)



## Wormhole

[Wormhole](https://wormhole.com/) is a generic messaging protocol that provides secure communication between blockchains.

By integrating Wormhole, a Base application can access users and liquidity on > 30 chains and > 7 different platforms.

See [this quickstart](https://docs.wormhole.com/wormhole/quick-start/tutorials/hello-wormhole) to get started with integrating Wormhole in your Base project.

For more information on integrating Wormhole, visit their [documentation](https://docs.wormhole.com/wormhole/) and the provided [GitHub examples](https://github.com/wormhole-foundation/wormhole-examples).

<HeaderNoToc title="Supported Networks"/>

- [Base Mainnet](https://docs.wormhole.com/wormhole/blockchain-environments/evm#base)
- [Base Sepolia](https://docs.wormhole.com/wormhole/blockchain-environments/evm#base) (Testnet)
