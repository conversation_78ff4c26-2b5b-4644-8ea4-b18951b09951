---
title: 'Node Providers'
description: Documentation for Node Providers for the Base network. Including details on their services, supported networks, and pricing plans.
---

import {HeaderNoToc} from "/snippets/headerNoToc.mdx";

## Coinbase Developer Platform (CDP)

[CDP](https://portal.cdp.coinbase.com/) provides an RPC endpoint that runs on the same node infrastructure that powers Coinbase's retail exchange, meaning you get the rock solid reliability of our retail exchange as a developer. CDP gives you a free, rate limited RPC endpoint to begin building on Base.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## 1RPC

[1RPC](https://1rpc.io/) is the first and only on-chain attested privacy preserving RPC that eradicates metadata exposure and leakage when interacting with blockchains. 1RPC offers free and [paid plans](https://www.1rpc.io/#pricing) with additional features and increased request limits.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet

## Alchemy

[Alchemy](https://www.alchemy.com/base) is a popular API provider and developer platform. Its robust, free tier offers access to enhanced features like SDKs, [JSON-RPC APIs](https://docs.alchemy.com/reference/base-api-quickstart), and hosted mainnet and testnet nodes for Base.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## All That Node

[All That Node](https://www.allthatnode.com/base.dsrv) is a comprehensive multi-chain development suite, designed to support multiple networks from a single platform. They offer free and [paid plans](https://www.allthatnode.com/pricing.dsrv) with additional features and increased request limits.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## Ankr

[Ankr](https://www.ankr.com/rpc/base/) provides private and public RPC endpoints for Base, powered by a globally distributed and decentralized network of nodes. They offer free and [paid plans](https://www.ankr.com/rpc/pricing/) with increased request limits.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## Blast

[Blast](https://blastapi.io/public-api/base) provides fast and reliable decentralized blockchain APIs by partnering with third-party Node Providers. Blast offers users the ability to generate their own [dedicated RPC endpoint for Base](https://blastapi.io/login).

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## Blockdaemon

[Blockdaemon](https://www.blockdaemon.com/protocols/base/) offers access to hosted Base nodes with a free plan at $0/month via the Ubiquity Data API Suite. Extra costs may be incurred depending on usage.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## BlockPI

[BlockPI](https://blockpi.io/) is a high-quality, robust, and efficient RPC service network that provides access to Base nodes with [free and paid plans](https://docs.blockpi.io/pricing/pricing-and-rate-limit).

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## Chainstack

[Chainstack](https://chainstack.com/build-better-with-base/) allows developers to run high-performing Base nodes and APIs in minutes. They offer elastic Base RPC nodes that provide personal, geographically diverse, and protected API endpoints, as well as archive nodes to query the entire history of the Base Mainnet. Get started with their [free and paid pricing plans](https://chainstack.com/pricing/).

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## DRPC

[DRPC](https://drpc.org/) offers access to a distributed network of independent third-party partners and public nodes for Base. They provide a free tier that allows for an unlimited amount of requests over public nodes, or a paid tier which provides access to all providers, as well as other additional features.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## GetBlock

[GetBlock](https://getblock.io/nodes/base/) is a Blockchain-as-a-Service (BaaS) platform that provides instant API access to full nodes for Base. They offer free, pay per use, and unlimited pricing plans.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## NodeReal

[NodeReal](https://nodereal.io/) is a blockchain infrastructure and services provider that provides instant and easy-access to Base node APIs.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet

## Nodies DLB

[Nodies DLB](https://nodies.app) provides highly performant RPC Services for Base, as well as all other OP-stacked chains. They offer free public endpoints, Pay-As-You-Go, and enterprise pricing plans.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Testnet (Available on request)

## NOWNodes

[NOWNodes](https://nownodes.io/nodes/basechain-base) is a Web3 development tool that provides shared and dedicated no rate-limit access to Base RPC full nodes.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet

## OnFinality

[OnFinality](https://onfinality.io) provides high performance archive access to Base Mainnet and Base Sepolia, with a generous free tier and high rate limits, as well as Trace and Debug APIs, available to [paid plans](https://onfinality.io/pricing).

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## QuickNode

[QuickNode](https://www.quicknode.com/chains/base) offers access to hosted Base nodes as part of their free Discover Plan. You can configure add-ons, like "Trace Mode" and "Archive Mode" for an additional cost by upgrading to one of their paid plans.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## RockX

[RockX](https://access.rockx.com) offers a global blockchain node network and developer tools for onchain innovation. Start with our free [Base RPC](https://access.rockx.com/product/base-blockchain-api-for-web3-builders) to access institutional-grade solutions.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet

## Stackup

[Stackup](https://www.stackup.sh/) is a leading ERC-4337 infrastructure platform. You can access hosted Base nodes with built-in [account abstraction tools](https://docs.stackup.sh/docs) like bundlers and paymasters.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## SubQuery

[SubQuery](https://subquery.network/rpc) is a globally distributed, decentralized network of RPC nodes, offering generous free public endpoints and higher access through Flex Plans

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet

## Tenderly Web3 Gateway

[Tenderly Web3 Gateway](https://tenderly.co/web3-gateway) provides a fast and reliable hosted node solution with a built-in suite of developer tooling and infrastructure building blocks covering your whole development lifecycle. Develop, test, deploy, and monitor your onchain app on the Base network with both [free and paid plans](https://tenderly.co/pricing).

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet
- Base Sepolia (Testnet)

## Unifra

[Unifra](https://www.unifra.io) is a Web3 developer platform that provides tools, APIs, and node infrastructure, and provides access to Base nodes that are nodes are reliable, scalable, and easy to use.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet

## Validation Cloud

[Validation Cloud](https://app.validationcloud.io/) is the world’s fastest node provider according to Compare Nodes. With 50 million compute units available for use without a credit card and a scale tier that never has rate limits, Validation Cloud is built to support your most rigorous and low-latency workloads.

<HeaderNoToc title="Supported Networks"/>

- Base Mainnet

