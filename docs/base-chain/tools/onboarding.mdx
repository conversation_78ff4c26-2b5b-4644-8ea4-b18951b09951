---
title: User Onboarding
slug: /tools/onboarding
description: Documentation for various ways to onboard users in your apps on Base.
---

# User Onboarding

## Crossmint

[Crossmint](https://crossmint.com/?utm_source=backlinks&utm_medium=docs&utm_campaign=base) is an enterprise-grade web3 development platform that lets you deploy smart contracts, create email wallets, enable credit-card and cross chain payments, and use APIs to create, distribute, sell, store, and edit NFTs. By abstracting away the core complexities of the Blockchain, Crossmint allows you to build NFT applications without requiring any blockchain experience or holding cryptocurrency, and making the blockchain invisible to end users. Crossmint enables you to provide a Web2 experience for your Web3 apps and onboard users.

## Dynamic

[Dynamic](https://www.dynamic.xyz/) is a login platform designed for seamless user onboarding. It offers smart and simple login flows for both crypto-native and non-native users. Dynamic features support for non-custodial embedded wallets and consolidating multiple wallets under a single user account. The Dynamic platform is compatible with most EVM chains, including Base.

## Openfort

[Openfort](https://openfort.xyz) is an infrastructure provider designed to simplify the development of games and gamified experiences across their suite of API endpoints. Authenticated users can instantly access the embedded, non-custodial [smart account](https://www.openfort.xyz/docs/guides/javascript/smart-wallet/connected-wallets) natively in the game and sign blockchain transactions with one button. The Openfort platform is compatible with most EVM chains, including Base.

Use [Auth Guide](https://www.openfort.xyz/docs/guides/javascript/auth) to allow several onboarding methods into your game regardless of the platform. 

## Privy

[Privy](https://www.privy.io/) is a library designed for progressive user onboarding and authentication. It enables users to connect to your app using traditional methods such as email addresses, phone numbers, or social profiles, as well as through web3 methods like crypto wallets. Additionally, Privy supports embedded wallets, eliminating the need for users to have a self-custodial wallet prior to exploring your app. Privy is compatible with most EVM chains, including Base.

You can [get started with Privy here](https://docs.privy.io/guide/quickstart), and check out these starter repos for [building a Progressive Web App (PWA) on Base](https://github.com/privy-io/create-privy-pwa) and [using the Base Paymaster with Privy](https://github.com/privy-io/base-paymaster-example).

## Particle Network

[Particle Network](https://particle.network/) is the intent-centric, modular access layer of Web3. With Particle's Smart Wallet-as-a-Service, developers can curate a seamless user experience through modular and customizable EOA/AA embedded wallet components. Using MPC-TSS for key management, Particle can streamline user onboarding via familiar web2 accounts - such as Google accounts, email addresses, and phone numbers. Particle Network's Smart Wallet-as-a-Service is compatible with most EVM chains, including Base.

## Sequence

[Sequence](https://sequence.xyz/base) is an all-in-one development platform for integrating web3 into games. Onboard, monetize, grow, and retain players with Sequence’s award-winning technology including: non-custodial Embedded Wallets, white labeled marketplaces and marketplace API, indexer, relayer, node gateway, Unity/Unreal/React Native/Mobile SDKs, transaction API, contract deployment, analytics, and more. [Learn more here](https://sequence.xyz/base) and start creating on [Sequence Builder](https://sequence.build/) now.

## thirdweb

[thirdweb](https://thirdweb.com/) is the full stack open source web3 solution for bringing web3 into ANY consumer application on ANY platform. Utilize our wide range of sdks on web, mobile, Unity/Unreal or through our cloud hosted engine service! Connect your users with EOA or social logins, create contracts for marketplaces or tokenize in-game items, handle thousands of transactions to build apps that scale, and provide a fiat onramper for your users. 

## WalletKit

[WalletKit](https://walletkit.com) is an all-in-one platform for adding smart, gasless wallets to your app. WalletKit offers pre-built components for onboarding users with email and social logins, which can be integrated in under 15 minutes using their React SDK or the wagmi connector. Alternatively, build completely bespoke experiences for your users using WalletKit's Wallets API.

WalletKit is compatible with most EVM chains, including Base. It has integrated support for ERC 4337 and comes with a paymaster and bundler included, requiring no extra setup.

You can check out the [WalletKit documentation here](https://docs.walletkit.com). Start building for free on the Base testnet today.
