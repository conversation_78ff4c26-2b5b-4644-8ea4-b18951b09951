---
title: 'Network Faucets'
description: Documentation for Testnet Faucets for the Base network. Details how to obtain Base testnet ETH.
---

## Coinbase Developer Platform

The [Coinbase Developer Platform Faucet](https://portal.cdp.coinbase.com/products/faucet) provides free testnet ETH on Base Sepolia - one claim per 24 hours.

<Note>
Requests to Coinbase Developer Platform's Faucet are limited to one claim per 24 hours.
</Note>


## thirdweb Faucet

The [thirdweb Faucet](https://thirdweb.com/base-sepolia-testnet) provides free testnet ETH on Base Sepolia - one claim per 24 hours.

<Note>
The thirdweb faucet allows developers to connect their wallet through EOA or social logins and claim Base Sepolia testnet funds.
</Note>


## Superchain Faucet

The [Superchain Faucet](https://app.optimism.io/faucet) provides testnet ETH for all OP Chains, including Base.

<Note>
The Superchain faucet allows developers to authenticate via their onchain identity. Developers that choose to authenticate via their onchain identity can claim more testnet ETH versus traditional faucets. For more information, see the [FAQ](https://app.optimism.io/faucet).
</Note>


## Alchemy Faucet

The [Alchemy Faucet](https://basefaucet.com/) is a fast and reliable network faucet that allows users with a free Alchemy account to request testnet ETH on Base Sepolia.

<Note>
Requests to Alchemy's Base Sepolia Faucet are limited to one claim per 24 hours.
</Note>


## Bware Labs Faucet

[Bware Labs Faucet](https://bwarelabs.com/faucets) is an easy to use faucet with no registration required. You can use Bware Labs Faucet to claim Base Sepolia testnet ETH for free - one claim per 24 hours.

<Note>
Requests to Bware Labs Faucet are limited to one claim per 24 hours.
</Note>

## Chainstack Faucet

[Chainstack Faucet](https://faucet.chainstack.com/) dispenses Base ETH based on your Chainstack platform API key.

<Note>
Chainstack faucet drips 0.5 ETH every 24 hours.
</Note>


## QuickNode Faucet

[QuickNode Faucet](https://faucet.quicknode.com/drip) is an easy to use Multi-Chain Faucet. You can use QuickNode Faucet to claim Base Sepolia testnet ETH for free - one drip per network every 12 hours.

<Note>
Requests to QuickNode Faucet are limited to one drip every 12 hours.
</Note>


## LearnWeb3 Faucet

[LearnWeb3 Faucet](https://learnweb3.io/faucets/base_sepolia) is a multi-chain faucet by LearnWeb3. You can use the LearnWeb3 faucet to claim Base Sepolia testnet ETH for free - one claim every 24 hours.

<Note>
Requests to LearnWeb3 faucet are limited to one claim per 24 hours.
</Note>


## Ethereum Ecosystem Faucet

The [Base Sepolia Faucet](https://www.ethereum-ecosystem.com/faucets/base-sepolia) is a free & easy to use testnet faucet for Base Sepolia with very generous drips that doesn't require users to log in. It's run by [Ethereum Ecosystem](https://www.ethereum-ecosystem.com).

<Note>
Each wallet is restricted to receiving 0.5 ETH from this faucet every 24 hours.
</Note>

import PolicyBanner from "/snippets/PolicyBanner.mdx";

<PolicyBanner />

