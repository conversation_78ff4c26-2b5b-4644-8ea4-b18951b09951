---
title: How to host Flashblocks-aware RPC nodes
sidebarTitle: Node Providers
---

## Quick Start

1. **Prerequisites**:
  - <PERSON><PERSON> and <PERSON>er Compose
  - Minimum hardware requirements (see [node README](https://github.com/base/node?tab=readme-ov-file#hardware-requirements))
  - Access to a Flashblocks websocket endpoint, we provide public endpoints in the env files in the repo

2. **Set Up Environment**:

  ```sh
  # Clone the repository
  git clone https://github.com/base/node.git
  cd node
  ```

3. **Start the Node with Flashblocks Support**:

  ```sh
  NODE_TYPE=base CLIENT=reth RETH_FB_WEBSOCKET_URL="wss://mainnet.flashblocks.base.org/ws" docker-compose up
  ```

## Configuration Options

- Node Type: Use `NODE_TYPE=base` to enable base reth node with Flashblocks functionality
- Network: Use `NETWORK_ENV=.env.mainnet` for mainnet or `NETWORK_ENV=.env.sepolia` for testnet

Ensure the RETH_FB_WEBSOCKET_URL is set. Base offers the following public rate limited RPC's:
- For Mainnet: `RETH_FB_WEBSOCKET_URL=wss://mainnet.flashblocks.base.org/ws`
- For Sepolia: `RETH_FB_WEBSOCKET_URL=wss://sepolia.flashblocks.base.org/ws`

## Verifying Flashblocks Functionality

Test that your node is properly supporting Flashblocks by querying a pending block:

```sh
curl -X POST \
  --data '{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["pending", false],"id":1}' \
  http://localhost:8545
```

## Available RPC Methods

Flashblocks-aware nodes provide all standard Ethereum JSON-RPC methods plus specialized Flashblocks endpoints. For more details, see the [Flashblocks RPC API documentation](/base-chain/flashblocks/apps#rpc-api).


## WebSocket API

The websocket API is intended to stream Flashblock data to nodes, so they can expose it via their RPC APIs. The endpoints are available at:

   | Network            | URL                                   |
   | :----------------- | :------------------------------------ |
   | Mainnet            | wss://mainnet.flashblocks.base.org/ws |
   | Sepolia            | wss://sepolia.flashblocks.base.org/ws |


#### Interpreting the data

To minimize the amount of data sent to nodes, each Flashblock only includes the diff data from the previous block. The initial Flashblock (when index is zero) includes the block properties (e.g. number, gas limit) and the subsequent Flashblocks only include the diff data (e.g. transactions that are present in that Flashblock).

**Example Initial Response**
```
{
  "payload_id": "0x03997352d799c31a",
  "index": 0,
  "base": {
    "parent_hash": "0x9edc29b8b0a1e31d28616e40c16132ad0d58faa8bb952595b557526bdb9a960a",
    "fee_recipient": "0x4200000000000000000000000000000000000011",
    "block_number": "0x158a0e9",
    "gas_limit": "0x3938700",
    "timestamp": "0x67bf8332",
    "base_fee_per_gas": "0xfa"
    // ... other base fields ...
  },
  "diff": {
    "state_root": "0x208fd63edc0681161105f27d03daf9f8c726d8c94e584a3c0696c98291c24333",
    "block_hash": "0x5c330e55a190f82ea486b61e5b12e27dfb4fb3cecfc5746886ef38ca1281bce8",
    "gas_used": "0xab3f",
    "transactions": [
      "0x7ef8f8a0b4afc0b7ce10e150801bbaf08ac33fecb0f38311793abccb022120d321c6d276..."
    ],
    "withdrawals": []
    // ... other diff fields ...
  },
  "metadata": {
    "block_number": ********,
    "new_account_balances": {
      "0x000f3df6d732807ef1319fb7b8bb8522d0beac02": "0x0",
      // ... other balances ...
    },
    "receipts": {
      "0x07d7f06b06fea714c1d1d446efa2790c6970aa74ee006186a32b5b7dd8ca2d82": {
        "Deposit": {
          "status": "0x1",
          "depositNonce": "0x158a0ea"
          // ... other receipt fields ...
        }
      }
    }
  }
}
```

**Example Diff Response**
```
{
  "payload_id": "0x03e303378749418d",
  "index": 4,
  "diff": {
    "state_root": "0x7a8f45038665072f382730e689f4a1561835c9987fca8942fa95872fb9367eaa",
    "block_hash": "0x9b32f7a14cbd1efc8c2c5cad5eb718ec9e0c5da92c3ba7080f8d4c49d660c332",
    "gas_used": "0x1234f",
    "transactions": [
      "0x7ef8f8a0b4afc0b7ce10e150801bbaf08ac33fecb0f38311793abccb022120d321c6d276..."
    ],
    "withdrawals": []
    // ... other diff fields ...
  },
  "metadata": {
    "block_number": ********,
    "new_account_balances": {
      "0x000f3df6d732807ef1319fb7b8bb8522d0beac02": "0x0",
      "******************************************": "0x1234"
      // ... other balances ...
    },
    "receipts": {
      "0x07d7f06b06fea714c1d1d446efa2790c6970aa74ee006186a32b5b7dd8ca2d82": {
        "status": "0x1",
        "gasUsed": "0x1234f",
        "logs": []
        // ... other receipt fields ...
      }
    }
  }
}
```

## Further Resources

For detailed information about node setup, including hardware requirements and additional configuration options, refer to the [Reth node README](https://github.com/base/node/tree/main/reth#readme).

