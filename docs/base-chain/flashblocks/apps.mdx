---
title: Flashblocks
sidebarTitle: Apps
description: Experience lightning-fast transaction confirmations of <PERSON> by using Flashblocks. Preconfirmations happen in just 200 milliseconds—designed for real-time apps, games, and seamless UX.
---

## Overview

Flashblocks enable up to 200 millisecond transaction confirmations on Base by leveraging preconfirmations, ultra-fast signals that arrive before the next block is sealed. Built for developers who demand instant UX, it's ideal for high-frequency apps, games, and real-time interactions where waiting even a few seconds is too long. By integrating directly within Base's infrastructure, Flashblocks enables, seamless, ultrafast and snappy user experiences without compromising security.

## Integrating Flashblocks

Flashblocks is enabled for developers on Base. There are two ways you can integrate with Flashblocks data. You can either use the WebSocket API to stream real-time block updates, or use the RPC API to query the Flashblocks-aware RPC endpoint.

### RPC API

Base offers the following public Flashblocks aware RPC endpoints. These are rate limited and may not be suitable for production use - we recommend using a node provider that runs Flashblocks integrated nodes.
Major node providers such as Alchemy, Infura, QuickNode and dRPC have Flashblocks-aware RPCs that can be leveraged

   | Network            | URL                              |
   | :----------------- | :------------------------------- |
   | Mainnet            | https://mainnet-preconf.base.org |
   | Sepolia            | https://sepolia-preconf.base.org |


The following RPC methods can return Flashblocks specific data. All standard Ethereum JSON-RPC methods are still supported as usual.

#### eth_getBlockByNumber

Use the `pending` tag to retrieve the latest Flashblock:
```
curl https://sepolia-preconf.base.org -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["pending",true],"id":1}'
```

**Example Response**
```
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "number": "0x1234",
    "hash": "0x...",
    "transactions": [...]
  }
}
```

#### eth_getTransactionReceipt

Use the existing receipt RPC to get preconfirmed receipts:
```
curl https://sepolia-preconf.base.org -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","method":"eth_getTransactionReceipt","params":["0x..."],"id":1}'
```

**Example Response**
```
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "transactionHash": "0x...",
    "blockNumber": "0x1234",
    "status": "0x1"
  }
}
```

#### eth_getBalance

Use the `pending` tag to get the address balance in the latest Flashblock:
```
curl https://sepolia-preconf.base.org -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","method":"eth_getBalance","params":["0x...","pending"],"id":1}'
```

**Example Response**
```
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": "0x0234"
}
```

#### eth_getTransactionCount

Use the `pending` tag to get the address nonce in the latest Flashblock:
```
curl https://sepolia-preconf.base.org -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","method":"eth_getTransactionCount","params":["0x...","pending"],"id":1}'
```

**Example Response**
```
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": "0x1b" // 27 transactions
}
```

#### eth_getTransactionByHash

Use the existing get transaction by hash RPC to get preconfirmed transactions:
```
curl https://sepolia-preconf.base.org -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","method":"eth_getTransactionByHash","params":["0x..."],"id":1}'
```

**Example Response**
```
{
  "type": "0x2",
  "chainId": "...",
  "nonce": "...",
  ...
}
```

### Libraries

You will need to use a Flashblocks-aware RPC endpoint to use Flashblocks with the following libraries:

#### [Wagmi](https://wagmi.sh)

To use Flashblocks with Wagmi, you will need to use the `basePreconf` chain in the Wagmi Config (see `config.ts`).

<CodeGroup>

```tsx Example.tsx
import { useSendTransaction, useWaitForTransactionReceipt } from "wagmi";

function SendTransaction() {
  const { data: hash, sendTransaction } = useSendTransaction();
  const { data: receipt } = useWaitForTransactionReceipt({ hash });

  return (
    <div>
      <button 
        onClick={() => sendTransaction({ 
          to: "0x...", 
          value: parseEther('0.0001'),
        })}
      >
        Send Transaction
      </button>
      {hash && <div>Hash: {hash}</div>}
      {receipt && <div>Included on block number: {receipt.blockNumber}</div>}
    </div>
  )
}
```

```tsx App.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { WagmiProvider, useAccount } from 'wagmi'
import { config } from './config'
import { Example } from './Example'

const queryClient = new QueryClient()

function App() {
  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}> 
        <Example />
      </QueryClientProvider> 
    </WagmiProvider>
  )
}
```

```tsx config.ts
import { createConfig, http } from "wagmi";
import { baseSepoliaPreconf } from "wagmi/chains";
import { baseAccount } from "wagmi/connectors";

export const config = createConfig({
  chains: [baseSepoliaPreconf],
  connectors: [baseAccount()],
  transports: {
    [baseSepoliaPreconf.id]: http(),
  },
});
```

</CodeGroup>

#### [Viem](https://viem.sh)

```ts
import { createWalletClient, http, parseEther, publicActions } from "viem";
import { privateKeyToAccount } from "viem/accounts";
import { baseSepoliaPreconf } from "viem/chains";

// Create client with the Flashblocks-aware chain.
const account = privateKeyToAccount(`0x${process.env.PRIVATE_KEY}`);
const client = createWalletClient({
  account,
  chain: baseSepoliaPreconf,
  transport: http(),
})
  .extend(publicActions);

const submissionTime = new Date();
console.log(`Submitting transaction at: ${submissionTime.toISOString()}`);

// Send transaction.
const hash = await client.sendTransaction({
  to: "0x...",
  value: parseEther('0.0001'),
});
console.log(`Transaction hash: ${hash}`);

// Wait for transaction to be included.
const receipt = await client.waitForTransactionReceipt({ hash });
const confirmTime = new Date();

console.log(`Transaction included at: ${confirmTime.toISOString()}`);
console.log(`Time difference: ${confirmTime - submissionTime}ms`);
```

#### [Ethers](https://github.com/ethers-io/ethers.js)

```jsx
  const providerA = new ethers.JsonRpcProvider(
    "https://sepolia-preconf.base.org"
  );

  const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, providerA);

  try {
    // Create a simple transaction (sending 0.001 ETH to a random address)
    const tx = {
      to: "<SOME ADDRESS>",
      value: ethers.parseEther("0.0000001"),
    };

    // Submit transaction
    const submissionTime = new Date();
    const transaction = await wallet.sendTransaction(tx);

    console.log(`Submitting transaction at: ${submissionTime.toISOString()}`);
    console.log(`Transaction hash: ${transaction.hash}`);

    await transaction.wait(0); // Make sure to set the confirmation count to 0

    console.log("Transaction confirmed");
    const confirmationTime = new Date();
    console.log(`Transaction confirmed at: ${confirmationTime.toISOString()}`);
    console.log(`Time difference: ${confirmationTime - submissionTime}ms`);
  }
```

You should see the confirmation time significantly lower than the standard RPC endpoint.

## Support

For feedback, support or questions about Flashblocks, please don't hesitate to contact us in the `#developer-chat` channel in the [Base Discord](https://base.org/discord).
