---
sidebarTitle: Base Contracts
title: Contract Addresses
description: A comprehensive list of L2 contract addresses for Base Mainnet and Base Testnet, including links to their respective blockchain explorers.
---

## L2 Contract Addresses

### Base Mainnet

| Name                          | Address                                                                                                               |
| :---------------------------- | :-------------------------------------------------------------------------------------------------------------------- |
| WETH9                         | [******************************************](https://basescan.org/address/******************************************) |
| L2CrossDomainMessenger        | [******************************************](https://basescan.org/address/******************************************) |
| L2StandardBridge              | [******************************************](https://basescan.org/address/******************************************) |
| SequencerFeeVault             | [******************************************](https://basescan.org/address/******************************************) |
| OptimismMintableERC20Factory  | [******************************************](https://basescan.org/address/******************************************) |
| GasPriceOracle                | [******************************************](https://basescan.org/address/******************************************) |
| L1Block                       | [******************************************](https://basescan.org/address/******************************************) |
| L2ToL1MessagePasser           | [0x4200000000000000000000000000000000000016](https://basescan.org/address/0x4200000000000000000000000000000000000016) |
| L2ERC721Bridge                | [0x4200000000000000000000000000000000000014](https://basescan.org/address/0x4200000000000000000000000000000000000014) |
| OptimismMintableERC721Factory | [0x4200000000000000000000000000000000000017](https://basescan.org/address/0x4200000000000000000000000000000000000017) |
| ProxyAdmin                    | [0x4200000000000000000000000000000000000018](https://basescan.org/address/0x4200000000000000000000000000000000000018) |
| BaseFeeVault                  | [0x4200000000000000000000000000000000000019](https://basescan.org/address/0x4200000000000000000000000000000000000019) |
| L1FeeVault                    | [******************************************](https://basescan.org/address/******************************************) |
| EAS                           | [******************************************](https://basescan.org/address/******************************************) |
| EASSchemaRegistry             | [******************************************](https://basescan.org/address/******************************************) |
| LegacyERC20ETH                | [******************************************](https://basescan.org/address/******************************************) |

### Base Testnet (Sepolia)

| Name                          | Address                                                                                                                       |
| :---------------------------- | :---------------------------------------------------------------------------------------------------------------------------- |
| WETH9                         | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| L2CrossDomainMessenger        | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| L2StandardBridge              | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| SequencerFeeVault             | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| OptimismMintableERC20Factory  | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| GasPriceOracle                | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| L1Block                       | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| L2ToL1MessagePasser           | [0x4200000000000000000000000000000000000016](https://sepolia.basescan.org/address/0x4200000000000000000000000000000000000016) |
| L2ERC721Bridge                | [0x4200000000000000000000000000000000000014](https://sepolia.basescan.org/address/0x4200000000000000000000000000000000000014) |
| OptimismMintableERC721Factory | [0x4200000000000000000000000000000000000017](https://sepolia.basescan.org/address/0x4200000000000000000000000000000000000017) |
| ProxyAdmin                    | [0x4200000000000000000000000000000000000018](https://sepolia.basescan.org/address/0x4200000000000000000000000000000000000018) |
| BaseFeeVault                  | [0x4200000000000000000000000000000000000019](https://sepolia.basescan.org/address/0x4200000000000000000000000000000000000019) |
| L1FeeVault                    | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| EAS                           | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| EASSchemaRegistry             | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| LegacyERC20ETH                | [******************************************](https://sepolia.basescan.org/address/******************************************) |

\*_L2 contract addresses are the same on both mainnet and testnet._

## L1 Contract Addresses

### Ethereum Mainnet

| Name                         | Address                                                                                                               |
| :--------------------------- | :-------------------------------------------------------------------------------------------------------------------- |
| AddressManager               | [******************************************](https://etherscan.io/address/******************************************) |
| AnchorStateRegistryProxy     | [******************************************](https://etherscan.io/address/******************************************) |
| DelayedWETHProxy (FDG)       | [******************************************](https://etherscan.io/address/******************************************) |
| DelayedWETHProxy (PDG)       | [******************************************](https://etherscan.io/address/******************************************) |
| DisputeGameFactoryProxy      | [******************************************](https://etherscan.io/address/******************************************) |
| FaultDisputeGame             | [******************************************](https://etherscan.io/address/******************************************) |
| L1CrossDomainMessenger       | [******************************************](https://etherscan.io/address/******************************************) |
| L1ERC721Bridge               | [******************************************](https://etherscan.io/address/******************************************) |
| L1StandardBridge             | [******************************************](https://etherscan.io/address/******************************************) |
| MIPS                         | [******************************************](https://etherscan.io/address/******************************************) |
| OptimismMintableERC20Factory | [******************************************](https://etherscan.io/address/******************************************) |
| OptimismPortal               | [******************************************](https://etherscan.io/address/******************************************) |
| PermissionedDisputeGame      | [******************************************](https://etherscan.io/address/******************************************) |
| PreimageOracle               | [******************************************](https://etherscan.io/address/******************************************) |
| ProxyAdmin                   | [******************************************](https://etherscan.io/address/******************************************) |
| SystemConfig                 | [******************************************](https://etherscan.io/address/******************************************) |
| SystemDictator               | [******************************************](https://etherscan.io/address/******************************************) |

**Unneeded contract addresses**

Certain contracts are mandatory according to the [OP Stack SDK](https://stack.optimism.io/docs/build/sdk/#unneeded-contract-addresses), despite not being utilized. For such contracts, you can simply assign the zero address:

- `StateCommitmentChain`
- `CanonicalTransactionChain`
- `BondManager`

### Ethereum Testnet (Sepolia)

| Name                         | Address                                                                                                                       |
| :--------------------------- | :---------------------------------------------------------------------------------------------------------------------------- |
| AddressManager               | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| AnchorStateRegistryProxy     | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| DelayedWETHProxy (FDG)       | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| DelayedWETHProxy (PDG)       | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| DisputeGameFactoryProxy      | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| FaultDisputeGame             | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| L1CrossDomainMessenger       | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| L1ERC721Bridge               | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| L1StandardBridge             | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| L2OutputOracle               | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| MIPS                         | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| OptimismMintableERC20Factory | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| OptimismPortal               | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| PermissionedDisputeGame      | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| PreimageOracle               | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| ProxyAdmin                   | [******************************************](https://sepolia.etherscan.io/address/******************************************) |
| SystemConfig                 | [******************************************](https://sepolia.etherscan.io/address/******************************************) |

## Base Admin Addresses

### Base Mainnet

| Admin Role                       | Address                                                                                                               | Type of Key                               |
| :------------------------------- | :-------------------------------------------------------------------------------------------------------------------- | :---------------------------------------- |
| Batch Sender                     | [******************************************](https://etherscan.io/address/******************************************) | EOA managed by Coinbase Technologies      |
| Batch Inbox                      | [******************************************](https://etherscan.io/address/******************************************) | EOA (with no known private key)           |
| Output Proposer                  | [******************************************](https://etherscan.io/address/******************************************) | EOA managed by Coinbase Technologies      |
| Proxy Admin Owner (L1)           | [******************************************](https://etherscan.io/address/******************************************) | Gnosis Safe                               |
| Challenger                       | [******************************************](https://etherscan.io/address/******************************************) | EOA managed by Coinbase Technologies      |
| System config owner              | [******************************************](https://etherscan.io/address/******************************************) | Gnosis Safe                               |
| Guardian                         | [******************************************](https://etherscan.io/address/******************************************) | Gnosis Safe                               |

### Base Testnet (Sepolia)

| Admin Role             | Address                                                                                                                            | Type of Key                          |
| :--------------------- | :--------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------- |
| Batch Sender           | [******************************************](https://sepolia.etherscan.io/address/******************************************)      | EOA managed by Coinbase Technologies |
| Batch Inbox            | [******************************************](https://sepolia.etherscan.io/address/******************************************)      | EOA (with no known private key)      |
| Output Proposer        | [******************************************](https://sepolia.etherscan.io/address/******************************************)      | EOA managed by Coinbase Technologies |
| Proxy Admin Owner (L1) | [******************************************](https://sepolia.etherscan.io/address/******************************************)      | Gnosis Safe                          |
| Challenger             | [******************************************](https://sepolia.etherscan.io/address/******************************************)      | EOA managed by Coinbase Technologies |
| System config owner    | [******************************************](https://sepolia.etherscan.io/address/******************************************)      | Gnosis Safe                          |
| Guardian               | [******************************************](https://sepolia.etherscan.io/address/******************************************)      | EOA managed by Coinbase Technologies |
