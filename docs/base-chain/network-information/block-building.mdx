---
title: Block Building
slug: /block-building
description: This page outlines how Base blocks are built.
---

## Overview
This section describes how blocks are ordered on the Base networks. The ordering is separate from the UX,
for example the sequencer could be building Flashblocks every 200ms, without these Flashblocks being exposed publicly. In this scenario, block ordering
would change but the user experience would remain consistent.

The Base networks are currently configured in the following ways:

| Network      | Current Configuration       | Upcoming Deployments   |
|--------------|-----------------------------|------------------------|
| Base Mainnet | [Flashblocks](#flashblocks) | Sep 17: [Flashblocks](#flashblocks) + [Per-Transaction Gas Max](#per-transaction-gas-maximum) |
| Base Sepolia | [Flashblocks](#flashblocks) + [Per-Transaction Gas Max](#per-transaction-gas-maximum) | |

## Configurations

### Flashblocks
Currently, blocks are built using [op-rbuilder](https://github.com/flashbots/op-rbuilder) and priority fee auctions occur
every 200ms. There are two changes from the vanilla ordering to be aware of:

##### Timing

Flashblocks are built every 200ms, each ordering a portion of the block. Unlike the current system where later-arriving transactions with higher priority fees can be placed at the top of the block, Flashblocks creates a time-based constraint. Once a Flashblock is built and broadcast, its transaction ordering is locked even if a transaction with a higher priority fee arrives later, it cannot be included in earlier, already built Flashblocks.

##### High Gas Limits

If your app creates transactions with large gas limits, we recommend monitoring to detect any changes in inclusion latency. Transactions with gas limits over 1/10 of the current block gas limit (currently 14 million gas), face additional constraints:

* Each Flashblock can only use a portion of the block's total gas limit
* Flashblock 1: up to 1/10 of the total gas
* Flashblock 2: up to 2/10 of the total gas

And so on for subsequent Flashblocks

Consequently, transactions with large gas requirements must wait for later Flashblocks with sufficient gas capacity. For example, a transaction exceeding 1/10 of the block's gas limit cannot be included in Flashblock 1 and must wait for Flashblock 2 or later.

### Per-Transaction Gas Maximum

<Warning>
On **September 17**, we’ll begin enforcing a per‑transaction gas cap of 25,000,000 on Base. This aligns Base with the [in‑flight L1 proposal](https://eips.ethereum.org/EIPS/eip-7825), improves network predictability, and has no effect on fees for typical users.

#### What’s changing

Today, a single transaction can request any gas up to the block gas limit. With this change, transactions that specify gas > 25,000,000 will be rejected by the mempool before inclusion.

#### Why we’re doing this

* Predictability & resilience. Bounding single‑tx execution reduces extreme outliers and makes block building more stable.
* Consistency with Ethereum. The L1 community is converging on an in-protocol per‑tx cap; we’re aligning early so builders don’t have to juggle different rules across layers.
* No practical impact for most builders. The overwhelming majority of Base transactions are far below this cap.

#### Action items for developers

* If you manually set large gas limits, update your code to stay ≤ 25,000,000.
* If you operate a bundler, configure your maximum bundle size to stay ≤ 25,000,000. For [`rundler`](https://github.com/alchemyplatform/rundler), set `max_bundle_block_gas_limit_ratio` to 0.166 and `target_bundle_block_gas_limit_ratio` to 0.1 for Base. This will produce bundles below the limit for today's gas limit of 150M.
* For batch jobs or complex on‑chain work, split large jobs into smaller calls.
* If you maintain custom tooling, surface a clear message when the cap hits.


#### FAQ

* **Does this change the block gas limit or fees?** No. This is a per‑transaction guardrail.
* **Will contract deployments break?** Typical deployments are well below the cap. If yours isn’t, consider slimming bytecode or chunking initialization.
</Warning>

Base enforces a per-transaction gas maximum of **25,000,000 gas**. Transactions that specify a gas limit above this value are **rejected by the mempool before inclusion**. `eth_sendTransaction` or `eth_sendRawTransaction` will return a JSON-RPC error (for example: `exceeds maximum per-transaction gas limit`). This cap does **not** change the block gas limit or the block validity conditions.

Fusaka's [EIP 7825](https://eips.ethereum.org/EIPS/eip-7825) **will** change the block validity conditions and enforce a lower per-transaction gas maximum of 16,777,216 gas (2^24). We expect this protocol change to be adopted in all OP Stack chains around January 2026.

### Vanilla

Blocks are built every 2s by [op-geth](https://github.com/ethereum-optimism/op-geth). Transactions within those blocks are ordered by
priority fee, see the ([code](https://github.com/ethereum-optimism/op-geth/blob/optimism/miner/worker.go#L627)).

## Changelog

* 11th Sep: Ended testing Per-Transaction Gas Maximum on Base Mainnet
* 10th Sep: Started testing Per-Transaction Gas Maximum on Base Mainnet
* 3rd Sep: Enabled Per-Transaction Gas Maximum on Base Sepolia
* 7th July: Enabled Flashblocks on Base Mainnet
* 15th May: Ended testing Flashblocks on Base Mainnet
* 15th May: Started testing Flashblocks on Base Mainnet
* 25th Feb: Enabled Flashblocks on Base Sepolia
