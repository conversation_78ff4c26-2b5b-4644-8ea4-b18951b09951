---
title: 'Ecosystem Contracts'
---

This page lists contract addresses for onchain apps that we have deployed.

## Base Mainnet

### Multicall3

| Contract   | Address                                                                                                               |
| :--------- | :-------------------------------------------------------------------------------------------------------------------- |
| Multicall3 | [0xcA11bde05977b3631167028862bE2a173976CA11](https://basescan.org/address/0xcA11bde05977b3631167028862bE2a173976CA11) |

### Uniswap v3

| Contract                             | Address                                                                                                               |
| :----------------------------------- | :-------------------------------------------------------------------------------------------------------------------- |
| `Permit2`                            | [0x000000000022D473030F116dDEE9F6B43aC78BA3](https://basescan.org/address/0x000000000022D473030F116dDEE9F6B43aC78BA3) |
| `universal router`                   | [0x198EF79F1F515F02dFE9e3115eD9fC07183f02fC](https://basescan.org/address/0x198EF79F1F515F02dFE9e3115eD9fC07183f02fC) |
| `v3CoreFactory`                      | [0x33128a8fC17869897dcE68Ed026d694621f6FDfD](https://basescan.org/address/0x33128a8fC17869897dcE68Ed026d694621f6FDfD) |
| `multicall`                          | [0x091e99cb1C49331a94dD62755D168E941AbD0693](https://basescan.org/address/0x091e99cb1C49331a94dD62755D168E941AbD0693) |
| `proxyAdmin`                         | [0x3334d83e224aF5ef9C2E7DDA7c7C98Efd9621fA9](https://basescan.org/address/0x3334d83e224aF5ef9C2E7DDA7c7C98Efd9621fA9) |
| `tickLens`                           | [0x0CdeE061c75D43c82520eD998C23ac2991c9ac6d](https://basescan.org/address/0x0CdeE061c75D43c82520eD998C23ac2991c9ac6d) |
| `nftDescriptor`                      | [0xF9d1077fd35670d4ACbD27af82652a8d84577d9F](https://basescan.org/address/0xF9d1077fd35670d4ACbD27af82652a8d84577d9F) |
| `nonfungibleTokenPositionDescriptor` | [0x4f225937EDc33EFD6109c4ceF7b560B2D6401009](https://basescan.org/address/0x4f225937EDc33EFD6109c4ceF7b560B2D6401009) |
| `descriptorProxy`                    | [0x4615C383F85D0a2BbED973d83ccecf5CB7121463](https://basescan.org/address/0x4615C383F85D0a2BbED973d83ccecf5CB7121463) |
| `nonfungibleTokenPositionManager`    | [0x03a520b32C04BF3bEEf7BEb72E919cf822Ed34f1](https://basescan.org/address/0x03a520b32C04BF3bEEf7BEb72E919cf822Ed34f1) |
| `v3Migrator`                         | [0x23cF10b1ee3AdfCA73B0eF17C07F7577e7ACd2d7](https://basescan.org/address/0x23cF10b1ee3AdfCA73B0eF17C07F7577e7ACd2d7) |
| `v3Staker`                           | [0x42bE4D6527829FeFA1493e1fb9F3676d2425C3C1](https://basescan.org/address/0x42bE4D6527829FeFA1493e1fb9F3676d2425C3C1) |
| `quoterV2`                           | [0x3d4e44Eb1374240CE5F1B871ab261CD16335B76a](https://basescan.org/address/0x3d4e44Eb1374240CE5F1B871ab261CD16335B76a) |
| `swapRouter`                         | [0x2626664c2603336E57B271c5C0b26F421741e481](https://basescan.org/address/0x2626664c2603336E57B271c5C0b26F421741e481) |

### Uniswap v2

| Contract  | Address                                                                                                               |
| :-------- | :-------------------------------------------------------------------------------------------------------------------- |
| `Factory` | [0x8909Dc15e40173Ff4699343b6eB8132c65e18eC6](https://basescan.org/address/0x8909Dc15e40173Ff4699343b6eB8132c65e18eC6) |
| `Router`  | [0x4752ba5dbc23f44d87826276bf6fd6b1c372ad24](https://basescan.org/address/0x4752ba5dbc23f44d87826276bf6fd6b1c372ad24) |

## Base Testnet (Sepolia)

### Multicall3

| Contract   | Address                                                                                                                       |
| :--------- | :---------------------------------------------------------------------------------------------------------------------------- |
| Multicall3 | [0xcA11bde05977b3631167028862bE2a173976CA11](https://sepolia.basescan.org/address/0xcA11bde05977b3631167028862bE2a173976CA11) |

### Uniswap v3

| Contract                             | Address                                                                                                                       |
| :----------------------------------- | :---------------------------------------------------------------------------------------------------------------------------- |
| `Permit2`                            | [0x000000000022d473030f116ddee9f6b43ac78ba3](https://sepolia.basescan.org/address/0x000000000022d473030f116ddee9f6b43ac78ba3) |
| `universal router`                   | [0x050E797f3625EC8785265e1d9BDd4799b97528A1](https://sepolia.basescan.org/address/0x050E797f3625EC8785265e1d9BDd4799b97528A1) |
| `v3CoreFactory`                      | [0x4752ba5DBc23f44D87826276BF6Fd6b1C372aD24](https://sepolia.basescan.org/address/0x4752ba5DBc23f44D87826276BF6Fd6b1C372aD24) |
| `multicall`                          | [0xd867e273eAbD6c853fCd0Ca0bFB6a3aE6491d2C1](https://sepolia.basescan.org/address/0xd867e273eAbD6c853fCd0Ca0bFB6a3aE6491d2C1) |
| `proxyAdmin`                         | [0xD7303474Baca835743B54D73799688990f24a79D](https://sepolia.basescan.org/address/0xD7303474Baca835743B54D73799688990f24a79D) |
| `tickLens`                           | [0xedf6066a2b290C185783862C7F4776A2C8077AD1](https://sepolia.basescan.org/address/0xedf6066a2b290C185783862C7F4776A2C8077AD1) |
| `nftDescriptor`                      | [0x4e0caFF1Df1cCd7CF782FDdeD77f020699B57f1a](https://sepolia.basescan.org/address/0x4e0caFF1Df1cCd7CF782FDdeD77f020699B57f1a) |
| `nonfungibleTokenPositionDescriptor` | [0xd7c6e867591608D32Fe476d0DbDc95d0cf584c8F](https://sepolia.basescan.org/address/0xd7c6e867591608D32Fe476d0DbDc95d0cf584c8F) |
| `nonfungibleTokenPositionManager`    | [0x27F971cb582BF9E50F397e4d29a5C7A34f11faA2](https://sepolia.basescan.org/address/0x27F971cb582BF9E50F397e4d29a5C7A34f11faA2) |
| `v3Migrator`                         | [0xCbf8b7f80800bd4888Fbc7bf1713B80FE4E23E10](https://sepolia.basescan.org/address/0xCbf8b7f80800bd4888Fbc7bf1713B80FE4E23E10) |
| `v3Staker`                           | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| `quoterV2`                           | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| `swapRouter`                         | [******************************************](https://sepolia.basescan.org/address/******************************************)         |

#### Testnet interfaces

<Info>
Two community projects, [BaseX](https://basex-test.vercel.app/swap?currencyA=ETH&currencyB=******************************************&focus=source) and [DapDap](https://testnet.base.dapdap.net/uniswap/swap), provide testnet interfaces for Uniswap contracts if you prefer to interact in the browser instead of with the contracts directly.
</Info>

### Uniswap v2

| Contract  | Address                                                                                                                       |
| :-------- | :---------------------------------------------------------------------------------------------------------------------------- |
| `Factory` | [******************************************](https://sepolia.basescan.org/address/******************************************) |
| `Router`  | [******************************************](https://sepolia.basescan.org/address/******************************************) |
