# https://docs.base.org/cookbook/llms-full.txt

## Cookbook — Deep Guide for LLMs

> Task‑oriented recipes for shipping features on Base. Use these guides to implement specific outcomes quickly.

### What you can do here
- Implement end‑to‑end use cases (payments, social, tokens, AI agents)
- Follow AI‑assisted workflows and prompt patterns
- Build Mini Apps rapidly with MiniKit + prompts

## Navigation (with brief descriptions)

### Use Cases
- [Onboard Any User](https://docs.base.org/cookbook/onboard-any-user.md) — UX patterns to remove friction
- [Accept Crypto Payments](https://docs.base.org/cookbook/accept-crypto-payments.md) — Payments flows
- [Launch AI Agents](https://docs.base.org/cookbook/launch-ai-agents.md) — Agent patterns and tooling
- [Launch Tokens](https://docs.base.org/cookbook/launch-tokens.md) — Responsible token launch
- [Deploy a Chain](https://docs.base.org/cookbook/deploy-a-chain.md) — OP Stack deployment
- [Onchain Social](https://docs.base.org/cookbook/onchain-social.md) — Social growth mechanics
- [DeFi Your App](https://docs.base.org/cookbook/defi-your-app.md) — Add finance features
- [Go Gasless](https://docs.base.org/cookbook/go-gasless.md) — Sponsor gas
- [Base App Coins](https://docs.base.org/cookbook/base-app-coins.md) — Coins in Base App
- [Testing Onchain Apps](https://docs.base.org/cookbook/testing-onchain-apps.md) — Testing strategies

### Build with AI
- [AI Prompting](https://docs.base.org/cookbook/ai-prompting.md) — Prompt patterns
- [Base Builder MCP](https://docs.base.org/cookbook/base-builder-mcp.md) — Build‑on‑Base MCP tool

### Vibe Code a Mini App
- [Foundations](https://docs.base.org/cookbook/introduction-to-mini-apps.md) — Mini Apps concepts
- [AI Fundamentals](https://docs.base.org/cookbook/ai-powered-development-fundamentals.md) — AI workflows
- [Prompt Engineering](https://docs.base.org/cookbook/mastering-ai-prompt-engineering.md) — Advanced prompts
- [Docs & Reading](https://docs.base.org/cookbook/essential-documentation-resources.md) — How to read docs
- [AI‑Assisted Reading](https://docs.base.org/cookbook/ai-assisted-documentation-reading.md) — Faster comprehension
- [Successful MiniApps in TBA](https://docs.base.org/cookbook/successful-miniapps-in-tba.md) — Patterns
- [Build with Prompt](https://docs.base.org/cookbook/minikit/build-your-mini-app-with-prompt.md) — Prompt‑driven build
- [Convert & Customize](https://docs.base.org/cookbook/converting-customizing-mini-apps.md) — Adapt templates
- [Fork & Customize](https://docs.base.org/cookbook/minikit/fork-and-customize.md) — Start from template
- [MiniKit: Install](https://docs.base.org/cookbook/minikit/install.md) — Install
- [MiniKit: Add](https://docs.base.org/cookbook/minikit/add-minikit.md) — Integrate
- [MiniKit: Configure Env](https://docs.base.org/cookbook/minikit/configure-environment.md) — Env
- [MiniKit: Manifest CLI](https://docs.base.org/cookbook/minikit/manifest-cli.md) — CLI
- [MiniKit: Create Manifest](https://docs.base.org/cookbook/minikit/create-manifest.md) — Manifest
- [MiniKit: Add Frame Metadata](https://docs.base.org/cookbook/minikit/add-frame-metadata.md) — Frames
- [MiniKit: Test & Deploy](https://docs.base.org/cookbook/minikit/test-and-deploy.md) — Go live

## Minimal Critical Code
None — recipe‑style docs; see product sections for code.


