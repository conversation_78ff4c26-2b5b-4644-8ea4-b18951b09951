# https://docs.base.org/cookbook/llms.txt

## Cookbook Documentation

> Practical, task‑oriented guides for building on Base: use cases, AI workflows, and MiniKit quickstarts.

## Use Cases
- [Onboard Any User](https://docs.base.org/cookbook/onboard-any-user.md) — Reduce friction and improve conversion
- [Accept Crypto Payments](https://docs.base.org/cookbook/accept-crypto-payments.md) — Add payments flows
- [Launch AI Agents](https://docs.base.org/cookbook/launch-ai-agents.md) — Ship messaging agents
- [Launch Tokens](https://docs.base.org/cookbook/launch-tokens.md) — Responsible token launch patterns
- [Deploy a Chain](https://docs.base.org/cookbook/deploy-a-chain.md) — OP Stack deployment guide
- [Onchain Social](https://docs.base.org/cookbook/onchain-social.md) — Social growth mechanics
- [DeFi Your App](https://docs.base.org/cookbook/defi-your-app.md) — Financial features
- [Go Gasless](https://docs.base.org/cookbook/go-gasless.md) — Sponsor user gas
- [Base App Coins](https://docs.base.org/cookbook/base-app-coins.md) — Coins in Base App
- [Testing Onchain Apps](https://docs.base.org/cookbook/testing-onchain-apps.md) — Testing strategies

## Build with AI
- [AI Prompting](https://docs.base.org/cookbook/ai-prompting.md) — Prompt patterns
- [Base Builder MCP](https://docs.base.org/cookbook/base-builder-mcp.md) — Build-on-Base MCP guide

## Vibe Code a Mini App
- [Foundations](https://docs.base.org/cookbook/introduction-to-mini-apps.md) — Mini Apps concepts
- [AI Fundamentals](https://docs.base.org/cookbook/ai-powered-development-fundamentals.md) — AI workflows
- [Prompt Engineering](https://docs.base.org/cookbook/mastering-ai-prompt-engineering.md) — Advanced prompting
- [Docs & Reading](https://docs.base.org/cookbook/essential-documentation-resources.md) — Reading workflows
- [AI‑Assisted Reading](https://docs.base.org/cookbook/ai-assisted-documentation-reading.md) — Faster comprehension
- [Successful MiniApps in TBA](https://docs.base.org/cookbook/successful-miniapps-in-tba.md) — Patterns
- [Build with Prompt](https://docs.base.org/cookbook/minikit/build-your-mini-app-with-prompt.md) — Prompt‑driven build
- [Convert & Customize](https://docs.base.org/cookbook/converting-customizing-mini-apps.md) — Adapt templates
- [Fork & Customize](https://docs.base.org/cookbook/minikit/fork-and-customize.md) — Start from templates
- [MiniKit: Install](https://docs.base.org/cookbook/minikit/install.md) — Install
- [MiniKit: Add](https://docs.base.org/cookbook/minikit/add-minikit.md) — Integrate
- [MiniKit: Configure Env](https://docs.base.org/cookbook/minikit/configure-environment.md) — Env setup
- [MiniKit: Manifest CLI](https://docs.base.org/cookbook/minikit/manifest-cli.md) — CLI
- [MiniKit: Create Manifest](https://docs.base.org/cookbook/minikit/create-manifest.md) — Manifest
- [MiniKit: Add Frame Metadata](https://docs.base.org/cookbook/minikit/add-frame-metadata.md) — Frames
- [MiniKit: Test & Deploy](https://docs.base.org/cookbook/minikit/test-and-deploy.md) — Go live

## Optional
- [Deploy a Chain](https://docs.base.org/cookbook/deploy-a-chain.md) — Chain setup overview

