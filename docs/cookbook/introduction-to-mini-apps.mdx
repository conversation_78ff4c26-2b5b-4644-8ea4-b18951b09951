---
title: Introduction to Mini Apps
description: Mini Apps represent a paradigm shift in application development and distribution
---

# Vibe Coding a Mini App

In the following sections you will be guided through the best practices for vibe coding a mini app game starting with te fundamentals of prompting, what documentation to leverage, to deployment and posting.

Mini Apps are lightweight web applications that run across Farcaster clients like Farcaster and TBA. Posting your mini app and TBA will populate on Farcaster and visa versa

# Introduction to Mini Apps

Mini Apps are a new way to build and share apps—designed for the internet we actually use today: fast, social, and always on. They're not "mini" because they're small in impact, but because they're lightweight, easy to create, and instantly accessible.

Instead of requiring users to download a full app or sign up for a new account, Mini Apps work directly inside the Base App. That means someone can open your app from a social feed, use it immediately, and share it with friends—all in one flow. Whether it's a poll, a marketplace, or a game, Mini Apps are designed to spread through networks, not app stores.

They also come with powerful features out of the box: decentralized identity, built-in payments, and seamless social connection. You don't need to worry about distribution rules or platform lock-in. Your app lives on open infrastructure—and your users own their experience.

# What is The Base App (TBA)?

The Base App (TBA) is your new home onchain—a place where you can post, message, pay, trade, and build. It brings together everything people love about the internet, but without the walls of traditional platforms. And it's built entirely on open protocols, so anyone with an internet connection can join or create.

TBA feels like a familiar social app, but underneath, it's a new kind of operating system for the onchain world. You can create content, earn from it, chat with friends, discover Mini Apps, and even launch your own—all in one place. It's multiplayer by default and designed for everyday people, not just crypto pros.

Creators can earn directly from their posts through "coined content," where likes, shares, and interactions come with real upside. App developers can publish Mini Apps that are instantly discoverable in the social feed and inside chat. And users get a universal wallet, human-readable identity (Basenames), and gasless payments from the start.

TBA isn't just a product—it's a protocol anyone can build on. You own your content, your apps, and your relationships. No gatekeepers, no downloads, no limits.
