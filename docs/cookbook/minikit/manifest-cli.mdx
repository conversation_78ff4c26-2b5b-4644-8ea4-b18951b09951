---
title: Manifest (CLI)
description: Generate account association credentials with the CLI
---

Generate your Farcaster account association credentials.

```bash Terminal
npx create-onchain --manifest
```

Follow the prompts to connect your Farcaster custody wallet, add your deployed URL, and sign. The CLI writes `FARCASTER_HEADER`, `FARCASTER_PAYLOAD`, and `FARCASTER_SIGNATURE` to your `.env`.


<Warning>
While testing, set `noindex: true` in your manifest to avoid indexing.
</Warning>



