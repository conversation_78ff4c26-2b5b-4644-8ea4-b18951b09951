---
title: Configure Environment
description: Add required and optional environment variables for MiniKit
---

Add required variables to your local and deployment environments.

<Tabs>
<Tab title="Required Variables">
These variables are essential for your MiniKit app to function:

<ParamField path='NEXT_PUBLIC_ONCHAINKIT_PROJECT_NAME' type='string' required>
  The name of your Mini App as it appears to users
 </ParamField>

<ParamField path='NEXT_PUBLIC_URL' type='string' required>
  The deployed URL of your application (must be HTTPS)
 </ParamField>

<ParamField path='NEXT_PUBLIC_ONCHAINKIT_API_KEY' type='string' required>
  Your Coinbase Developer Platform API key
 </ParamField>

<ParamField path='FARCASTER_HEADER' type='string' required>
  Generated during manifest creation for account association
 </ParamField>

<ParamField path='FARCASTER_PAYLOAD' type='string' required>
  Generated during manifest creation for account association
 </ParamField>

<ParamField path='FARCASTER_SIGNATURE' type='string' required>
  Generated during manifest creation for account association
 </ParamField>
</Tab>

<Tab title="Optional Variables">
These variables enhance your app's appearance and metadata:

<ParamField path='NEXT_PUBLIC_APP_ICON' type='string'>
  URL to your app's icon (recommended: 48x48px PNG)
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_SUBTITLE' type='string'>
  Brief subtitle shown in app listings
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_DESCRIPTION' type='string'>
  Detailed description of your app's functionality
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_SPLASH_IMAGE' type='string'>
  URL to splash screen image shown during app loading
 </ParamField>

<ParamField path='NEXT_PUBLIC_SPLASH_BACKGROUND_COLOR' type='string'>
  Hex color code for splash screen background (e.g., "#000000")
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_PRIMARY_CATEGORY' type='string'>
  Primary category for app discovery (e.g., "social", "gaming", "utility")
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_HERO_IMAGE' type='string'>
  Hero image URL displayed in cast previews
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_TAGLINE' type='string'>
  Short, compelling tagline for your app
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_OG_TITLE' type='string'>
  Open Graph title for social sharing
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_OG_DESCRIPTION' type='string'>
  Open Graph description for social sharing
 </ParamField>

<ParamField path='NEXT_PUBLIC_APP_OG_IMAGE' type='string'>
  Open Graph image URL for social media previews
 </ParamField>
</Tab>
</Tabs>

### Copy-paste .env example

```bash Terminal
# Required
NEXT_PUBLIC_ONCHAINKIT_PROJECT_NAME=YourAppName
NEXT_PUBLIC_URL=https://your-app.vercel.app
NEXT_PUBLIC_ONCHAINKIT_API_KEY=your_cdp_client_api_key

# Generated by `npx create-onchain --manifest`
FARCASTER_HEADER=base64_header
FARCASTER_PAYLOAD=base64_payload
FARCASTER_SIGNATURE=hex_signature

# Optional (appearance and metadata)
NEXT_PUBLIC_APP_ICON=https://your-app.vercel.app/icon.png
NEXT_PUBLIC_APP_SUBTITLE=Short subtitle
NEXT_PUBLIC_APP_DESCRIPTION=Describe what your app does
NEXT_PUBLIC_APP_SPLASH_IMAGE=https://your-app.vercel.app/splash.png
NEXT_PUBLIC_SPLASH_BACKGROUND_COLOR=#000000
NEXT_PUBLIC_APP_PRIMARY_CATEGORY=social
NEXT_PUBLIC_APP_HERO_IMAGE=https://your-app.vercel.app/og.png
NEXT_PUBLIC_APP_TAGLINE=Play instantly
NEXT_PUBLIC_APP_OG_TITLE=Your App
NEXT_PUBLIC_APP_OG_DESCRIPTION=Fast, fun, social
NEXT_PUBLIC_APP_OG_IMAGE=https://your-app.vercel.app/og.png
```

<Warning>
Ensure all referenced assets are publicly accessible via HTTPS.
</Warning>




