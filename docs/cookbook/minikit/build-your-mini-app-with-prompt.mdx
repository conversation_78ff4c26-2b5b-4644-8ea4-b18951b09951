---
title: Build Your Mini App With a Prompt
description: Use AI to draft your Mini App’s UI quickly—then iterate on layout, flows, and components before adding onchain features
---

# Your First Mini App Prompt

Now that you’ve seen how to write effective prompts and where to find help, it’s time to build your first interface. In this section we’ll focus on the visuals: screens, flows, buttons, and layout. No onchain features yet—just getting the look and feel right so you can move fast.

<Tip>
  Use a split-screen setup if your display allows. Keep **Vercel V0** open in
  one window for generating UI, and a second window for **ChatGPT (or another
  LLM)** plus a **Google Doc** for drafting and iterating on your prompt, and
  skimming docs as needed. This reduces context switching and speeds up
  iteration.
</Tip>

<Steps>
  <Step title='Draft your prompt with intent'>
    Start in your notes or a Google Doc. Write a short, focused prompt that
    describes the core user journey, key screens, and any visual preferences.
    Keep it specific and concise.
  </Step>
  <Step title='Set up your workspace'>
    Arrange two windows side by side. Left: Vercel V0 for UI generation. Right:
    your LLM and notes for refining the prompt and searching documentation.
  </Step>
  <Step title='Generate the first UI in V0'>
    Paste your prompt into V0 and generate an initial interface. Review the
    output carefully: layout, component structure, naming, and accessibility.
  </Step>
  <Step title='Iterate with fast feedback loops'>
    Identify issues or gaps, refine your prompt, and regenerate targeted parts
    (not always the entire UI). Repeat until the flow matches your intent.
  </Step>
  <Step title='Export when satisfied'>
    When the interface feels right, download the code from V0. You’ll wire up
    functionality and any onchain features later in your local environment.
  </Step>
</Steps>

## Example: Frontend-Only Prompt Template

Use (and adapt) this prompt to have V0 generate a clean starting interface for your Mini App. Keep it UI-first—no wallet hooks or chain calls yet.

```
I’m designing the first version of a Mini App UI. Please generate React + TypeScript components with Tailwind CSS that focus on layout and flow only (no data fetching, no blockchain code).

## GOAL
- A simple interface that lets users complete a primary task in under 3 taps/clicks.

## SCREENS

- Home: brief header, primary call to action, and a simple list/grid of recent items.
- Create: a form with 2–3 inputs and a prominent submit button.
- Activity: a read-only feed/timeline showing recent actions in clean cards.

## COMPONENTS

- Reusable Button, Input, Card, and EmptyState components.
- A top-level Layout with responsive header and mobile navigation.

## UX & STYLE

- Mobile-first, accessible, keyboard-friendly.
- Clear hierarchy, generous spacing, and concise copy.
- Subtle loading states and disabled states for buttons.

## DELIVERABLES

- A small component tree with sensible file names.
- Minimal state management with placeholder handlers.
- Inline comments describing where to wire real logic later.

Return complete, working code and explain key decisions briefly at the top of the file.
```
