---
title: Install
description: Add MiniKit to an existing Next.js app
---

Install MiniKit (part of OnchainKit) into your existing Next.js App Router project.

## Prerequisites

<AccordionGroup>
<Accordion title="Next.js App Router">
Your project uses the `app/` directory (App Router).
</Accordion>
<Accordion title="Deployment">
Your app is deployed and publicly accessible over HTTPS (e.g., Vercel).
</Accordion>
<Accordion title="Farcaster account">
You have access to your Farcaster custody wallet for manifest signing.
</Accordion>
<Accordion title="CDP account (for API key)">
Sign in to Coinbase Developer Platform to get a Client API key.
</Accordion>
</AccordionGroup>

## Install dependencies

```bash
npm install @coinbase/onchainkit
```

<Check>
Verify `@coinbase/onchainkit` appears in your `package.json`.
</Check>




