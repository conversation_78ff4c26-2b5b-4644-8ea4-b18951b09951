---
title: Add MiniKit
description: Wrap your app with MiniKitProvider and initialize the frame
---

Add the provider and initialize MiniKit in your main page.

## Add MiniKitProvider

Create `providers/MiniKitProvider.tsx` and wrap `app/layout.tsx`.

```jsx providers/MiniKitProvider.tsx
'use client';
import { MiniKitProvider } from '@coinbase/onchainkit/minikit';
import { ReactNode } from 'react';
import { base } from 'wagmi/chains';

export function MiniKitContextProvider({ children }: { children: ReactNode }) {
  return (
    <MiniKitProvider apiKey={process.env.NEXT_PUBLIC_CDP_CLIENT_API_KEY} chain={base}>
      {children}
    </MiniKitProvider>
  );
}
```

Wrap your root layout:

```jsx app/layout.tsx
import { MiniKitContextProvider } from '@/providers/MiniKitProvider';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang='en'>
      <body>
        <MiniKitContextProvider>{children}</MiniKitContextProvider>
      </body>
    </html>
  );
}
```

## Initialize MiniKit in your page

Use `useMiniKit()` to call `setFrameReady()` when your app is ready.

```jsx app/page.tsx
'use client';
import { useEffect } from 'react';
import { useMiniKit } from '@coinbase/onchainkit/minikit';

export default function HomePage() {
  const { setFrameReady, isFrameReady } = useMiniKit();

  useEffect(() => {
    if (!isFrameReady) setFrameReady();
  }, [isFrameReady, setFrameReady]);

  return <div>Your app content goes here</div>;
}
```

<Tip>
The provider configures wagmi and react‑query and uses the Farcaster connector when available.
</Tip>



