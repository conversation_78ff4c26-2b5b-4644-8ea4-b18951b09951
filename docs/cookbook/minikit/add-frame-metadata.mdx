---
title: Add Frame Metadata
description: Define fc:frame metadata to render rich embeds with launch buttons
---

Metadata is critical for your app to be discovered. It enables rich embeds shared in the social feed and allows it to be properly indexed.Add `fc:frame` metadata so shared links render an embed with a launch button.

<Frame caption="Metadata enables rich embeds and discovery">
<img src="/images/minikit/social_finding.gif" alt="Image of social feed with Mini Apps" />
</Frame>

<Warning>
Place the meta tag in `<head>` and ensure all referenced assets use HTTPS.
</Warning>

### Next.js (generateMetadata)

```ts app/layout.tsx
export async function generateMetadata(): Promise<Metadata> {
  const URL = process.env.NEXT_PUBLIC_URL as string;
  return {
    title: process.env.NEXT_PUBLIC_ONCHAINKIT_PROJECT_NAME,
    description: 'Generated by `create-onchain --mini`',
    other: {
      'fc:frame': JSON.stringify({
        version: 'next',
        imageUrl: process.env.NEXT_PUBLIC_APP_HERO_IMAGE,
        button: {
          title: `Launch ${process.env.NEXT_PUBLIC_ONCHAINKIT_PROJECT_NAME}`,
          action: {
            type: 'launch_frame',
            name: process.env.NEXT_PUBLIC_ONCHAINKIT_PROJECT_NAME,
            url: URL,
            splashImageUrl: process.env.NEXT_PUBLIC_SPLASH_IMAGE,
            splashBackgroundColor: process.env.NEXT_PUBLIC_SPLASH_BACKGROUND_COLOR,
          },
        },
      }),
    },
  };
}
```



