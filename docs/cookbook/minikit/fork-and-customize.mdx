---
title: Fork and Customize (Optional)
description: Learn how to fork a reference Mini App, customize it with your own flows and branding, and deploy it to production
---

## Fork, Customize and Deploy

Forking a reference Mini App can save time, then you can layer in your specific flows and branding.

<Steps>
  <Step title='Fork a relevant starter'>
    Choose an example close to your use case. Update the
    `/.well-known/farcaster.json` file, metadata in `app/layout.tsx` and
    environment variables.
  </Step>
  <Step title='Customize UI and contracts'>
    Adjust theme tokens, copy, and any contract addresses or endpoints required
    for your flows.
  </Step>
  <Step title='Deploy and verify in Base App'>
    Deploy with Vercel, test sharing and discovery, and validate wallet and
    Paymaster flows end to end.
  </Step>
</Steps>

To fork a reference Mini App, open your terminal and run the following command:

```bash
git clone https://github.com/base/demos.git
cd demos/minikit/three-card-monte
npm install
npm run dev
```

<Accordion title="Prompt: Forking and Customizing Mini Apps">

```
Help me fork and customize an existing Mini App for my specific needs:

PROJECT REQUIREMENTS:

I want to build: [DESCRIBE YOUR MINI APP IDEA]

Target audience: [DESCRIBE YOUR USERS]

Key differentiators: [WHAT MAKES YOUR APP UNIQUE]

Branding requirements: [COLORS, STYLE, MESSAGING]

CUSTOMIZATION TASKS:

Fork appropriate Base Mini App repository

Update miniapp.config.json metadata

Customize theme and copy

Configure contract addresses and env vars

DEPLOYMENT PROCESS:

Deploy with Vercel

Set custom domain if needed

Test Mini App behavior within Base App

VERIFICATION CHECKLIST:

Social sharing displays correctly

Smart Wallet and gasless flows work

All custom features function as expected

Provide a file-by-file checklist, exact commands, and a short troubleshooting section.
```

</Accordion>
