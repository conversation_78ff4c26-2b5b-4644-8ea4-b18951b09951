---
title: Master Prompt Engineering
description: Learn best practices for writing effective prompts that generate useful code and UI components for your Mini App
---

## What makes a good prompt

Prompting well is a core skill—it unlocks faster results, better apps, and more creativity. Here's how to improve:

- **Start with a clear goal.** Be specific about what the app should do, who it's for, and how users will interact with it.
- **Give context.** Tell the AI what platform you're building for (TBA), what tools you're using (MiniKit, React, Tailwind), and what kind of experience you want to create.
- **Iterate in small steps.** Don't try to get everything perfect in one go. Run your prompt, review the output, and refine your request to get closer to your vision.
- **Use `llms.txt` files.** These are AI-friendly docs provided by many blockchain tools. Including their contents (or linking to them) gives the AI better reference data.
- **Read your prompt out loud.** If it sounds confusing to you, it'll confuse the AI too.
- **Save good prompts.** Treat them like building blocks. You'll reuse them across projects.

## What makes a prompt effective

- **State the goal and audience** so the model knows what to optimize. This keeps answers focused on the right use case instead of generic solutions.
- **Use sections and lists** to structure thinking and outputs, making it easier for the model to organize and for you to read.
- **Name users, roles, and permissions** to anchor behavior and prevent gaps in access planning.
- **List core features as outcomes** rather than vague ideas so results are actionable.
- **Define data entities and fields** to guide consistent responses and align on what's being stored or displayed.
- **Call out non-functional needs** like security and performance so they aren't forgotten in planning.
- **Provide tech preferences and constraints** to narrow options and avoid irrelevant suggestions.
- **Specify deliverables and format** so outputs are ready to use without heavy rework.
- **Phase the plan** to keep scope lean and shippable. _Example:_ Phase 1 = basic employee profiles and login, Phase 2 = payroll and payslips, Phase 3 = attendance and reviews.
- **Exclude out-of-scope items** to prevent feature creep and keep the project realistic.
- **Invite assumptions** when details are missing so progress continues without waiting on answers.

<AccordionGroup>
  <Accordion title="Prompt Template">
    ```
    I want to build a Mini App for the Base App (TBA)—a social platform where users can post, trade, message, and earn. Please create a responsive React component that includes:

    CORE FUNCTIONALITY:

    [Briefly describe your app's purpose, e.g., "a mood tracker that lets users log their feelings and share their vibe"]

    [List 2–3 features the app should include, like: mood selection, daily recap, emoji reactions]

    [Mention if the app needs to store or display any user data]

    TECH REQUIREMENTS:

    Use React with TypeScript

    Integrate wallet connection using MiniKit SDK

    Allow users to post to the TBA social feed

    Use Tailwind CSS for styling

    Ensure it's mobile-responsive

    VISUAL STYLE:

    Clean, modern design

    [Optional: Specify colors or mood — e.g., "calming blues and purples"]

    Include clear call-to-action buttons

    Optimize layout for mobile users

    SOCIAL FEATURES:

    Show user's Basename if connected

    Let users react to each other's posts (e.g., emoji or stickers)

    Enable easy sharing or reposting

    Please return complete, working code with clear comments that explain each part.
    ```

  </Accordion>

  <Accordion title="Make any prompt better">
    Create an effective prompt from a weak one using the template:

    ```
    You are an expert prompt engineer. I will give you (A) my rough/weak prompt and (B) a proven prompt template. Rewrite (A) to fully conform to (B), filling required sections with best-guess placeholders where my info is missing, and adding only what the template structure requires.

    Constraints:
    - Keep my original intent, audience, and scope.
    - Use clear sections and bullet points.
    - Specify deliverables and output format.
    - List assumptions you made at the end.

    Inputs:
    (A) ROUGH_PROMPT:
    ---
    <paste your rough prompt>
    ---

    (B) TEMPLATE:
    ---
    <paste the prompt template>
    ---

    Output:
    - Final improved prompt that follows the template
    - Short list of assumptions (if any)
    ```

  </Accordion>
</AccordionGroup>

## Additional Resources

Here are essential resources to support your Mini App development journey:

- [AI Prompting Guide](https://docs.base.org/onchainkit/guides/ai-prompting-guide#developers-guide-to-effective-ai-prompting) – Strategies for better AI-assisted development
- [MiniKit Documentation](https://docs.base.org/base-app/guides) – Complete guide to Mini App tools and APIs
- [Base Documentation](https://docs.base.org) – Technical documentation for the Base blockchain
- [OnchainKit Components](https://onchainkit.xyz) – Pre-built React components for onchain functionality
- [Vercel V0 Documentation](https://v0.vercel.com/docs) – Build UIs with natural language
- [Base Community Discord](https://discord.gg/buildonbase) – Connect with other builders
- [Farcaster Dev Resources](https://docs.farcaster.xyz) – Build with Farcaster social protocols
- [Base App Developer Portal](https://developers.base.org) – Tutorials, guides, and tools for Base developers
- https://v0.app/chat/design-planning-for-team-management-site-jazkKQyN4Ok
