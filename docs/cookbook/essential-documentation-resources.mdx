---
title: Essential Documentation Resources
description: Navigate key documentation sources and understand when to use each resource for specific development needs
---

# Developer Resources

When building a Mini App for the Base App (TBA) and Farcaster, there are five main documentation sources you’ll use again and again. These aren’t just docs—they’re tools that will help you solve problems, debug issues, and keep shipping.

Don’t worry if they feel intimidating at first. You’re not expected to memorize them. Instead, you’ll learn to reference and search these docs as needed—and prompt your AI assistant to help you understand and use them. (We’ll show you how to do that in the next section.)

Here’s the key idea:

- **Base Docs** focus on the Base chain and app platform—everything from MiniKit to OnchainKit to smart account tools.
- **Coinbase Developer Platform (CDP)** gives you access to infrastructure tools—wallet APIs, Paymaster, and fiat onramps. Think of it as the backend services layer behind your app.

Use the table below to get a feel for when to reach for each one:

| Documentation                     | Helps with                                               | When to use it                                                          | Example prompt                                                                                                                                                                                                                                                                                              |
| :-------------------------------- | :------------------------------------------------------- | :---------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Base Docs                         | Base Chain, Base Account, Base App, OnchainKit, Cookbook | Finding MiniKit templates; sponsoring transactions via Base Account     | `Using the Base documentation at docs.base.org, help me implement wallet connection in my Mini App. I need to: 1) Connect to a user's wallet, 2) Display their Basename if available, 3) Show their account balance. Provide complete React code using MiniKit SDK with TypeScript and explain each step.`  |
| Coinbase Developer Platform (CDP) | Wallet services, Paymaster, onramps                      | Obtaining API keys; using the Paymaster endpoint; creating fiat onramps | `I want to sponsor transactions for my Mini App users using Coinbase Paymaster. Based on the CDP documentation, provide: 1) Setup instructions for API keys, 2) Complete code example for sponsoring a transaction, 3) Error handling best practices. Include both frontend and any required backend code.` |
| Next.js Docs                      | App router, page rendering, project structure            | Deciding where to store assets; understanding routing and SSR/SSG       | `Using Next.js 14 App Router, help me structure my Mini App with: 1) A main app page, 2) A settings page, 3) API routes for data fetching, 4) Proper file organization for components and assets. Provide the complete folder structure and explain routing patterns.`                                      |
| Wagmi / Viem                      | Wallet integration and on-chain data access              | Retrieving connected wallet address; reading on-chain data              | `Using Wagmi v2 and Viem, create a React hook that: 1) Connects to the user's wallet, 2) Reads their ETH balance on Base, 3) Fetches their last 5 transactions, 4) Handles loading and error states. Include TypeScript types and proper error handling.`                                                   |
| Vercel V0 Docs                    | AI-powered UI generation with V0                         | Adding environment variables; downloading generated code                | `I generated a Mini App UI with V0 and want to deploy it. Help me: 1) Add environment variables for my API keys, 2) Download and integrate the V0 code into my local MiniKit project, 3) Deploy to Vercel with proper configuration. Provide step-by-step instructions.`                                    |

---

## Example Prompt for Understanding Key Tools

Use prompts like the one below to help an AI assistant explain concepts from documentation:

```
Help me understand blockchain frontend development by explaining these concepts in simple terms:

  1. What is Wagmi and how does it simplify wallet connections in React?
  2. How does Viem handle low-level blockchain operations?
  3. What's the difference between reading blockchain data and writing transactions?
  4. How do these tools work together in a typical Mini App?

Focus on the Base network and provide practical examples for each concept. Include code snippets where helpful.
```

In the next section, we’ll show you how to prompt AI tools to make sense of all this documentation—so even the complex stuff becomes easier to use.
