---
title: Earn components & utilities Types
sidebarTitle: Earn
description: Glossary of Types in Earn components & utilities.
---

## `LifecycleStatus`

```ts
export type LifecycleStatus =
  | {
      statusName: 'init';
      statusData: null;
    }
  | {
      statusName: 'amountChange';
      statusData: {
        amount: string;
        token: Token;
      };
    }
  | Extract<
      TransactionLifecycleStatus,
      {
        statusName:
          | 'transactionPending'
          | 'transactionLegacyExecuted'
          | 'success'
          | 'error';
      }
    >;
```

## `EarnReact`

```ts
export type EarnReact = {
  children?: React.ReactNode;
  className?: string;
  vaultAddress: Address;
  isSponsored?: boolean;
};
```

## `EarnProviderReact`

```ts
export type EarnProviderReact = {
  children: React.ReactNode;
  vaultAddress: Address;
  isSponsored?: boolean;
};
```

## `EarnContextType`

```ts
export type EarnContextType = {
  /** Warns users if vault address is invalid */
  error: Error | null;
  recipientAddress?: Address;
  /** Balance of the underlying asset in the user's wallet, converted to the asset's decimals */
  walletBalance?: string;
  /** Status of the wallet balance fetch */
  walletBalanceStatus: 'pending' | 'success' | 'error';
  refetchWalletBalance: () => void;
  vaultAddress: Address;
  /** The token that is being deposited or withdrawn (the underlying asset of the vault) */
  vaultToken: Token | undefined;
  vaultName: string | undefined;
  /** Total deposits in the vault */
  deposits: string | undefined;
  /** Total liquidity (available to borrow) in the vault */
  liquidity: string | undefined;
  /** Total APY of the vault, including rewards */
  apy: number | undefined;
  nativeApy: UseMorphoVaultReturnType['nativeApy'];
  vaultFee: UseMorphoVaultReturnType['vaultFee'];
  /** Rewards earned by the user in the vault */
  rewards: UseMorphoVaultReturnType['rewards'];
  /** The amount of underlying asset that has been deposited in the vault by the connected user */
  depositedBalance?: string;
  /** Whether the deposited balance is being fetched */
  depositedBalanceStatus: 'pending' | 'success' | 'error';
  refetchDepositedBalance: () => void;
  /** Interest earned by the user in the vault */
  interestEarned?: string;
  /** Amount that the user has typed into the deposit amount input */
  depositAmount: string;
  setDepositAmount: (amount: string) => void;
  depositAmountError: string | null;
  depositCalls: Call[];
  /** Amount that the user has typed into the withdraw amount input */
  withdrawAmount: string;
  setWithdrawAmount: (amount: string) => void;
  withdrawAmountError: string | null;
  withdrawCalls: Call[];
  lifecycleStatus: LifecycleStatus;
  updateLifecycleStatus: (
    status: LifecycleStatusUpdate<LifecycleStatus>,
  ) => void;
};
```

## `EarnAmountInputReact`

```ts
export type EarnAmountInputReact = {
  className?: string;
  onChange: (value: string) => void;
  value: string;
  disabled?: boolean;
  'aria-label'?: string;
};
```

## `DepositAmountInputReact`

```ts
export type DepositAmountInputReact = {
  className?: string;
};
```

## `WithdrawAmountInputReact`

```ts
export type WithdrawAmountInputReact = {
  className?: string;
};
```

## `EarnBalanceReact`

```ts
export type EarnBalanceReact = {
  className?: string;
  onActionPress: () => void;
  title: React.ReactNode;
  subtitle: string;
  showAction?: boolean;
};
```

## `DepositBalanceReact`

```ts
export type DepositBalanceReact = {
  className?: string;
};
```

## `WithdrawBalanceReact`

```ts
export type WithdrawBalanceReact = {
  className?: string;
};
```

## `EarnDepositReact`

```ts
export type EarnDepositReact = {
  children?: React.ReactNode;
  className?: string;
};
```

## `EarnWithdrawReact`

```ts
export type EarnWithdrawReact = {
  children?: React.ReactNode;
  className?: string;
};
```

## `EarnDetailsReact`

```ts
export type EarnDetailsReact = {
  className?: string;
};
```

## `DepositButtonReact`

```ts
export type DepositButtonReact = {
  className?: string;
};
```

## `WithdrawButtonReact`

```ts
export type WithdrawButtonReact = {
  className?: string;
};
```

## `DepositToMorphoParams`

```ts
export type DepositToMorphoParams = {
  /** The address of the Morpho vault */
  vaultAddress: Address;
  /** The address of the token to deposit */
  tokenAddress: Address;
  /** The amount of tokens to deposit */
  amount: bigint;
  /** The address which can withdraw the deposited tokens */
  recipientAddress: Address;
};
```

## `WithdrawFromMorphoParams`

```ts
export type WithdrawFromMorphoParams = {
  /** The address of the Morpho vault */
  vaultAddress: Address;
  /** The amount of tokens to withdraw */
  amount: bigint;
  /** The address to which the withdrawn funds will be sent */
  recipientAddress: Address;
};
```

## `UseBuildDepositToMorphoTxParams`

```ts
export type UseBuildDepositToMorphoTxParams = {
  vaultAddress: Address;
  recipientAddress?: Address;
  amount: string;
};
```

## `UseBuildWithdrawFromMorphoTxParams`

```ts
export type UseBuildWithdrawFromMorphoTxParams = {
  vaultAddress: Address;
  recipientAddress?: Address;
  amount: string;
  tokenDecimals: number | undefined;
};
```

## `UseMorphoVaultParams`

```ts
export type UseMorphoVaultParams = {
  vaultAddress: Address;
  recipientAddress?: Address;
};
```

## `UseMorphoVaultReturnType`

```ts
export type UseMorphoVaultReturnType = {
  vaultName: string | undefined;
  status: 'pending' | 'success' | 'error';
  /** Warns users if vault address is invalid */
  error: Error | null;
  /** Underlying asset of the vault */
  asset: {
    address: Address;
    symbol: string | undefined;
    decimals: number | undefined;
  };
  /** User's deposits in the vault, adjusted for decimals */
  balance: string | undefined;
  balanceStatus: 'pending' | 'success' | 'error';
  refetchBalance: () => void;
  /** Total net APY of the vault after all rewards and fees */
  totalApy: number | undefined;
  /** Native rewards of the vault (e.g. USDC if the asset is USDC) */
  nativeApy: number | undefined;
  /** Additional rewards (e.g. MORPHO) */
  rewards:
    | {
        asset: Address;
        assetName: string;
        apy: number;
      }[]
    | undefined;
  /** Vault fee, in percent (e.g. 0.03 for 3%) */
  vaultFee: number | undefined;
  /** Number of decimals of the vault's share tokens (not underlying asset) */
  vaultDecimals: number | undefined;
  /** Total deposits in the vault */
  deposits: string | undefined;
  /** Total liquidity available to borrow in the vault */
  liquidity: string | undefined;
};
```

