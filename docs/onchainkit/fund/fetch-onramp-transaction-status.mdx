---
title: fetchOnrampTransactionStatus
---


The `fetchOnrampTransactionStatus` utility retrieves the status of onramp transactions for a specific user. This is useful for tracking the progress of crypto purchases and displaying transaction history.

## Usage

<CodeGroup>
```tsx code
import { fetchOnrampTransactionStatus } from '@coinbase/onchainkit/fund';

const transactions = await fetchOnrampTransactionStatus({
  partnerUserId: 'user123',
  nextPageKey: '',
  pageSize: '10',
  apiKey: 'your-api-key', // Required when using without OnchainKitProvider or in non-React environment
});
```

```json return value
{
  "transactions": [
    {
      "id": "tx123",
      "status": "ONRAMP_TRANSACTION_STATUS_SUCCESS",
      "purchaseAmount": { "value": "0.1", "currency": "ETH" },
      "paymentAmount": { "value": "200.00", "currency": "USD" },
      "network": "ethereum",
      "createdAt": "2024-01-01T12:00:00Z"
    }
  ],
  "nextPageKey": "next_page_token",
  "totalCount": "1"
}
```
</CodeGroup>

## Parameters

```typescript
{
  /**
   * A unique identifier that will be associated with any transactions created
   * by the user during their Onramp session. You can use this with the
   * Transaction Status API to check the status of the user's transaction.
   * See https://docs.cdp.coinbase.com/onramp/docs/api-reporting#buy-transaction-status
   */
  partnerUserId: string; // Your unique identifier for the user
  nextPageKey: string;   // Token for pagination
  pageSize: string;      // Number of transactions per page
  /**
   * Optional API key for Coinbase Onramp. If not provided, the API key from
   * OnchainKitProvider will be used. Required when using the utility without
   * OnchainKitProvider or in a non-React environment.
   */
  apiKey?: string;
}
```

## Returns

`Promise<OnrampTransactionStatusResponseData>` - Returns a promise that resolves to the transaction status data.

See [`OnrampTransactionStatusResponseData`](/onchainkit/fund/types#onramptransactionstatusresponsedata) for full response type details.

