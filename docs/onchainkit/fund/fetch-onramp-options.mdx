---
title: fetchOnrampOptions
---


The `fetchOnrampOptions` utility retrieves supported fiat currencies and available crypto assets for a specific country. This information is essential for determining which assets can be purchased in the user's jurisdiction.

## Usage

<CodeGroup>
```tsx code
import { fetchOnrampOptions } from '@coinbase/onchainkit/fund';

const options = await fetchOnrampOptions({
  country: 'US',
  subdivision: 'CA', // Required for US residents
  apiKey: 'your-api-key', //   Required when using without OnchainKitProvider or in non-React environment
});
```

```js return value
{
  fiatCurrencies: [
    {
      id: "USD",
      name: "US Dollar",
      symbol: "$"
    }
  ],
  assets: [
    {
      id: "ETH",
      name: "Ethereum",
      networks: [
        {
          id: "ethereum",
          name: "Ethereum",
          config: {
            chainId: "0x1"
          }
        }
      ]
    }
  ]
}
```
</CodeGroup>

## Parameters

```typescript
{
  /**
   * ISO 3166-1 two-letter country code string representing
   * the purchasing user’s country of residence, e.g., US.
   */
  country: string;
  /**
   * ISO 3166-2 two-letter country subdivision code representing
   * the purchasing user’s subdivision of residence within their country,
   * e.g. NY. Required if the country=“US” because certain states (e.g., NY)
   * have state specific asset restrictions.
   */
  subdivision?: string;
  /**
   * Optional API key for Coinbase Onramp. If not provided, the API key from
   * OnchainKitProvider will be used. Required when using the utility without
   * OnchainKitProvider or in a non-React environment.
   */
  apiKey?: string;
}
```

## Returns

`Promise<OnrampOptionsResponseData>` - Returns a promise that resolves to the available options for the specified location.

See [`OnrampOptionsResponseData`](/onchainkit/fund/types#onrampoptionsresponsedata) for full response type details.

