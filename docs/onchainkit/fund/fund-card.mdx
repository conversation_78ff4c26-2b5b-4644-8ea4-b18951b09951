---
title: <FundCard /> · OnchainKit
sidebarTitle: <FundCard />
description: The `<FundCard />` component provides a complete fiat onramp experience with amount input, currency switching, and payment method selection.
---

<Frame>
<img alt="FundCard"
  src="/images/onchainkit/fund-card.gif"
  height="364"/>
</Frame>

The `<FundCard />` component provides a complete fiat onramp experience within your app. It includes:

- Amount input with fiat/crypto switching
- Payment method selection (Coinbase, Apple Pay, Debit Card)
- Automatic exchange rate updates
- Smart handling of payment method restrictions (based on country and subdivision)

<Note>
The Apple Pay and Debit Card onramp options are only available for Coinbase supported assets.  
</Note>


## Prerequisites

Before using the `FundCard` component, ensure you've completed the [Getting Started](/onchainkit/installation/nextjs#get-your-client-api-key) steps.

<Tip>
To use the `FundCard` component, you'll need to provide a Client API Key in `OnchainKitProvider`. You can get one following our [Getting Started](/onchainkit/installation/nextjs#get-your-client-api-key) steps.
</Tip>


<Note>
This component requires a `projectId` to be set in the `OnchainKitProvider`. You can find your `projectId` on [Coinbase Developer Platform](https://portal.cdp.coinbase.com/products/onchainkit).
</Note>


## Usage

### Drop in the `<FundCard />` component

```tsx
import { FundCard } from '@coinbase/onchainkit/fund';

<FundCard
  assetSymbol="ETH"
  country="US"
  currency="USD"
/>;
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-fund-card--default&viewMode=story&dark=true&hero=true"
  width="100%"
  height="370px"
/>
{/* <App>
  <FundWrapper>
    {({ address }) => {
      if (address) {
        return <FundCard assetSymbol="ETH" country="US" currency="USD" />;
      }
      return (
        <>
          <Wallet>
            <ConnectWallet>
              <Avatar className="h-6 w-6" />
              <Name />
            </ConnectWallet>
          </Wallet>
        </>
      );
    }}
  </FundWrapper>
</App> */}

## Customization

### Custom Header and Button Text

You can customize the header and button text:

```tsx
<FundCard
  assetSymbol="ETH"
  country="US"
  currency="USD"
  headerText="Purchase Ethereum"
  buttonText="Purchase"
/>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-fund-card--with-custom-text&viewMode=story&dark=true&hero=true"
  width="100%"
  height="370px"
/>
{/* <App>
  <FundWrapper>
    {({ address }) => {
      if (address) {
        return (
          <FundCard
            assetSymbol="ETH"
            country="US"
            currency="USD"
            headerText="Purchase Ethereum"
            buttonText="Purchase"
          />
        );
      }
      return (
        <>
          <Wallet>
            <ConnectWallet>
              <Avatar className="h-6 w-6" />
              <Name />
            </ConnectWallet>
          </Wallet>
        </>
      );
    }}
  </FundWrapper>
</App> */}

### Custom Currency

You can specify which fiat currency to use:

```tsx
<FundCard
  assetSymbol="ETH"
  country="GB"
  currency="GBP"
/>
```

### Preset Amount Inputs

You can specify preset amount buttons:

```tsx
const presetAmountInputs = ['10', '20', '50'] as const;

<FundCard
  assetSymbol="ETH"
  country="US"
  currency="USD"
  presetAmountInputs={presetAmountInputs}
/>;
```

<Note>
**Note: 3 preset amount inputs are required in order to show the preset amount buttons.**
</Note>


### Custom Content

You can provide custom children to completely customize the card content while keeping the fund button functionality:

```tsx
<FundCard
  assetSymbol="ETH"
  country="US"
  currency="USD"
>
  <div className="space-y-4">
    <h2>Custom Header</h2>
    <input type="number" placeholder="Enter amount" />
    <select>
      <option>Payment Method 1</option>
      <option>Payment Method 2</option>
    </select>
  </div>
</FundCard>
```

You can also reuse the existing children from the default implementation and add your own custom content.

```tsx
import {
  FundCardHeader,
  FundCardAmountInput,
  FundCardAmountInputTypeSwitch,
  FundCardPresetAmountInputList,
  FundCardPaymentMethodDropdown,
  FundCardSubmitButton,
 } from '@coinbase/onchainkit/fund';

<FundCard
  assetSymbol="ETH"
  country="US"
  currency="USD"
>

  <h2>Custom Header instead of the default "FundCardHeader" </h2>
  <FundCardAmountInput />
  <FundCardAmountInputTypeSwitch />
  <FundCardPresetAmountInputList />
  <div>Any custom content</div>
  <FundCardPaymentMethodDropdown />
  <FundCardSubmitButton />
  <div>Any custom content</div>
</FundCard>
```

<Note>
**Note:** If you are using the custom components then you are going to need to access the state of the `FundCard` component. You can do this by using the `useFundContext` hook.
</Note>


```tsx
const {
    asset,
    currency,
    selectedPaymentMethod,
    setSelectedPaymentMethod,
    fundAmountFiat,
    setFundAmountFiat,
    fundAmountCrypto,
    setFundAmountCrypto,
    selectedInputType,
    setSelectedInputType,
    exchangeRate,
    setExchangeRate,
    exchangeRateLoading,
    setExchangeRateLoading,
    submitButtonState,
    setSubmitButtonState,
    paymentMethods,
    setPaymentMethods,
    paymentMethodsLoading,
    setPaymentMethodsLoading,
    headerText,
    buttonText,
    country,
    subdivision,
    lifecycleStatus,
    updateLifecycleStatus,
    presetAmountInputs,
 } = useFundContext();
```

## Props

- [`FundCardPropsReact`](/onchainkit/fund/types#fundcardpropsreact)

## Related Components

- [`<FundButton />`](/onchainkit/fund/fund-button)

