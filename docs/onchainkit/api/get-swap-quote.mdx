---
title: getSwapQuote
openapi: get /getSwapQuote
---


The `getSwapQuote` function is used to get a quote for a swap between two Tokens.

Before using them, make sure to obtain a [Client API Key](https://portal.cdp.coinbase.com/projects/api-keys/client-key) from Coinbase Developer Platform.

{/* ## Usage

<CodeGroup>
```tsx code
import { setOnchainKitConfig } from '@coinbase/onchainkit';
import { getSwapQuote } from '@coinbase/onchainkit/api';
import type { Token } from '@coinbase/onchainkit/token';

setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

const fromToken: Token = {
  name: 'ETH',
  address: '',
  symbol: 'ETH',
  decimals: 18,
  image: 'https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png',
  chainId: 8453,
};

const toToken: Token = {
  name: '<PERSON><PERSON>',
  address: '******************************************',
  symbol: 'USDC',
  decimals: 6,
  image:
    'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2',
  chainId: 8453,
};

const quote = await getSwapQuote({
  from: fromToken,
  to: toToken,
  amount: '0.001',
  useAggregator: false,
});
```

```json return value
{
  "amountReference": "from",
  "chainId": 8453,
  "from": {
    "address": "",
    "chainId": 8453,
    "decimals": 18,
    "image": "https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png",
    "name": "ETH",
    "symbol": "ETH"
  },
  "to": {
    "address": "******************************************",
    "chainId": 8453,
    "decimals": 6,
    "image": "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/…-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2",
    "name": "USDC",
    "symbol": "USDC"
  },
    "fromAmount": "1000000000000000",
    "fromAmountUSD": "2.6519265340000002",
    "toAmount": "2650405",
    "toAmountUSD": "2.64980125",
    "amountReference": "from",
    "priceImpact": "0",
    "chainId": 8453,
    "highPriceImpact": false,
    "slippage": "3"
}
```
</CodeGroup> */}

## Returns

[`Promise<GetSwapQuoteResponse>`](/onchainkit/api/types#getswapquoteresponse)

## Parameters

[`GetSwapQuoteParams`](/onchainkit/api/types#getswapquoteparams)

## Types

- [`GetSwapQuoteResponse`](/onchainkit/api/types#getswapquoteresponse)
- [`GetSwapQuoteParams`](/onchainkit/api/types#getswapquoteparams)

