---
title: buildDepositToMorphoTx
---


The `buildDepositToMorphoTx` function is used to build [Calls](/onchainkit/transaction/types#calls) for depositing an asset to Morpho. These calls can be passed the `<Transaction />` component to send a transaction.

## Usage

```tsx code
import { buildDepositToMorphoTx } from '@coinbase/onchainkit/earn';

const calls = await buildDepositToMorphoTx({
  vaultAddress: '0x...', // Morpho vault address on Base
  tokenAddress: '0x...', // Address of the token to deposit
  amount: 1000000000000000000n, // Amount of tokens to deposit
  recipientAddress: '0x...', // Address of the recipient
});
```

## Returns

[`Call[]`](/onchainkit/transaction/types#calls)

## Parameters

[`DepositToMorphoParams`](/onchainkit/earn/types#deposittomorphoparams)

