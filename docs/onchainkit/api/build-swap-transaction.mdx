---
title: buildSwapTransaction
openapi: post /buildSwapTransaction
---


The `buildSwapTransaction` function is used to get an unsigned transaction for a swap between two Tokens.

Before using this function, make sure to obtain a [Client API Key](https://portal.cdp.coinbase.com/projects/api-keys/client-key) from Coinbase Developer Platform.

{/* ## Usage */}

{/* <CodeGroup>
```tsx code
import { setOnchainKitConfig } from '@coinbase/onchainkit';
import { buildSwapTransaction } from '@coinbase/onchainkit/api';
import type { Token } from '@coinbase/onchainkit/token';

setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

const fromToken: Token = {
  name: 'ETH',
  address: '',
  symbol: 'ETH',
  decimals: 18,
  image: 'https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png',
  chainId: 8453,
};

const toToken: Token = {
  name: 'USDC',
  address: '******************************************',
  symbol: 'USDC',
  decimals: 6,
  image:
    'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2',
  chainId: 8453,
};

const response = await buildSwapTransaction({
  fromAddress: '0x...',
  from: fromToken,
  to: toToken,
  amount: '0.1',
  useAggregator: false,
});
```

```json return value
{
  "approveTransaction": {
    "chainId": 8453,
    "data": "",
    "gas": 0,
    "to": "",
    "value": 0
  },
  "fee": {
    "baseAsset": {
      "name": "USDC",
      "address": "******************************************",
      "currencyCode": "USDC",
      "decimals": 6,
      "imageURL": "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2",
      "blockchain": "eth",
      "aggregators": [Array],
      "swappable": true,
      "unverified": false,
      "chainId": 8453
    },
    "percentage": "1",
    "amount": "3517825"
  },
  "quote": {
    "from": {
      "address": "",
      "chainId": 8453,
      "decimals": 18,
      "image": "https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png",
      "name": "ETH",
      "symbol": "ETH"
    },
    "to": {
      "address": "******************************************",
      "chainId": 8453,
      "decimals": 6,
      "image": "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2",
      "name": "USDC",
      "symbol": "USDC"
    },
    "fromAmount": "100000000000000000",
    "toAmount": "348264739",
    "amountReference": "from",
    "priceImpact": "",
    "chainId": 8453,
    "highPriceImpact": false,
    "slippage": "3",
    "warning": {
      "type": "warning",
      "message": "This transaction has a very high likelihood of failing if submitted",
      "description": "failed with 500000000 gas: insufficient funds for gas * price + value: address ****************************************** have 0 want 100000000000000000"
    }
  },
  "transaction": {
    "chainId": 8453,
    "data": "0x...",
    "gas": 419661,
    "to": "******************************************",
    "value": 100000000000000000
  },
  "warning": {
    "type": "warning",
    "message": "This transaction has a very high likelihood of failing if submitted",
    "description": "failed with 500000000 gas: insufficient funds for gas * price + value: address ****************************************** have 0 want 100000000000000000"
  }
}
```
</CodeGroup> */}

## Returns

[`Promise<BuildSwapTransactionResponse>`](/onchainkit/api/types#buildswaptransactionresponse)

## Parameters

[`BuildSwapTransactionParams`](/onchainkit/api/types#buildswaptransactionparams)

## Types

- [`BuildSwapTransactionResponse`](/onchainkit/api/types#buildswaptransactionresponse)
- [`BuildSwapTransactionParams`](/onchainkit/api/types#buildswaptransactionparams)

