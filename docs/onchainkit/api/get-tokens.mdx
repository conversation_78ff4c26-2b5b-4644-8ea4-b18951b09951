---
title: getTokens
openapi: get /getTokens
---


The `getTokens` function retrieves a list of tokens on Base by searching for the name, symbol, or address of a token.

Before using them, make sure to obtain a [Client API Key](https://portal.cdp.coinbase.com/projects/api-keys/client-key) from Coinbase Developer Platform.

{/* ## Usage

Search by symbol

<CodeGroup>
```tsx code
import { setOnchainKitConfig } from '@coinbase/onchainkit';
import { getTokens } from '@coinbase/onchainkit/api'; // [!code focus]

setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

const tokens = await getTokens({ limit: '1', search: 'degen' }); // [!code focus]
```

```ts return value
[
  {
    address: '******************************************',
    chainId: 8453,
    decimals: 18,
    image:
      'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/3b/bf/3bbf118b5e6dc2f9e7fc607a6e7526647b4ba8f0bea87125f971446d57b296d2-MDNmNjY0MmEtNGFiZi00N2I0LWIwMTItMDUyMzg2ZDZhMWNm',
    name: 'DEGEN',
    symbol: 'DEGEN',
  },
];
```
</CodeGroup>

Search by name

<CodeGroup>
```tsx code
import { setOnchainKitConfig } from '@coinbase/onchainkit';
import { getTokens } from '@coinbase/onchainkit/api'; // [!code focus]

setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

const tokens = await getTokens({ limit: '1', search: 'Wrapped Ether' }); // [!code focus]
```

```ts return value
[
  {
    address: '******************************************',
    chainId: 8453,
    decimals: 18,
    image:
      'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/47/bc/47bc3593c2dec7c846b66b7ba5f6fa6bd69ec34f8ebb931f2a43072e5aaac7a8-YmUwNmRjZDUtMjczYy00NDFiLWJhZDUtMzgwNjFmYWM0Njkx',
    name: 'Wrapped Ether',
    symbol: 'WETH',
  },
];
```
</CodeGroup>

Search by address

<CodeGroup>
```tsx code
import { setOnchainKitConfig } from '@coinbase/onchainkit';
import { getTokens } from '@coinbase/onchainkit/api'; // [!code focus]

setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

const tokens = await getTokens({
  limit: '1',
  search: '******************************************',
}); // [!code focus]
```

```ts return value
[
  {
    address: '******************************************',
    chainId: 8453,
    decimals: 6,
    image:
      'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2',
    name: 'USDC',
    symbol: 'USDC',
  },
];
```
</CodeGroup> */}

## Returns

[`Promise<GetTokensResponse>`](/onchainkit/api/types#gettokensresponse)

## Parameters

[`GetTokensOptions`](/onchainkit/api/types#gettokensoptions)

## Types

- [`GetTokensResponse`](/onchainkit/api/types#gettokensresponse)
- [`GetTokensOptions`](/onchainkit/api/types#gettokensoptions)

