# https://docs.base.org/onchainkit/llms.txt

## OnchainKit Documentation

> OnchainKit is a React/TypeScript SDK with components, hooks, and APIs to ship beautiful onchain apps (supercharged for Base) in minutes.

## Introduction
- [Getting Started](https://docs.base.org/onchainkit/getting-started.md) — Install, scaffold, and test an OnchainKit app
- [Telemetry](https://docs.base.org/onchainkit/guides/telemetry.md) — Data collection and privacy details

## Installation
- [Next.js](https://docs.base.org/onchainkit/installation/nextjs.md) — Setup in a Next.js 14 project
- [Vite](https://docs.base.org/onchainkit/installation/vite.md) — Vite project setup

## Config
- [OnchainKitProvider](https://docs.base.org/onchainkit/config/onchainkit-provider.md) — Project-wide provider and required props
- [Supplemental Providers](https://docs.base.org/onchainkit/config/supplemental-providers.md) — Customize Wagmi/react-query providers

## Guides
- [Lifecycle Status](https://docs.base.org/onchainkit/guides/lifecycle-status.md) — Component and API lifecycle guidance
- [Testing with OnchainTestKit](https://docs.base.org/onchainkit/guides/testing-with-onchaintestkit.md) — E2E patterns for wallet and tx flows

## Templates
- [Onchain NFT App](https://docs.base.org/onchainkit/templates/onchain-nft-app.md) — Template for minting and displaying NFTs
- [Onchain Commerce App](https://docs.base.org/onchainkit/templates/onchain-commerce-app.md) — Template for checkout and payments

## Components
- [Transaction](https://docs.base.org/onchainkit/transaction/transaction.md) — Programmatic onchain actions with UI
- [Wallet](https://docs.base.org/onchainkit/wallet/wallet.md) — Wallet UI and connection patterns

## API
- [Get Swap Quote](https://docs.base.org/onchainkit/api/get-swap-quote.md) — Quoting endpoint for building swaps
- [Get Portfolios](https://docs.base.org/onchainkit/api/get-portfolios.md) — Wallet portfolio data

## Utilities
- [isBase](https://docs.base.org/onchainkit/config/is-base.md) — Chain helpers for Base
- [Format Amount](https://docs.base.org/onchainkit/token/format-amount.md) — Display utilities for tokens

## Types
- [API Types](https://docs.base.org/onchainkit/api/types.md) — Common API type definitions

## Contribution
- [Contribution Guide](https://docs.base.org/onchainkit/guides/contribution.md) — How to propose changes and report bugs

## Optional
- [AI Prompting Guide](https://docs.base.org/onchainkit/guides/ai-prompting-guide.md) — Patterns for IDEs and LLM copilots
- [Themes](https://docs.base.org/onchainkit/guides/themes.md) — Built-in themes and customization


