---
title: Identity components & utilities Types
description: Glossary of Types in Identity components & utilities.
sidebarTitle: Identity
---

## `AddressReact`

```ts
type AddressReact = {
  address?: Address | null; // The Ethereum address to render.
  className?: string; // Optional className override for top span element.
  isSliced?: boolean; // Determines if the displayed address should be sliced.
  hasCopyAddressOnClick?: boolean; // Defaults to true. Optional boolean to disable copy address on click functionality.
};
```

## `Attestation`

```ts
type Attestation = {
  attester: Address; // the attester who created the attestation.
  decodedDataJson: string; // The attestation data decoded to JSON.
  expirationTime: number; // The Unix timestamp when the attestation expires (0 for no expiration).
  id: string; // The unique identifier of the attestation.
  recipient: Address; // The Ethereum address of the recipient of the attestation.
  revocationTime: number; // The Unix timestamp when the attestation was revoked, if applicable.
  revoked: boolean; // A boolean indicating whether the attestation is revocable or not.
  schemaId: EASSchemaUid; // The schema identifier associated with the attestation.
  time: number; // The Unix timestamp when the attestation was created.
};
```

## `AvatarReact`

```ts
type AvatarReact = {
  address?: Address | null; // The Ethereum address to fetch the avatar for.
  chain?: Chain; // Optional chain for domain resolution
  className?: string; // Optional className override for top div element.
  loadingComponent?: JSX.Element; // Optional custom component to display while the avatar data is loading.
  defaultComponent?: JSX.Element; // Optional custom component to display when no ENS name or avatar is available.
  children?: ReactNode; // Optional attestation by passing Badge component as its children
} & ImgHTMLAttributes<HTMLImageElement>; // Optional additional image attributes to apply to the avatar.
```

## `BadgeReact`

```ts
type BadgeReact = {
  className?: string; // Optional className override for top span element.
  tooltip?: boolean | string; // Controls whether the badge shows a tooltip on hover. When true, the tooltip displays the attestation's name. When a string is provided, that text overrides the default display. Defaults to false.
};
```

## `BaseMainnetName`

```ts
export type BaseMainnetName = `${string}.base.eth`;
```

## `Basename`

```ts
type Basename = BaseMainnetName | BaseSepoliaName;
```

## `BaseSepoliaName`

```ts
type BaseSepoliaName = `${string}.basetest.eth`;
```

## `EASSchemaUid`

```ts
type EASSchemaUid = `0x${string}`;
```

## `EASChainDefinition`

```ts
type EASChainDefinition = {
  easGraphqlAPI: string; // EAS GraphQL API endpoint
  id: number; // blockchain source id
  schemaUids: EASSchemaUid[]; // Array of EAS Schema UIDs
};
```

## `EthBalanceReact`

```ts
type EthBalanceReact = {
  address?: Address;
  className?: string;
};
```

## `GetAddress`

```ts
type GetAddress = {
  name: string | Basename; // Name to resolve
  chain?: Chain; // Optional chain for domain resolution
};
```

## `GetAddressReturnType`

```ts
type GetAddressReturnType = Address | null;
```

## `GetAttestationsOptions`

```ts
type GetAttestationsOptions = {
  schemas?: EASSchemaUid[];
  revoked?: boolean;
  expirationTime?: number;
  limit?: number;
};
```

## `GetAvatar`

```ts
type GetAvatar = {
  ensName: string; // The ENS name to fetch the avatar for.
  chain?: Chain; // Optional chain for domain resolution
};
```

## `GetAvatarReturnType`

```ts
type GetAvatarReturnType = string | null;
```

## `GetName`

```ts
type GetName = {
  address: Address;
  chain?: Chain;
};
```

## `GetNameReturnType`

```ts
type GetNameReturnType = string | null;
```

## `GetNames`

```ts
type GetNames = {
  addresses: Address[]; // Array of Ethereum addresses to resolve names for
  chain?: Chain; // Optional chain for domain resolution
};
```

## `IdentityCardReact`

```ts
type IdentityCardReact = {
  address?: Address;
  chain?: Chain;
  className?: string;
  schemaId?: Address | null;
  badgeTooltip?: boolean | string; // Controls whether the badge shows a tooltip on hover. When true, the tooltip displays the attestation's name. When a string is provided, that text overrides the default display. Defaults to false.
};
```

## `IdentityContextType`

```ts
type IdentityContextType = {
  address: Address; // The Ethereum address to fetch the avatar and name for.
  schemaId?: Address | null; // The Ethereum address of the schema to use for EAS attestation.
};
```

## `IdentityReact`

```ts
type IdentityReact = {
  address?: Address; // The Ethereum address to fetch the avatar and name for.
  chain?: Chain; // Optional chain for domain resolution
  children: ReactNode;
  className?: string; // Optional className override for top div element.
  schemaId?: Address | null; // The Ethereum address of the schema to use for EAS attestation.
  hasCopyAddressOnClick?: boolean; // Optional boolean to disable copy address on click functionality.
};
```

## `NameReact`

```ts
type NameReact = {
  address?: Address | null; // Ethereum address to be displayed.
  children?: ReactNode; // Optional attestation by passing Badge component as its children
  chain?: Chain; // Optional chain for domain resolution
  className?: string; // Optional className override for top span element.
} & HTMLAttributes<HTMLSpanElement>; // Optional additional span attributes to apply to the name.
```

## `UseAddressOptions`

```ts
type UseAddressOptions = {
  name: string | Basename; // The ENS or Basename for which the Ethereum address is to be fetched
  chain?: Chain; // Optional chain for domain resolution
};
```

## `UseAvatarOptions`

```ts
type UseAvatarOptions = {
  ensName: string;
  chain?: Chain; // Optional chain for domain resolution
};
```

## `UseAvatarsOptions`

```ts
type UseAvatarsOptions = {
  ensNames: string[]; // Array of ENS names to resolve avatars for
  chain?: Chain; // Optional chain for domain resolution
};
```

## `UseNameOptions`

```ts
type UseNameOptions = {
  address: Address; // The address for which the ENS or Basename is to be fetched.
  chain?: Chain; // Optional chain for domain resolution
};
```

## `UseNamesOptions`

```ts
type UseNamesOptions = {
  addresses: Address[]; // Array of addresses to resolve ENS or Basenames for
  chain?: Chain; // Optional chain for domain resolution
};
```

