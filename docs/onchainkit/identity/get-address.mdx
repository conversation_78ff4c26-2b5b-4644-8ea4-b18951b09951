---
title: getAddress
---


The `getAddress` utility is designed to retrieve an address from an onchain identity provider for a given name.

## Usage

Get ENS Name from mainnet chain

<CodeGroup>
```tsx code
import { getAddress } from '@coinbase/onchainkit/identity';

const address = await getAddress({ name: 'zizzamia.eth' });
```

```ts return value
******************************************
```
</CodeGroup>

Get Basename from base chain

<CodeGroup>
```tsx code
import { getAddress } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const address = await getAddress({ name: 'zizzamia.base.eth', chain: base });
```

```ts return value
******************************************
```
</CodeGroup>

## Returns

See [`GetAddressReturnType`](/onchainkit/identity/types#getaddressreturntype) and [`GetAddress`](/onchainkit/identity/types#getaddress) for more details.

## Parameters

See [`GetAddressReturnType`](/onchainkit/identity/types#getaddressreturntype) and [`GetAddress`](/onchainkit/identity/types#getaddress) for more details.

