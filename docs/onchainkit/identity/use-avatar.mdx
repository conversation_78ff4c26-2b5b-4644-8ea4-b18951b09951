---
title: useAvatar
---


The `useAvatar` hook is used to get avatar image URL from an onchain identity
provider for a given name.

It is implemented with [useQuery](https://tanstack.com/query/latest/docs/framework/react/reference/useQuery) from `@tanstack/react-query`, and returns a `UseQueryResult` object, allowing you to pass through all `@tanstack/react-query` options.

## Usage

```tsx
import { useAvatar } from '@coinbase/onchainkit/identity';

const { data: avatar, isLoading } = useAvatar({ ensName: 'vitalik.eth' });
```

## Returns

[`useQuery<Promise<GetAddressReturnType>>`](/onchainkit/identity/types#getaddressreturntype)

## Parameters

### UseAvatarOptions

[`UseAvatarOptions`](/onchainkit/identity/types#useavataroptions)

### UseQueryOptions

[`UseQueryOptions`](/onchainkit/identity/types#usequeryoptions)

