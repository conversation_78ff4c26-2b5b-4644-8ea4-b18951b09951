---
title: useName
---


The `useName` hook is used to get name from an onchain identity provider
for a given address.

It is implemented with [useQuery](https://tanstack.com/query/latest/docs/framework/react/reference/useQuery) from `@tanstack/react-query`, and returns a `UseQueryResult` object, allowing you to pass through all `@tanstack/react-query` options.

## Usage

Get ENS name from an address:

<CodeGroup>
```tsx code
import { useName } from '@coinbase/onchainkit/identity';

const address = '******************************************';
const { data: name, isLoading } = await useName({ address });
```

```ts return value
{ data: 'zizzamia.eth', isLoading: false }
```
</CodeGroup>

Get Basename from an address:

<CodeGroup>
```tsx code
import { useName } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const address = '******************************************';
const { data: name, isLoading } = await useName({ address, chain: base });
```

```ts return value
{ data: 'zizzamia.base.eth', isLoading: false }
```
</CodeGroup>

## Returns

[`useQuery<Promise<GetAddressReturnType>>`](/onchainkit/identity/types#getaddressreturntype)

## Parameters

### UseNameOptions

[`UseNameOptions`](/onchainkit/identity/types#usenameoptions)

### UseQueryOptions

[`UseQueryOptions`](/onchainkit/identity/types#usequeryoptions)

