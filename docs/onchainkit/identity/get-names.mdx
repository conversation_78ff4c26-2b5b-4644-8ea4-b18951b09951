---
title: getNames
---


The `getNames` utility is designed to retrieve multiple names from an onchain identity
provider for an array of addresses in a single batch request.

Consider the utility instead of the hook when you
use it with Next.js or any Node.js backend.

## Usage

Get ENS names from multiple addresses:

<CodeGroup>
```tsx code
import { getNames } from '@coinbase/onchainkit/identity';

const addresses = [
  '******************************************',
  '******************************************'
];
const names = await getNames({ addresses });
```

```ts return value
['paulcramer.eth', 'vitalik.eth']
```
</CodeGroup>

Get Basenames from multiple addresses:

<CodeGroup>
```tsx code
import { getNames } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const addresses = [
  '******************************************',
  '******************************************'
];
const names = await getNames({ addresses, chain: base });
```

```ts return value
['paul.base.eth', 'coinbase.base.eth']
```
</CodeGroup>

Get a mix of ENS names and sliced addresses when some addresses don't have names:

<CodeGroup>
```tsx code
import { getNames } from '@coinbase/onchainkit/identity';

const addresses = [
  '******************************************',
  '******************************************'
];
const names = await getNames({ addresses });
```

```ts return value
['paulcramer.eth', null]
```
</CodeGroup>

## Returns

Array of [`GetNameReturnType`](/onchainkit/identity/types#getnamereturntype)

## Parameters

### GetNames

[`GetNames`](/onchainkit/identity/types#getnames) 

