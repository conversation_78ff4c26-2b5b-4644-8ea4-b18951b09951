---
title: getAttestations
---


The `getAttestations` function fetches attestations for a specified address
and blockchain in Ethereum Attestation Service (EAS). It allows optional filtering
based on schema IDs, revocation status, expiration time, and the number of attestations to return.

In the example, we use the Coinbase [Verified Account](https://base.easscan.org/schema/view/0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9) schema ID.

## Usage

<CodeGroup>
```tsx code
// @noErrors: 2345 - Argument of type string is not assignable to 0x{string}
import { getAttestations } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const COINBASE_VERIFIED_ACCOUNT_SCHEMA_ID =
  '0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9';

const address = '******************************************';
const attestationsOptions = {
  schemas: [COINBASE_VERIFIED_ACCOUNT_SCHEMA_ID],
};

const attestations = await getAttestations(address, base, attestationsOptions);
```

```ts return value
const attestations = [
  {
    attester: '0x357458739F90461b99789350868CD7CF330Dd7EE',
    decodedDataJson:
      '[{"name":"verifiedAccount","type":"bool","signature":"bool verifiedAccount","value":{"name":"verifiedAccount","type":"bool","value":true}}]',
    expirationTime: 0,
    id: '0xf1fdb9d102f4b7c1d3b729fc10fea68596301c831913468f3f77f2d631486e12',
    recipient: '0x02feeb0AdE57b6adEEdE5A4EEea6Cf8c21BeB6B1',
    revocationTime: 0,
    revoked: false,
    schemaId: '0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9',
    time: **********,
    timeCreated: **********,
    txid: '0x71e21910fd44c2b75b143cf71318507b60d09c5f3caf848109974dd397de9f37',
  },
];
```
</CodeGroup>

## Returns

[`Promise<Attestation[]>`](/onchainkit/identity/types#attestation)

See [Attestation](/onchainkit/identity/types#attestation) and [GetEASAttestationsOptions](/onchainkit/identity/types#geteasttestationsoptions) for more details.

## Parameters

### Address

```ts
type Address = `0x${string}`; // The address for which attestations are being queried.
```

### Chain

```ts
type Chain = { // The blockchain of interest.
  id: string;
  name: string;
  network: string;
  chainId: number;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: string[];
  blockExplorerUrls: string[];
};
```

### GetAttestationsOptions

[GetEASAttestationsOptions](/onchainkit/identity/types#geteasttestationsoptions)

