---
title: <IdentityCard /> · OnchainKit
sidebarTitle: <IdentityCard />
description: Display user identity information with ENS and chain-specific name resolution
---

The `IdentityCard` component provides a comprehensive way to display user identity information, including ENS names, avatars, and chain-specific name resolution.

## Features
- **Name Resolution:** Resolves both Basenames and ENS names automatically
- **Avatar Support:** Displays ENS and chain-specific avatars
- **Flexible Display:** Customizable layout and styling options
- **Chain-Aware:** Works across different EVM chains that support name resolution
- **Tooltip Support:** Displays attestation information on badge hover

## Usage

### Basic Usage

```tsx
// @errors: 2305
import { IdentityCard } from '@coinbase/onchainkit/identity'; // [!code focus]
import { base } from 'viem/chains';

<IdentityCard // [!code focus] 
  address="******************************************" // [!code focus]
  chain={base} // [!code focus]
  schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
/> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-card--default-identity&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>


### Badge Tooltip

You can enable a tooltip for the attestation badge to provide context about what the badge represents:

```tsx
// @noErrors: 2305 2657
import { IdentityCard } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

<IdentityCard 
  address="******************************************"
  chain={base}
  schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
  badgeTooltip={true} // [!code focus]
/>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-card--badge-tooltip&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <IdentityCard 
    schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
    address="******************************************"
    chain={base}
    className="max-w-[300px]"
    badgeTooltip={true}
  />
</App> */}

You can also provide custom tooltip text:

```tsx
// @noErrors: 2305 2657
import { IdentityCard } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

<IdentityCard 
  address="******************************************"
  chain={base}
  schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
  badgeTooltip="Coinbase Verified" // [!code focus]
/>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-card--badge-tooltip-custom&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <IdentityCard 
    schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
    address="******************************************"
    chain={base}
    className="max-w-[300px]"
    badgeTooltip="Coinbase Verified"
  />
</App> */}

## Customization

You can override styles using `className` or by setting a custom [OnchainKit theme](/onchainkit/guides/themes#custom-theme). You can also set the `mainnet` chain for ENS name resolution:

```tsx
import { IdentityCard } from '@coinbase/onchainkit/identity';
import { mainnet } from 'viem/chains'; // [!code focus]

<OnchainKitProvider
  config={{
    appearance: {
      mode: 'auto',
      theme: 'cyberpunk', // [!code focus]
    },
  }}
>
</OnchainKitProvider>

<IdentityCard 
  address="0x123..."
  chain={mainnet} // [!code focus]
  schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
/>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-card--customization&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <IdentityCard 
    schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
    address="******************************************"
    chain={mainnet}
    className="cyberpunk max-w-[300px]"
  />
</App> */}

## Props

[`IdentityCardReact`](/onchainkit/identity/types#identitycardreact)

| Prop | Type | Description |
|------|------|-------------|
| `address` | `string` | The wallet address to display identity for |
| `chain` | `Chain` | The chain to resolve the identity on |
| `className` | `string` | Additional CSS classes to apply |
| `schemaId` | `Address \| null` | The schema ID for attestation |
| `badgeTooltip` | `boolean \| string` | When `true`, displays the attestation name in tooltip. When a string is provided, shows that custom text instead. Defaults to `false` |

## Error Handling

The component handles various error states gracefully:

- Invalid addresses display a shortened address format
- Missing ENS names fallback to shortened addresses
- Failed avatar fetches show a default avatar
- Network errors maintain a degraded but functional display

## Best Practices

1. Always provide a valid chain object from viem/chains
2. Handle loading states in parent components when address might be undefined
3. Implement proper error boundaries in parent components
4. Consider mobile responsiveness when styling
5. Use `badgeTooltip` to provide context about what the verification badge represents

## Related Components

- [`<Avatar>`](/onchainkit/identity/avatar) - Displays user avatars
- [`<Name>`](/onchainkit/identity/name) - Displays resolved names
- [`<Identity>`](/onchainkit/identity/identity) - Simplified identity display


