---
title: Next.js Installation · OnchainKit
sidebarTitle: Next.js Installation
description: Install OnchainKit using Next.js
---

import StartBuilding from "/snippets/start-building.mdx";

Install and configure OnchainKit with Next.js.
If you are integrating OnchainKit into an existing project,
skip to the [OnchainKit installation](#install-onchainkit).

<Steps>
  <Step title="Install Next.js">
  Create a new Next.js project by using the Next.js CLI.
  More information about Next.js can be found [here](https://nextjs.org/docs/getting-started/installation).

  ```bash npm
  npx create-next-app@14
  ```

  During the setup process you will encounter multiple prompts.
  Make sure you enable TypeScript, ESLint, and Tailwind CSS.
  </Step>
  <Step title="Install OnchainKit">
  Install OnchainKit in your project.

  <CodeGroup>
  ```bash npm
  npm install @coinbase/onchainkit
  ```

  ```bash yarn
  yarn add @coinbase/onchainkit
  ```

  ```bash pnpm
  pnpm add @coinbase/onchainkit
  ```

  ```bash bun
  bun add @coinbase/onchainkit
  ```
  </CodeGroup>
  </Step>
  <Step title="Get Your Client API Key">
  Get your [Client API Key](https://portal.cdp.coinbase.com/projects/api-keys/client-key) from Coinbase Developer Platform.

<Frame>
  <img
    alt="OnchainKit copy Client API Key"
    src="/images/onchainkit/copy-api-key-guide.png"
    height="364"
  />
</Frame>

  Create a `.env` file in your project's root directory.

<Frame>
  <img
    alt="OnchainKit define Client API Key"
    src="/images/onchainkit/getting-started-create-env-file.png"
    width="250"
    loading="lazy"
  />
</Frame>

  Add your Client API Key to the `.env` file:

  ```tsx .env
  NEXT_PUBLIC_ONCHAINKIT_API_KEY=YOUR_CLIENT_API_KEY;
  ```
  </Step>
  <Step title="Add Providers">
  Create a `providers.tsx` file. Add `OnchainKitProvider` with your desired config.

  Under the hood, OnchainKit will create our recommended Wagmi and QueryClient
  providers. If you wish to customize these providers, check out [Custom
  Supplemental Providers](/onchainkit/config/supplemental-providers).

  ```tsx providers.tsx
  // @noErrors: ************** - cannot find 'process', cannot find './wagmi', cannot find 'import.meta'
  'use client';

  import type { ReactNode } from 'react';
  import { OnchainKitProvider } from '@coinbase/onchainkit';
  import { base } from 'wagmi/chains'; // add baseSepolia for testing // [!code ++]

  export function Providers(props: { children: ReactNode }) {
    return (
      <OnchainKitProvider
        apiKey={process.env.NEXT_PUBLIC_ONCHAINKIT_API_KEY} // [!code ++]
        chain={base} // add baseSepolia for testing // [!code ++]
      >
        {props.children}
      </OnchainKitProvider>
    );
  }
  ```
  </Step>
  <Step title="Wrap your app with <Providers />">
  After the setup, wrap your app with the above `<Providers />` component.

  ```javascript app.tsx
  import './globals.css';
  import { Providers } from './providers'; // [!code ++]

  export default function RootLayout({
    children,
  }: Readonly<{
    children: React.ReactNode,
  }>) {
    return (
      <html lang="en">
        <body>
          <Providers> 
            {children}
          </Providers>
        </body>
      </html>
    );
  }
  ```
  </Step>
  <Step title="Add Styles">
  OnchainKit components come with pre-configured styles. To include these styles in your project, add the following import statement at the top of this file:

  ```javascript
  import '@coinbase/onchainkit/styles.css';
  ```

  For example, if you're using Next.js with the app router, your `app/layout.tsx` might look like this:

  ```tsx layout.tsx
  import '@coinbase/onchainkit/styles.css'; // [!code ++]
  import './globals.css';
  import type { Metadata } from 'next';
  import { Inter } from 'next/font/google';
  import { headers } from 'next/headers';
  import { type ReactNode } from 'react';
  import { cookieToInitialState } from 'wagmi';

  import { getConfig } from '../wagmi';
  import { Providers } from './providers';

  const inter = Inter({ subsets: ['latin'] });

  export const metadata: Metadata = {
    title: 'Create Wagmi',
    description: 'Generated by create-wagmi',
  };

  export default function RootLayout(props: { children: ReactNode }) {
    const initialState = cookieToInitialState(
      getConfig(),
      headers().get('cookie')
    );
    return (
      <html lang="en">
        <body className={inter.className}>
          <Providers initialState={initialState}>{props.children}</Providers>
        </body>
      </html>
    );
  }
  ```

  This ensures that the OnchainKit styles are loaded and applied to your entire application.

  - For Tailwind CSS users, check out our [Tailwind Integration Guide](/onchainkit/guides/tailwind).

  - Update the appearance of components by using our built-in themes or crafting your own custom theme.
    Explore the possibilities in our [Theming Guide](/onchainkit/guides/themes).

  </Step>
</Steps>

<StartBuilding />

