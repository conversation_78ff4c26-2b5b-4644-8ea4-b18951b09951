---
title: Vite Installation · OnchainKit
sidebarTitle: Vite Installation
description: Install OnchainKit using Vite
---

import StartBuilding from "/snippets/start-building.mdx";

Install and configure OnchainKit with Vite.
If you are integrating OnchainKit into an existing project,
skip to the [OnchainKit installation](#install-onchainkit).

<Steps>
  <Step title="Install Vite">
  Create a new Vite project by using the Vite CLI.
  More information about Vite can be found [here](https://vite.dev/guide/#scaffolding-your-first-vite-project).

  <CodeGroup>
  ```bash npm
  npm create vite@latest
  ```

  ```bash yarn
  yarn create vite
  ```

  ```bash pnpm
  pnpm create vite
  ```

  ```bash bun
  bun create vite
  ```
  </CodeGroup>

  During the setup process you will encounter multiple prompts.
  Make sure you select React and TypeScript.
  </Step>
  <Step title="Install OnchainKit">

  Add OnchainKit to your project by installing the `@coinbase/onchainkit` package.

  <CodeGroup>
  ```bash npm
  npm install @coinbase/onchainkit
  ```

  ```bash yarn
  yarn add @coinbase/onchainkit
  ```

  ```bash pnpm
  pnpm add @coinbase/onchainkit
  ```

  ```bash bun
  bun add @coinbase/onchainkit
  ```
  </CodeGroup>
  </Step>
  <Step title="Get A Client API Key">
  Get your [Client API Key](https://portal.cdp.coinbase.com/projects/api-keys/client-key) from Coinbase Developer Platform.

<Frame>
  <img
    alt="OnchainKit copy Client API Key"
    src="/images/onchainkit/copy-api-key-guide.png"
    height="364"
  />
</Frame>

  Create a `.env` file in your project's root directory.

<Frame>
  <img
    alt="OnchainKit define Client API Key"
    src="/images/onchainkit/getting-started-create-env-file.png"
    width="250"
    loading="lazy"
  />
</Frame>

  Add your Client API Key to the `.env` file:

  ```dotenv .env
  VITE_PUBLIC_ONCHAINKIT_API_KEY=YOUR_CLIENT_API_KEY
  ```
  </Step>
  <Step title="Add Providers">

  Create a `providers.tsx` file. Add `OnchainKitProvider` with your desired config.

  Under the hood, OnchainKit will create our recommended Wagmi and QueryClient
  providers. If you wish to customize these providers, check out [Custom
  Supplemental Providers](/onchainkit/config/supplemental-providers).

  ```tsx providers.tsx
  // @noErrors: ************** - cannot find 'process', cannot find './wagmi', cannot find 'import.meta'
  'use client';

  import type { ReactNode } from 'react';
  import { OnchainKitProvider } from '@coinbase/onchainkit';
  import { base } from 'wagmi/chains'; // add baseSepolia for testing // [!code ++]

  export function Providers(props: { children: ReactNode }) {
    return (
      <OnchainKitProvider
        apiKey={import.meta.env.VITE_PUBLIC_ONCHAINKIT_API_KEY} // [!code ++]
        chain={base} // add baseSepolia for testing // [!code ++]
      >
        {props.children}
      </OnchainKitProvider>
    );
  }
  ```
  </Step>
  <Step title="Wrap your app with <AppProviders />">
  After configuring the providers in step 4, wrap your app with
  the `<AppProviders />` component.

  ```tsx app.tsx
  import { AppProviders } from 'src/AppProviders';

  export default function App() {
    return (
      <AppProviders>
        <YourApp />
      </AppProviders>
    );
  }
  ```
  </Step>
  <Step title="Import OnchainKit Styles">
OnchainKit components come with pre-configured styles.
  To include these styles in your project, add the following import
  statement at the top of the file where imported `<AppProviders />`:

  ```tsx app.tsx
  import { AppProviders } from 'src/AppProviders';
  import '@coinbase/onchainkit/styles.css'; // [!code ++]

  export default function App() {
    return (
      <AppProviders>
        <YourApp />
      </AppProviders>
    );
  }
  ```

  This ensures that the OnchainKit styles are loaded and applied to your entire application.

  - For Tailwind CSS users, check out our [Tailwind Integration Guide](/onchainkit/guides/tailwind).

  - Update the appearance of components by using our built-in themes or crafting your own custom theme.
    Explore the possibilities in our [Theming Guide](/onchainkit/guides/themes).

  </Step>
</Steps>

<StartBuilding />

