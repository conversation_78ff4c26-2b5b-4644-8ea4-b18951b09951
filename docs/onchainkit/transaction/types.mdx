---
title: Transaction components & utilities Types
description: Glossary of Types in Transaction components & utilities.
sidebarTitle: Transaction
---

## `Call`

```ts
type Call = { to: Hex; data?: Hex; value?: bigint };
```

## `Calls`

```ts
type Calls = Call[] | Promise<Call[]> | (() => Promise<Call[]>);
```

## `Contracts`

```ts
type Contracts =
  | ContractFunctionParameters[]
  | Promise<ContractFunctionParameters[]>
  | (() => Promise<ContractFunctionParameters[]>);
```

## `LifecycleStatus`

```ts
type LifecycleStatus =
  | {
      statusName: 'init';
      statusData: null;
    }
  | {
      statusName: 'error';
      statusData: TransactionError;
    }
  | {
      statusName: 'transactionIdle'; // initial status prior to the mutation function executing
      statusData: null;
    }
  | {
      statusName: 'buildingTransaction'; // resolving calls or contracts promise
      statusData: null;
    }
  | {
      statusName: 'transactionPending'; // if the mutation is currently executing
      statusData: null;
    }
  | {
      statusName: 'transactionLegacyExecuted';
      statusData: {
        transactionHashList: Address[];
      };
    }
  | {
      statusName: 'success'; // if the last mutation attempt was successful
      statusData: {
        transactionReceipts: TransactionReceipt[];
      };
    };
```

## `TransactionButtonReact`

```ts
type TransactionButtonReact = {
  className?: string; // An optional CSS class name for styling the button component.
  disabled?: boolean; // A optional prop to disable the submit button
  text?: string; // An optional text to be displayed in the button component.
};
```

## `TransactionError`

```ts
type TransactionError = {
  code: string; // The error code representing the type of transaction error.
  error: string; // The error message providing details about the transaction error.
  message: string; // The error message providing details about the transaction error.
};
```

## `TransactionReact`

```ts
type TransactionReact = {
  calls?: Calls | Contracts | (Call | ContractFunctionParameters)[]; // An array of calls to be made in the transaction.
  isSponsored?: boolean; // Whether or not the transaction is sponsored.
  /** @deprecated Use `isSponsored` instead. */
  capabilities?: WalletCapabilities; // Capabilities that a wallet supports (e.g. paymasters, session keys, etc).
  chainId?: number; // The chainId for the transaction.
  className?: string; // An optional CSS class name for styling the component.
  contracts?: Calls | Contracts | (Call | ContractFunctionParameters)[]; // An array of calls to be made in the transaction.
  onError?: (e: TransactionError) => void; // An optional callback function that handles transaction errors.
  onStatus?: (lifecycleStatus: LifecycleStatus) => void; // An optional callback function that exposes the component lifecycle state
  onSuccess?: (response: TransactionResponse) => void; // An optional callback function that exposes the transaction receipts
} & (
  | {
      children: ReactNode;
      /** An optional prop to disable submit button. Only available when children are not provided. */
      disabled?: never;
    }
  | {
      children?: never;
      /** An optional prop to disable submit button. Only available when children are not provided. */
      disabled?: boolean;
    }
);
```

## `TransactionDefaultReact`

```ts
export type TransactionDefaultReact = {
  disabled?: boolean;
} & Omit<TransactionReact, 'children'>;
```

## `TransactionResponse`

```ts
type TransactionResponse = {
  transactionReceipts: TransactionReceipt[]; // An array containing the transaction receipts
};
```

## `TransactionSponsorReact`

```ts
type TransactionSponsorReact = {
  className?: string; // An optional CSS class name for styling the sponsor component.
};
```

## `TransactionStatusReact`

```ts
type TransactionStatusReact = {
  children?: ReactNode; // The child components to be rendered within the status component.
  className?: string; // An optional CSS class name for styling the status component.
};
```

## `TransactionStatusActionReact`

```ts
type TransactionStatusActionReact = {
  className?: string; // An optional CSS class name for styling.
};
```

## `TransactionStatusLabelReact`

```ts
type TransactionStatusLabelReact = {
  className?: string; // An optional CSS class name for styling.
};
```

## `TransactionToastReact`

```ts
type TransactionToastReact = {
  children?: ReactNode; // The child components to be rendered within the toast component.
  className?: string; // An optional CSS class name for styling the toast component.
  durationMs?: number; // An optional value to customize time until toast disappears
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right'; // An optional position property to specify the toast's position on the screen.
};
```

## `TransactionToastActionReact`

```ts
type TransactionToastActionReact = {
  className?: string; // An optional CSS class name for styling.
};
```

## `TransactionToastIconReact`

```ts
type TransactionToastIconReact = {
  className?: string; // An optional CSS class name for styling.
};
```

## `TransactionToastLabelReact`

```ts
type TransactionToastLabelReact = {
  className?: string; // An optional CSS class name for styling.
};
```

## `WalletCapabilities`

```ts
type WalletCapabilities = {
  paymasterService?: PaymasterService;
};
```
