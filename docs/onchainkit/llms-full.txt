# https://docs.base.org/onchainkit/llms-full.txt

## OnchainKit — Deep Guide for LLMs

> OnchainKit is a React/TypeScript SDK that ships production‑ready components, hooks, and APIs to build onchain apps fast, with first‑class support for Base.

### What you can do here
- Install and configure OnchainKit in Next.js/Vite
- Use the OnchainKitProvider to wire chains, API keys, and appearance
- Compose wallet, transaction, swap, mint, and identity components
- Call server APIs for quotes, tokens, portfolios
- Test E2E with OnchainTestKit

## Minimal Critical Code (provider)
```tsx
import { OnchainKitProvider } from '@coinbase/onchainkit'
import { base } from 'wagmi/chains'

export function Providers(props: { children: React.ReactNode }) {
  return (
    <OnchainKitProvider apiKey={process.env.NEXT_PUBLIC_ONCHAINKIT_API_KEY} chain={base}>
      {props.children}
    </OnchainKitProvider>
  )
}
```

## Navigation (with brief descriptions)

### Introduction
- [Getting Started](https://docs.base.org/onchainkit/getting-started.md) — Install, scaffold, test
- [Telemetry](https://docs.base.org/onchainkit/guides/telemetry.md) — Metrics
- [Troubleshooting](https://docs.base.org/onchainkit/guides/troubleshooting.md) — Common issues

### Installation
- [Next.js](https://docs.base.org/onchainkit/installation/nextjs.md) — Install in Next.js
- [Vite](https://docs.base.org/onchainkit/installation/vite.md) — Install in Vite
- [Remix](https://docs.base.org/onchainkit/installation/remix.md) — Install in Remix
- [Astro](https://docs.base.org/onchainkit/installation/astro.md) — Install in Astro

### Config
- [OnchainKitProvider](https://docs.base.org/onchainkit/config/onchainkit-provider.md) — Provider config
- [Supplemental Providers](https://docs.base.org/onchainkit/config/supplemental-providers.md) — Custom providers
- [Types](https://docs.base.org/onchainkit/config/types.md) — Config types

### Guides
- [Lifecycle Status](https://docs.base.org/onchainkit/guides/lifecycle-status.md) — Stability
- [Tailwind](https://docs.base.org/onchainkit/guides/tailwind.md) — Styling
- [Themes](https://docs.base.org/onchainkit/guides/themes.md) — Theming
- [Use Basename](https://docs.base.org/onchainkit/guides/use-basename-in-onchain-app.md) — Basename integration
- [Using AI IDEs](https://docs.base.org/onchainkit/guides/using-ai-powered-ides.md) — Copilot patterns
- [AI Prompting Guide](https://docs.base.org/onchainkit/guides/ai-prompting-guide.md) — Prompt patterns
- [Testing with OnchainTestKit](https://docs.base.org/onchainkit/guides/testing-with-onchaintestkit.md) — E2E
- [Reporting Bug](https://docs.base.org/onchainkit/guides/reporting-bug.md) — Issues
- [Contribution](https://docs.base.org/onchainkit/guides/contribution.md) — Contribute

### Templates
- [Onchain NFT App](https://docs.base.org/onchainkit/templates/onchain-nft-app.md)
- [Onchain Commerce App](https://docs.base.org/onchainkit/templates/onchain-commerce-app.md)
- [Onchain Social Profile](https://docs.base.org/onchainkit/templates/onchain-social-profile.md)

### Components (selected)
- [Appchain/Bridge](https://docs.base.org/onchainkit/appchain/bridge.md) — Bridge widget
- [Buy](https://docs.base.org/onchainkit/buy/buy.md) — Onramp UI
- [Checkout](https://docs.base.org/onchainkit/checkout/checkout.md) — Commerce flows
- [Earn](https://docs.base.org/onchainkit/earn/earn.md) — Yield UI
- [Fund Button/Card](https://docs.base.org/onchainkit/fund/fund-button.md) — Onramp components
- [Identity](https://docs.base.org/onchainkit/identity/identity.md) — Identity components
- [Mint](https://docs.base.org/onchainkit/mint/nft-card.md) — NFT UI
- [Signature](https://docs.base.org/onchainkit/signature/signature.md) — Sign flows
- [Swap](https://docs.base.org/onchainkit/swap/swap.md) — Swap UI
- [Token](https://docs.base.org/onchainkit/token/token-chip.md) — Token UI
- [Transaction](https://docs.base.org/onchainkit/transaction/transaction.md) — Transactions
- [Wallet](https://docs.base.org/onchainkit/wallet/wallet.md) — Wallet UI

### API
- [Mint: Build Tx](https://docs.base.org/onchainkit/api/build-mint-transaction.md)
- [Swap: Build Tx](https://docs.base.org/onchainkit/api/build-swap-transaction.md)
- [Swap: Quote](https://docs.base.org/onchainkit/api/get-swap-quote.md)
- [Token: List](https://docs.base.org/onchainkit/api/get-tokens.md)
- [Wallet: Portfolios](https://docs.base.org/onchainkit/api/get-portfolios.md)

### Utilities
- [Earn: build deposit/withdraw tx](https://docs.base.org/onchainkit/api/build-deposit-to-morpho-tx.md)
- [Fund: onramp helpers](https://docs.base.org/onchainkit/fund/get-onramp-buy-url.md)
- [Identity: lookups and hooks](https://docs.base.org/onchainkit/identity/get-address.md)
- [Mint: hooks](https://docs.base.org/onchainkit/hooks/use-mint-details.md)
- [Token: formatting](https://docs.base.org/onchainkit/token/format-amount.md)
- [Wallet: helpers](https://docs.base.org/onchainkit/wallet/is-wallet-a-coinbase-smart-wallet.md)

### Types
- [API](https://docs.base.org/onchainkit/api/types.md), [Appchain](https://docs.base.org/onchainkit/appchain/types.md), [Checkout](https://docs.base.org/onchainkit/checkout/types.md), [Config](https://docs.base.org/onchainkit/config/types.md), [Earn](https://docs.base.org/onchainkit/earn/types.md), [Fund](https://docs.base.org/onchainkit/fund/types.md), [Identity](https://docs.base.org/onchainkit/identity/types.md), [Mint](https://docs.base.org/onchainkit/mint/types.md), [Signature](https://docs.base.org/onchainkit/signature/types.md), [Swap](https://docs.base.org/onchainkit/swap/types.md), [Token](https://docs.base.org/onchainkit/token/types.md), [Transaction](https://docs.base.org/onchainkit/transaction/types.md), [Wallet](https://docs.base.org/onchainkit/wallet/types.md)


## Quickstart (excerpts)

Source: `https://docs.base.org/onchainkit/getting-started.md`

Install and wire the provider:

```bash
npm install @coinbase/onchainkit wagmi viem
```

```tsx
import { OnchainKitProvider } from '@coinbase/onchainkit'
import { base } from 'wagmi/chains'

export function Providers(props: { children: React.ReactNode }) {
  return (
    <OnchainKitProvider apiKey={process.env.NEXT_PUBLIC_ONCHAINKIT_API_KEY} chain={base}>
      {props.children}
    </OnchainKitProvider>
  )
}
```

Minimal wallet + transaction UI:

Source: `https://docs.base.org/onchainkit/wallet/wallet.md` and `https://docs.base.org/onchainkit/transaction/transaction.md`

```tsx
import { Wallet } from '@coinbase/onchainkit/wallet'
import { Transaction } from '@coinbase/onchainkit/transaction'

export function Checkout() {
  return (
    <div>
      <Wallet />
      <Transaction calls={[{ to: USDC, data: erc20.approve(MERCHANT, AMOUNT) }]} />
    </div>
  )
}
```


## Key Concepts (excerpts)

Source: `https://docs.base.org/onchainkit/config/onchainkit-provider.md`

- Provider role: Central place to configure chain, API key, appearance, and integrations. Keep it high in the tree to enable components and hooks.
- Lifecycle status: Components and APIs expose stability levels to guide production usage.
  - Source: `https://docs.base.org/onchainkit/guides/lifecycle-status.md`
- Composition model: Building blocks (Wallet, Transaction, Swap, Mint, Identity) compose into product flows with sensible defaults.
- Server APIs: Quote and transaction‑builder endpoints power client components, reducing onchain logic in the browser.


## API (selected, pruned)

OnchainKit interface

- Get Swap Quote — `GET /v1/swap/quote?fromToken=...&toToken=...&amount=...`
  - Source: `https://docs.base.org/onchainkit/api/get-swap-quote.md`
- Build Swap Transaction — `POST /v1/swap/build`
  - Source: `https://docs.base.org/onchainkit/api/build-swap-transaction.md`
- Get Tokens — `GET /v1/tokens`
  - Source: `https://docs.base.org/onchainkit/api/get-tokens.md`
- Get Portfolios — `GET /v1/wallets/{address}/portfolios`
  - Source: `https://docs.base.org/onchainkit/api/get-portfolios.md`

Component props and types

- Transaction — core props include `calls`, `onStatus`, `appearance`
  - Source: `https://docs.base.org/onchainkit/transaction/transaction.md`
- Wallet — `onConnect`, `onDisconnect`, `appearance`
  - Source: `https://docs.base.org/onchainkit/wallet/wallet.md`


## Examples (common flows)

Example: Fetch and display a swap quote, then build a tx

Sources:
- `https://docs.base.org/onchainkit/api/get-swap-quote.md`
- `https://docs.base.org/onchainkit/api/build-swap-transaction.md`

```ts
const quote = await fetch(`/api/ock/swap/quote?fromToken=${from}&toToken=${to}&amount=${amount}`).then(r => r.json())
const build = await fetch('/api/ock/swap/build', { method: 'POST', body: JSON.stringify({ quote }) }).then(r => r.json())
```

Example: Programmatic transaction calls

Source: `https://docs.base.org/onchainkit/transaction/transaction.md`

```tsx
<Transaction
  calls={[
    { to: USDC, data: erc20.approve(SPENDER, AMOUNT) },
    { to: SPENDER, data: spender.purchase(AMOUNT) }
  ]}
  onStatus={s => console.log(s)}
/>
```

