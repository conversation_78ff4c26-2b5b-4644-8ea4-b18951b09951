---
title: formatAmount
---


The `formatAmount` utility is designed for consistent number formatting.

## Usage

<CodeGroup>
```tsx code
import { formatAmount } from '@coinbase/onchainkit/token';

const amount = formatAmount('10000', { minimumFractionDigits: 2 });
```

```ts return value
'10,000.00'; // if in U.S. English locale

'10.000,00'; // if in EU country locale
```
</CodeGroup>

## Returns

`string` - The formatted amount.

## Parameters

[`FormatAmountOptions`](/onchainkit/token/types#formatamountoptions)
