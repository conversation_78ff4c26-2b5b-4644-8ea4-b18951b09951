---
title: "`<TokenRow />`"
---

The `TokenRow` component displays token information in a row format to be used in list components.

## Usage

Token with an image url

```tsx
// @noErrors: 1109 - Expression expected
import { TokenRow } from '@coinbase/onchainkit/token';

const token = { ... };

<TokenRow token={token} />; // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-row--default&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <TokenRow
    token={{
      address: '0x1234',
      chainId: 1,
      decimals: 18,
      image:
        'https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png',
      name: '<PERSON><PERSON><PERSON>',
      symbol: 'ETH',
    }}
  />
</App> */}

Token without an image url

```tsx
// @noErrors: 1109 - Expression expected
import { TokenRow } from '@coinbase/onchainkit/token';

const token = { ... };

<TokenRow token={token} />; // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-row--null&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <TokenRow
    token={{
      address: '0x1234',
      chainId: 1,
      decimals: 18,
      image: null,
      name: 'Ethereum',
      symbol: 'ETH',
    }}
    amount="1000.00234"
  />
</App> */}

Token with an amount

```tsx
// @noErrors: 1109 - Expression expected
import { TokenRow } from '@coinbase/onchainkit/token';

const token = { ... };

<TokenRow token={token} amount="1000" />; // [!code focus]
<TokenRow token={token} amount="0.00234567" />; // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-row--amount&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <TokenRow
    token={{
      address: '0x1234',
      chainId: 1,
      decimals: 18,
      image:
        'https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png',
      name: 'Ethereum',
      symbol: 'ETH',
    }}
    amount="1000"
  />
  <TokenRow
    token={{
      address: '0x1234',
      chainId: 1,
      decimals: 18,
      image:
        'https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png',
      name: 'Ethereum',
      symbol: 'ETH',
    }}
    amount="0.00234567"
  />
</App> */}

Variations with `hideImage` and `hideSymbol`

```tsx
// @noErrors: 1109 - Expression expected
import { TokenRow } from '@coinbase/onchainkit/token';

const token = { ... };

<TokenRow token={token} hideSymbol />; // [!code focus]
<TokenRow token={token} hideImage />; // [!code focus]
<TokenRow token={token} hideSymbol hideImage />; // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-row--hide-symbol&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <TokenRow
    token={{
      address: '0x1234',
      chainId: 1,
      decimals: 18,
      image:
        'https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png',
      name: 'Ethereum',
      symbol: 'ETH',
    }}
    hideSymbol
  /> */}

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-row--hide-image&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>

{/* <TokenRow
  token={{
    address: '0x1234',
    chainId: 1,
    decimals: 18,
    image:
      'https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png',
    name: 'Ethereum',
    symbol: 'ETH',
  }}
  hideImage
/> */}

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-row--hide-both&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
  {/* <TokenRow
    token={{
      address: '0x1234',
      chainId: 1,
      decimals: 18,
      image:
        'https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png',
      name: 'Ethereum',
      symbol: 'ETH',
    }}
    hideSymbol
    hideImage
  />
</App> */}

## Props

[`TokenRowReact`](/onchainkit/token/types#tokenrowreact)
