---
title: Token components & utilities Types
description: Glossary of Types in Token components & utilities.
sidebarTitle: Token
---

## `FormatAmountOptions`

```ts
type FormatAmountOptions = {
  locale?: string; // User locale (default: browser locale)
  minimumFractionDigits?: number; // Minimum fraction digits for number decimals (default: 0)
  maximumFractionDigits?: number; // Maximum fraction digits for number decimals (default: 0)
};
```

## `FormatAmountResponse`

```ts
type FormatAmountResponse = string; // See Number.prototype.toLocaleString for more info
```

## `Token`

```ts
type Token = {
  address: Address; // The address of the token contract
  chainId: number; // The chain id of the token contract
  decimals: number; // The number of token decimals
  image: string | null; // A string url of the token logo
  name: string;
  symbol: string; // A ticker symbol or shorthand, up to 11 characters
};
```

## `TokenChipReact`

```ts
type TokenChipReact = {
  token: Token; // Rendered token
  onClick?: (token: Token) => void;
  className?: string;
  isPressable?: boolean; // Default: true
};
```

## `TokenImageReact`

```ts
type TokenImageReact = {
  className?: string; // Optional additional CSS class to apply to the component
  size?: number; // size of the image in px (default: 24)
  token: Token;
};
```

## `TokenRowReact`

```ts
type TokenRowReact = {
  amount?: string; // Token amount
  className?: string;
  hideImage?: boolean;
  hideSymbol?: boolean;
  onClick?: (token: Token) => void; // Component on click handler
  token: Token; // Rendered token
};
```

## `TokenSearchReact`

```ts
type TokenSearchReact = {
  className?: string;
  delayMs?: number; // Debounce delay in milliseconds
  onChange: (value: string) => void; // Search callback function
};
```

## `TokenSelectButtonReact`

```ts
type TokenSelectDropdownReact = {
  className?: string;
  isOpen: boolean; // Determines carot icon direction
  onClick: () => void; // Button on click handler
  token?: Token; // Selected token
};
```

## `TokenSelectDropdownReact`

```ts
type TokenSelectDropdownReact = {
  options: Token[]; // List of tokens
  setToken: (token: Token) => void; // Token setter
  token?: Token; // Selected token
};
```

## `TokenSelectModalReact`

```ts
type TokenSelectModalReact = {
  options: Token[]; // List of tokens
  setToken: (token: Token) => void; // Token setter
  token?: Token; // Selected token
};
```
