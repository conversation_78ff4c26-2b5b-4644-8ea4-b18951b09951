---
title: useBuildDepositToMorphoTx
---


The `useBuildDepositToMorphoTx` hook is used to build a deposit transaction to Morpho from within a React component.

The hook handles the following:

- Fetching data required to build the transaction
- Using [`buildDepositToMorphoTx`](/onchainkit/api/build-deposit-to-morpho-tx) to build the transaction
- Returning the transaction calls to be used with the `<Transaction />` component

## Usage

```tsx code
import { useBuildDepositToMorphoTx } from '@coinbase/onchainkit/earn';

const { calls } = useBuildDepositToMorphoTx({
  vaultAddress: '0x...',
  recipientAddress: '0x...',
  amount: '1000000000000000000',
});
```

## Returns

```ts
{
  calls: Call[];
}

type Call = { 
  to: Hex; 
  data?: Hex; 
  value?: bigint; 
};
```

## Parameters

```ts
type UseBuildDepositToMorphoTxParams = {
  vaultAddress: Address;
  recipientAddress?: Address;
  amount: string;
};
```
