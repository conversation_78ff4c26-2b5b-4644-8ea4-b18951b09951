---
title: useMorph<PERSON>Vault
---


The `useMorpho<PERSON>ault` hook fetches and returns comprehensive data about a Morpho vault, including APYs, balances, and rewards.

## Usage

```tsx
import { useMorphoVault } from '@coinbase/onchainkit/earn';

const {
  balance,
  totalApy,
  rewards,
  deposits,
  liquidity,
  // ... other values
} = useMorphoVault({
  vaultAddress: '0x...',
  recipientAddress: '0x...',
});
```

## Returns

```ts
type UseMorphoVaultReturnType = {
  vaultName: string | undefined;
  status: 'pending' | 'success' | 'error';
  /** Warns users if vault address is invalid */
  error: Error | null;
  /** Underlying asset of the vault */
  asset: {
    address: Address;
    symbol: string | undefined;
    decimals: number | undefined;
  };
  /** User's deposits in the vault, adjusted for decimals */
  balance: string | undefined;
  balanceStatus: 'pending' | 'success' | 'error';
  refetchBalance: () => void;
  /** Total net APY of the vault after all rewards and fees */
  totalApy: number | undefined;
  /** Native rewards of the vault (e.g. USDC if the asset is USDC) */
  nativeApy: number | undefined;
  /** Additional rewards (e.g. MORPHO) */
  rewards:
    | Array<{
        asset: Address;
        assetName: string;
        apy: number;
      }>
    | undefined;
  /** Vault fee, in percent (e.g. 0.03 for 3%) */
  vaultFee: number | undefined;
  /** Number of decimals of the vault's share tokens (not underlying asset) */
  vaultDecimals: number | undefined;
  /** Total deposits in the vault */
  deposits: string | undefined;
  /** Total liquidity available to borrow in the vault */
  liquidity: string | undefined;
};
```

## Parameters

```ts
type UseMorphoVaultParams = {
  vaultAddress: Address;
  recipientAddress?: Address;
};
```

