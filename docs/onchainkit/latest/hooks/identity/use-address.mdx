---
title: useAddress
---

The `useAddress` hook is used to get an address from an onchain identity provider for a given name.

It is implemented with [useQuery](https://tanstack.com/query/latest/docs/framework/react/reference/useQuery) from `@tanstack/react-query`, and returns a `UseQueryResult` object, allowing you to pass through all `@tanstack/react-query` options.

## Usage

```tsx
import { useAddress } from '@coinbase/onchainkit/identity';

const { data: address, isLoading } = useAddress({ name: 'zizzamia.base.eth' });
```

## Returns

```ts
useQuery<Promise<GetAddressReturnType>>

type GetAddressReturnType = Address | null;
```

## Parameters

### UseAddressParams

```ts
type UseAddressParams = {
  /** The ENS or Basename for which the Ethereum address is to be fetched */
  name: string | Basename;
  /** Optional chain for domain resolution */
  chain?: Chain;
};
```

### UseQueryOptions

```ts
type UseQueryOptions<TData = unknown> = Omit<
  TanstackUseQueryOptions<TData>,
  'queryKey' | 'queryFn'
>;
```
