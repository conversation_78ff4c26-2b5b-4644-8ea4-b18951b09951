---
title: useName
---

The `useName` hook is used to get name from an onchain identity provider
for a given address.

It is implemented with [useQuery](https://tanstack.com/query/latest/docs/framework/react/reference/useQuery) from `@tanstack/react-query`, and returns a `UseQueryResult` object, allowing you to pass through all `@tanstack/react-query` options.

## Usage

Get ENS name from an address:

<CodeGroup>
```tsx code
import { useName } from '@coinbase/onchainkit/identity';

const address = '******************************************';
const { data: name, isLoading } = await useName({ address });

````

```ts return value
{ data: 'zizzamia.eth', isLoading: false }
````

</CodeGroup>

Get Basename from an address:

<CodeGroup>
```tsx code
import { useName } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const address = '******************************************';
const { data: name, isLoading } = await useName({ address, chain: base });

````

```ts return value
{ data: 'zizzamia.base.eth', isLoading: false }
````

</CodeGroup>

## Returns

```ts
useQuery<Promise<GetNameReturnType>>

type GetNameReturnType = string | Basename | null;
```

## Parameters

### UseNameParams

```ts
type UseNameParams = {
  /** The address for which the ENS or Basename is to be fetched. */
  address?: Address;
  /** Optional chain for domain resolution */
  chain?: Chain;
};
```

### UseQueryOptions

```ts
type UseQueryOptions<TData = unknown> = Omit<
  TanstackUseQueryOptions<TData>,
  'queryKey' | 'queryFn'
>;
```
