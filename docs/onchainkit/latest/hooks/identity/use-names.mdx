---
title: useNames
---

The `useNames` hook is used to get multiple names from an onchain identity provider
for an array of addresses in a single batch request.

It is implemented with [useQuery](https://tanstack.com/query/latest/docs/framework/react/reference/useQuery) from `@tanstack/react-query`, and returns a `UseQueryResult` object, allowing you to pass through all `@tanstack/react-query` options.

## Usage

Get ENS names from multiple addresses:

<CodeGroup>
```tsx code
import { useNames } from '@coinbase/onchainkit/identity';

const addresses = [
'******************************************',
'******************************************'
];
const { data: names, isLoading } = useNames({ addresses });

````

```ts return value
{
  data: ['paulcramer.eth', 'vitalik.eth'],
  isLoading: false
}
````

</CodeGroup>

Get Basenames from multiple addresses:

<CodeGroup>
```tsx code
import { useNames } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const addresses = [
'******************************************',
'******************************************'
];
const { data: names, isLoading } = useNames({ addresses, chain: base });

````

```ts return value
{
  data: ['paul.base.eth', 'coinbase.base.eth'],
  isLoading: false
}
````

</CodeGroup>

## Returns

```ts
useQuery<Promise<GetNameReturnType[]>>

type GetNameReturnType = string | Basename | null;
```

## Parameters

### UseNamesParams

```ts
type UseNamesParams = {
  /** Array of addresses to resolve ENS or Basenames for */
  addresses: Address[];
  /** Optional chain for domain resolution */
  chain?: Chain;
};
```

### UseQueryOptions

```ts
type UseQueryOptions<TData = unknown> = Omit<
  TanstackUseQueryOptions<TData>,
  'queryKey' | 'queryFn'
>;
```
