---
title: buildDepositToMorphoTx
---

Builds `Call[]` for depositing assets to Morpho. Pass the calls to `<Transaction />` to execute the deposit.

## Usage

```tsx code
import { buildDepositToMorphoTx } from '@coinbase/onchainkit/earn';

const calls = await buildDepositToMorphoTx({
  vaultAddress: '0x...', // Morpho vault address on Base
  tokenAddress: '0x...', // Address of the token to deposit
  amount: 1000000000000000000n, // Amount of tokens to deposit
  recipientAddress: '0x...', // Address of the recipient
});
```

## Returns

```typescript
Call[]

type Call = {
  to: Hex;
  data?: Hex;
  value?: bigint;
};

type Hex = `0x${string}`;
```

## Parameters

```typescript
type DepositToMorphoParams = {
  /** The address of the Morpho vault */
  vaultAddress: Address;
  /** The address of the token to deposit */
  tokenAddress: Address;
  /** The amount of tokens to deposit */
  amount: bigint;
  /** The address which can withdraw the deposited tokens */
  recipientAddress: Address;
};

type Address = `0x${string}`;
```
