---
title: buildWithdrawFromMorphoTx
---

The `buildWithdrawFromMorphoTx` function is used to build `Call[]` for withdrawing an asset from Morpho. These calls can be passed the `<Transaction />` component to send a transaction.

## Usage

```tsx code
import { buildWithdrawFromMorphoTx } from '@coinbase/onchainkit/earn';

const calls = await buildWithdrawFromMorphoTx({
  vaultAddress: '0x...', // Morpho vault address on Base
  amount: 1000000000000000000n, // Amount of tokens to withdraw
  recipientAddress: '0x...', // Address of the recipient
});
```

## Returns

```typescript
Call[]

type Call = {
  to: Hex;
  data?: Hex;
  value?: bigint;
};

type Hex = `0x${string}`;
```

## Parameters

```typescript
type WithdrawFromMorphoParams = {
  /** The address of the Morpho vault */
  vaultAddress: Address;
  /** The amount of tokens to withdraw */
  amount: bigint;
  /** The address to which the withdrawn funds will be sent */
  recipientAddress: Address;
};

type Address = `0x${string}`;
```
