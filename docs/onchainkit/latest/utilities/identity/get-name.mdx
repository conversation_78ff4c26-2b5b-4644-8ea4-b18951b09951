---
title: getName
---

The `getName` utility is designed to retrieve a name from an onchain identity
provider for a specific address.

Consider the utility instead of the hook when you
use it with Next.js or any Node.js backend.

## Usage

Get ENS name from an address:

<CodeGroup>
```tsx code
import { getName } from '@coinbase/onchainkit/identity';

const address = '******************************************';
const name = await getName({ address });

````

```ts return value
zizzamia.eth;
````

</CodeGroup>

Get Basename from an address:

<CodeGroup>
```tsx code
import { getName } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const address = '******************************************';
const name = await getName({ address, chain: base });

````

```ts return value
zizzamia.base.eth;
````

</CodeGroup>

Get a sliced address when address does not have an ENS name:

<CodeGroup>
```tsx code
import { getName } from '@coinbase/onchainkit/identity';

const address = '******************************************';
const name = await getName({ address });

````

```ts return value
0x123...5678
````

</CodeGroup>

## Returns

```typescript
Promise<GetNameReturnType>

type GetNameReturnType = string | Basename | null;

type Basename = BaseMainnetName | BaseSepoliaName;
type BaseMainnetName = `${string}.base.eth`;
type BaseSepoliaName = `${string}.basetest.eth`;
```

## Parameters

```typescript
type GetNameParams = {
  /** Ethereum address to resolve */
  address?: Address;
  /** Optional chain for domain resolution */
  chain?: Chain;
};

type Address = `0x${string}`;

type Chain = {
  id: number;
  name: string;
  // ... other chain properties
};
```
