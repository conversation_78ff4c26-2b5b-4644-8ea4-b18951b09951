---
title: getAddress
---

The `getAddress` utility is designed to retrieve an address from an onchain identity provider for a given name.

## Usage

Get ENS Name from mainnet chain

<CodeGroup>
```tsx code
import { getAddress } from '@coinbase/onchainkit/identity';

const address = await getAddress({ name: 'zizzamia.eth' });

````

```ts return value
******************************************
````

</CodeGroup>

Get Basename from base chain

<CodeGroup>
```tsx code
import { getAddress } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

const address = await getAddress({ name: 'zizzamia.base.eth', chain: base });

````

```ts return value
******************************************
````

</CodeGroup>

## Returns

```typescript
type GetAddressReturnType = Address | null;

type Address = `0x${string}`;
```

## Parameters

```typescript
type GetAddressParams = {
  /** Name to resolve */
  name: string | Basename;
  /** Optional chain for domain resolution */
  chain?: Chain;
};

type Basename = BaseMainnetName | BaseSepoliaName;
type BaseMainnetName = `${string}.base.eth`;
type BaseSepoliaName = `${string}.basetest.eth`;

type Chain = {
  id: number;
  name: string;
  // ... other chain properties
};
```
