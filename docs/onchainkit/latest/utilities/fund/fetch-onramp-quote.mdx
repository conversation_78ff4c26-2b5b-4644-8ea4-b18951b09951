---
title: fetchOnrampQuote
---

The `fetchOnrampQuote` utility provides a quote based on the asset the user would like to purchase, plus the network, fiat payment, payment currency, payment method, and country. This is useful for getting pricing information and fees before initiating a transaction.

## Usage

<CodeGroup>
```tsx code
import { fetchOnrampQuote } from '@coinbase/onchainkit/fund';

const quote = await fetchOnrampQuote({
purchaseCurrency: 'ETH',
purchaseNetwork: 'ethereum',
paymentCurrency: 'USD',
paymentMethod: 'CARD',
paymentAmount: '100.00',
country: 'US',
subdivision: 'CA', // Required for US residents
apiKey: 'your-api-key', // Required when using without OnchainKitProvider or in non-React environment
});

````

```json return value
{
  "paymentTotal": {
    "value": "105.00",
    "currency": "USD"
  },
  "paymentSubtotal": {
    "value": "100.00",
    "currency": "USD"
  },
  "purchaseAmount": {
    "value": "0.05",
    "currency": "ETH"
  },
  "coinbaseFee": {
    "value": "3.00",
    "currency": "USD"
  },
  "networkFee": {
    "value": "2.00",
    "currency": "USD"
  },
  "quoteId": "quote_123"
}
````

</CodeGroup>

## Parameters

```typescript
{
  /**
   * ID of the crypto asset the user wants to purchase.
   * Retrieved from the options API.
   */
  purchaseCurrency: string;

  /**
   * Name of the network that the purchase currency should be purchased on.
   * Retrieved from the options API. If omitted, the default network for
   * the crypto currency is used.
   */
  purchaseNetwork?: string;

  /**
   * Fiat currency of the payment amount, e.g., USD.
   */
  paymentCurrency: string;

  /**
   * ID of payment method used to complete the purchase.
   * Retrieved from the options API.
   */
  paymentMethod: string;

  /**
   * Fiat amount the user wants to spend to purchase the crypto currency,
   * inclusive of fees with two decimals of precision, e.g., "100.00".
   */
  paymentAmount: string;

  /**
   * ISO 3166-1 two-digit country code string representing the purchasing
   * user's country of residence, e.g., US.
   */
  country: string;

  /**
   * ISO 3166-2 two-digit country subdivision code. Required if country="US"
   * because certain states (e.g., NY) have state specific asset restrictions.
   */
  subdivision?: string;

   /**
   * Optional API key for Coinbase Onramp. If not provided, the API key from
   * OnchainKitProvider will be used. Required when using the utility without
   * OnchainKitProvider or in a non-React environment.
   */
  apiKey?: string;
}
```

## Returns

`Promise<OnrampQuoteResponseData>` - Returns a promise that resolves to the quote data containing payment amounts, fees, and quote ID.

```typescript
type OnrampQuoteResponseData = {
  /**
   * Object with amount and currency of the total fiat payment required to complete the purchase, inclusive of any fees.
   * The currency will match the `paymentCurrency` in the request if it is supported, otherwise it falls back to USD.
   */
  paymentTotal: OnrampAmount;
  /**
   * Object with amount and currency of the fiat cost of the crypto asset to be purchased, exclusive of any fees.
   * The currency will match the `paymentCurrency`.
   */
  paymentSubtotal: OnrampAmount;
  /**
   * Object with amount and currency of the crypto that to be purchased.
   * The currency will match the `purchaseCurrency` in the request.
   * The number of decimals will be based on the crypto asset.
   */
  purchaseAmount: OnrampAmount;
  /**
   * Object with amount and currency of the fee changed by the Coinbase exchange to complete the transaction.
   * The currency will match the `paymentCurrency`.
   */
  coinbaseFee: OnrampAmount;
  /**
   * Object with amount and currency of the network fee required to send the purchased crypto to the user's wallet.
   * The currency will match the `paymentCurrency`.
   */
  networkFee: OnrampAmount;
  /**
   * Reference to the quote that should be passed into the initialization parameters when launching the Coinbase Onramp widget via the SDK or URL generator.
   */
  quoteId: string;
};

type OnrampAmount = {
  value: string;
  currency: string;
};
```
