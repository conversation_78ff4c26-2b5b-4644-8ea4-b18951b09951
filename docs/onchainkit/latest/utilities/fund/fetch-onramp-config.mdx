---
title: fetchOnrampConfig
---

The `fetchOnrampConfig` utility fetches the list of countries supported by Coinbase Onramp and their available payment methods. This is useful when you need to validate user eligibility or display available payment options.

## Usage

<CodeGroup>
```tsx code
import { fetchOnrampConfig } from '@coinbase/onchainkit/fund';

// When using with OnchainKitProvider
const config = await fetchOnrampConfig();

// When using without OnchainKitProvider or in non-React environment
const config2 = await fetchOnrampConfig('your-api-key');

````

```js return value
{
  countries: [
    {
      id: "US",
      subdivisions: ["CA", "NY", "TX"],
      paymentMethods: [
        {
          id: "CARD",
          name: "Credit or debit card",
          description: "Pay with Visa or Mastercard",
          currencies: ["USD", "EUR"],
          minAmount: { value: "10.00", currency: "USD" },
          maxAmount: { value: "1000.00", currency: "USD" }
        }
      ]
    }
  ]
}
````

</CodeGroup>

## Parameters

```typescript
/**
 * Optional API key for Coinbase Onramp. If not provided, the API key from
 * OnchainKitProvider will be used. Required when using the utility without
 * OnchainKitProvider or in a non-React environment.
 */
apiKey?: string;
```

## Returns

`Promise<OnrampConfigResponseData>` - Returns a promise that resolves to the configuration data containing supported countries and their payment methods.

```typescript
type OnrampConfigResponseData = {
  countries: OnrampConfigCountry[];
};

type OnrampConfigCountry = {
  id: string;
  subdivisions: string[];
  paymentMethods: OnrampPaymentMethod[];
};

type OnrampPaymentMethod = {
  id: string;
};
```
