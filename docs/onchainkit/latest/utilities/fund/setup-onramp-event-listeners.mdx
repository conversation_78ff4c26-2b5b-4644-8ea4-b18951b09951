---
title: setupOnrampEventListeners
---

The `setupOnrampEventListeners` utility sets up event listeners for the Coinbase Onramp widget. It helps you handle various events like successful purchases, exits, and other user interactions.

## Usage

<CodeGroup>
```tsx code
// @errors: 2305
import { setupOnrampEventListeners } from '@coinbase/onchainkit/fund';
import type { SuccessEventData, OnrampError, EventMetadata } from '@coinbase/onchainkit/fund';
const unsubscribe = setupOnrampEventListeners({
  onSuccess: (data: SuccessEventData) => {
    console.log('Purchase successful:', data);
  },
  onExit: (error: OnrampError) => {
    if (error) {
      console.error('Exit with error:', error);
    }
  },
  onEvent: (event: EventMetadata) => {
    console.log('Event received:', event);
  },
});

// Clean up when done
unsubscribe();

````

```ts return value
// Returns an unsubscribe function
() => void
````

</CodeGroup>

## Parameters

```typescript
{
  host?: string;                                   // Optional custom host URL
  onSuccess?: (data?: SuccessEventData) => void;  // Success callback
  onExit?: (error?: OnrampError) => void;         // Exit callback
  onEvent?: (event: EventMetadata) => void;       // General event callback
}
```

## Returns

`() => void` - Returns an unsubscribe function that removes the event listeners when called.

## Event Types

```typescript
type SuccessEventData = {
  assetImageUrl: string;
  assetName: string;
  assetSymbol: string;
  chainId: string;
};

type OnrampError = {
  errorType:
    | 'internal_error'
    | 'handled_error'
    | 'network_error'
    | 'unknown_error';
  code?: string;
  debugMessage?: string;
};

type EventMetadata =
  | OpenEvent
  | TransitionViewEvent
  | PublicErrorEvent
  | ExitEvent
  | SuccessEvent
  | RequestOpenUrlEvent;

type OpenEvent = {
  eventName: 'open';
  widgetName: string;
};

type TransitionViewEvent = {
  eventName: 'transition_view';
  pageRoute: string;
};

type PublicErrorEvent = {
  eventName: 'error';
  error: OnrampError;
};

type ExitEvent = {
  eventName: 'exit';
  error?: OnrampError;
};

type SuccessEvent = {
  eventName: 'success';
  data?: SuccessEventData;
};

type RequestOpenUrlEvent = {
  eventName: 'request_open_url';
  url: string;
};
```
