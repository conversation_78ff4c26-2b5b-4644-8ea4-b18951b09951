---
title: formatAmount
sidebarTitle: formatAmount
description: Utility for consistent number formatting
---


The `formatAmount` utility is designed for consistent number formatting.

## Usage

<CodeGroup>
```tsx code
import { formatAmount } from '@coinbase/onchainkit/token';

const amount = formatAmount('10000', { minimumFractionDigits: 2 });
```

```ts return value
'10,000.00'; // if in U.S. English locale

'10.000,00'; // if in EU country locale
```
</CodeGroup>

## Returns

`string` - The formatted amount.

## Parameters

### FormatAmountOptions

```ts
type FormatAmountOptions = {
  locale?: string; // User locale (default: browser locale)
  minimumFractionDigits?: number; // Minimum fraction digits for number decimals
  maximumFractionDigits?: number; // Maximum fraction digits for number decimals
};
```
