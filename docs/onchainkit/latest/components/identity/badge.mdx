---
title: "Badge"
---

Use `Badge` component along with [`Avatar`](/onchainkit/identity/avatar) or [`Name`](/onchainkit/identity/name) components to display user attestations attached to their account

## Usage

Badge with default colors:

<CodeGroup>
```tsx tsx
import { Badge } from '@coinbase/onchainkit/identity';
<Badge className="badge" /> // [!code focus]
```

```css css
.badge {
  background: #4F46E5;
  path {
    fill: #F9FAFB;
  }
}
```
</CodeGroup>

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-badge--default-identity&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Badge className="badge" />
</App> */}

Badge with custom colors:

```tsx
import { Badge } from '@coinbase/onchainkit/identity';
<Badge className="bg-blue-400 border-white"/> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-badge--custom-color&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>

## Badge with Tooltip

You can enable a tooltip for the attestation badge to provide context about what the badge represents:

```tsx
import { Badge } from '@coinbase/onchainkit/identity';

<Badge 
  tooltip={true} // [!code focus]
  className="badge"
/>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-badge--tooltip&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Badge tooltip={true} className="badge" />
</App> */}

With custom tooltip text:

```tsx
import { Badge } from '@coinbase/onchainkit/identity';

<Badge 
  tooltip="Coinbase Verified Account" // [!code focus]
  className="badge"
/>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-badge--custom-tooltip&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Badge tooltip="Coinbase Verified Account" className="badge" />
</App> */}

## Props

[`BadgeReact`](/onchainkit/identity/types#badgereact)

| Prop | Type | Description |
|------|------|-------------|
| `className` | `string` | Optional className override for top span element |
| `tooltip` | `boolean \| string` | Controls whether the badge shows a tooltip on hover. When `true`, displays the attestation name. When a string is provided, shows that custom text instead. Defaults to `false` |

## Badge on `<Name />` and `<Avatar />`

Badge with [`<Name />`](/onchainkit/identity/name), best used when [`<Name />`](/onchainkit/identity/name) are displayed alongside [`<Avatar />`](/onchainkit/identity/avatar) components.

In both examples we use the Coinbase [Verified Account](https://base.easscan.org/schema/view/0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9) schema ID to show the Coinbase verified badge on the Name and Avatar components.

```tsx
import { base } from 'viem/chains';
import { Badge, Name, Identity } from '@coinbase/onchainkit/identity';

const address = '******************************************';
const COINBASE_VERIFIED_ACCOUNT_SCHEMA_ID =
  '0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9';

<Identity
  className="bg-transparent"
  schemaId={COINBASE_VERIFIED_ACCOUNT_SCHEMA_ID}
  address={address}
>
  <Name address={address}>
    <Badge tooltip={true} /> {/* With tooltip that shows attestation name */}
  </Name>
</Identity>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-badge--badge-on-name&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Identity
    className="bg-transparent"
    schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
  >
    <Name address="******************************************">
      <Badge tooltip={true} />
    </Name>
  </Identity>
</App> */}

Badge with [`<Avatar />`](/onchainkit/identity/avatar), best used when [`<Avatar />`](/onchainkit/identity/avatar) is not paired with any labels.

```tsx
import { base } from 'viem/chains';
import { Avatar, Badge, Identity } from '@coinbase/onchainkit/identity';

const address = '******************************************';
const COINBASE_VERIFIED_ACCOUNT_SCHEMA_ID =
  '0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9';

<Identity
  className="bg-transparent"
  schemaId={COINBASE_VERIFIED_ACCOUNT_SCHEMA_ID}
>
  <Avatar address={address}>
    <Badge tooltip="Verified by Coinbase" /> {/* With custom tooltip text */}
  </Avatar>
</Identity>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-badge--badge-on-avatar&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Identity schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9" className="bg-transparent">
    <Avatar address="******************************************">
      <Badge tooltip="Verified by Coinbase" />
    </Avatar>
  </Identity>
</App> */}

