---
title: "Address"
---

The `Address` component is used to render a shortened user account address.

## Usage

Sliced account address:

```tsx
import { Address } from '@coinbase/onchainkit/identity';
<Address address="******************************************" /> // [!code focus]
```
<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-address--default-identity&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Address address="******************************************" />
</App> */}

### Display full address

Set `isSliced` to false, to display the full address:

```tsx
import { Address } from '@coinbase/onchainkit/identity';
<Address address="******************************************" isSliced={false} /> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-address--full-address&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Address address="******************************************" isSliced={false}/>
</App> */}

### Override styles

You can override component styles using `className`.

```tsx
import { Address } from '@coinbase/onchainkit/identity';
<Address
  className="bg-emerald-400 px-2 py-1 rounded" // [!code focus]
  address="******************************************"
/>
```
<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-address--override-styles&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Address className="bg-emerald-400 px-2 py-1 rounded" address="******************************************"/>
</App> */}

## Props

[`AddressProps`](#addressprops)

```ts
type AddressProps = {
  /** The Ethereum address to render. */
  address?: Address | null;
  /** Optional className override for top span element. */
  className?: string;
  /** Determines if the displayed address should be sliced. (defaults: true) */
  isSliced?: boolean;
  /** Defaults to true. Optional boolean to disable copy address on click functionality. */
  hasCopyAddressOnClick?: boolean;
};
```

