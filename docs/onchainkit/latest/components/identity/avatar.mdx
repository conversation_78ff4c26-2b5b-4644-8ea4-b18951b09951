---
title: "Avatar"
---

The `Avatar` component is used to display ENS or [Basenames](https://www.base.org/names) avatar associated with Ethereum addresses.
When an avatar is not available, it defaults to blue color avatar.

## Usage

Address with an ENS avatar:
```tsx
import { Avatar } from '@coinbase/onchainkit/identity';
<Avatar address="******************************************" /> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-avatar--default-identity&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Avatar address="******************************************" />
</App> */}

Address without an ENS or Basenames avatar defaults to a plain avatar:

```tsx
import { Avatar } from '@coinbase/onchainkit/identity';
<Avatar address="******************************************" /> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-avatar--full-address&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Avatar address="******************************************" />
</App> */}

Address with a Basename avatar:

```tsx
import { Avatar } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains'; // [!code focus]

<Avatar address="0x4bEf0221d6F7Dd0C969fe46a4e9b339a84F52FDF" chain={base} /> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-avatar--full-address&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Avatar address="0x4bEf0221d6F7Dd0C969fe46a4e9b339a84F52FDF" chain={base}/>
</App> */}

Override styles via `className` prop:

```tsx
import { Avatar } from '@coinbase/onchainkit/identity';
<Avatar // [!code focus]
  className="bg-white rounded-full" // [!code focus]
  address="******************************************"
/> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-avatar--override-styles&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>

{/* <App>
  <Avatar className="bg-white rounded-full" address="******************************************" />
</App> */}

Use `defaultComponent` prop to change the default avatar when ENS avatar is not found.
Use `loadingComponent` prop to change the loading placeholder:
```tsx
import { Avatar } from '@coinbase/onchainkit/identity';
<Avatar
  address="******************************************"
  loadingComponent={(
    <div className="h-8 w-8">
      <svg width="100%" height="100%" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <polygon points="6,1 14,1 19,6 19,14 14,19 6,19 1,14 1,6" fill="yellow" stroke="yellow" stroke-width="1"/>
      </svg>
    </div>
  )}
  defaultComponent={(
    <div className="h-8 w-8">
      <svg width="100%" height="100%" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <polygon points="6,1 14,1 19,6 19,14 14,19 6,19 1,14 1,6" fill="green" stroke="green" stroke-width="1"/>
      </svg>
    </div>
  )}
/>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-avatar--default-loading-components&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Avatar
    address="******************************************"
    loadingComponent={(
      <div className="h-8 w-8">
        <svg width="100%" height="100%" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <polygon points="6,1 14,1 19,6 19,14 14,19 6,19 1,14 1,6" fill="yellow" stroke="yellow" stroke-width="1"/>
        </svg>
      </div>
    )}
    defaultComponent={(
      <div className="h-8 w-8">
        <svg width="100%" height="100%" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <polygon points="6,1 14,1 19,6 19,14 14,19 6,19 1,14 1,6" fill="green" stroke="green" stroke-width="1"/>
        </svg>
      </div>
    )}
  />
</App> */}

Show attestation on ENV avatar with [`Badge`](/onchainkit/identity/badge) component.
Use [`OnchainKitProvider`](/onchainkit/config/onchainkit-provider) or [`Identity`](/onchainkit/identity/identity) component with the `schemaId` prop.
```tsx
import { Avatar, Badge, Identity } from '@coinbase/onchainkit/identity';
<Identity
  schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
  address="******************************************"
>
  <Avatar >
    <Badge />
  </Avatar>
</Identity>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-identity-avatar--with-badge&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
{/* <App>
  <Identity
    schemaId="0xf8b05c79f090979bf4a80270aba232dff11a10d9ca55c4f88de95317970f0de9"
    address="******************************************"
    className="bg-transparent"
  >
    <Avatar >
      <Badge />
    </Avatar>
  </Identity>
</App> */}

## Props
[`AvatarReact`](/onchainkit/identity/types#AvatarReact)

