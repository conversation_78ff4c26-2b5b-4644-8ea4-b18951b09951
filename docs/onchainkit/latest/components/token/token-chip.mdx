---
title: TokenChip
sidebarTitle: TokenChip
description: Button component that displays token symbol
---

The `TokenChip` component is a button that displays the token symbol.

## Usage

```tsx
import { TokenChip } from '@coinbase/onchainkit/token';
import '@coinbase/onchainkit/styles.css';

const token = {
  address: '0x1234',
  chainId: 1,
  decimals: 18,
  image:
    'https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png',
  name: 'Ethereum',
  symbol: 'ETH',
};

<TokenChip token={token} />; // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-chip--default&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>

## Props

### TokenChipProps

```ts
type TokenChipProps = {
  token: Token; // Rendered token
  onClick?: (token: Token) => void;
  className?: string;
  isPressable?: boolean;
};
```
