---
title: TokenSelectDropdown
sidebarTitle: TokenSelectDropdown
description: Dropdown component for token selection
---

The `TokenSelectDropdown` component is a dropdown component that selects a token in a given list of tokens.

## Usage

```tsx
import { TokenSelectDropdown, Token } from '@coinbase/onchainkit/token';
const token = {} as Token;
const setToken = (token: Token) => {};
// ---cut-before---
<TokenSelectDropdown // [!code focus]
  token={token} // [!code focus]
  setToken={setToken} // [!code focus]
  options={[ // [!code focus]
    {
      name: 'Ethereum',
      address: '',
      symbol: 'ETH',
      decimals: 18,
      image: 'https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png',
      chainId: 8453,
    },
    {
      name: 'USDC',
      address: '******************************************',
      symbol: 'USDC',
      decimals: 6,
      image:
        'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2',
      chainId: 8453,
    },
    {
      name: 'Dai',
      address: '******************************************',
      symbol: 'DAI',
      decimals: 18,
      image:
        'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/d0/d7/d0d7784975771dbbac9a22c8c0c12928cc6f658cbcf2bbbf7c909f0fa2426dec-NmU4ZWViMDItOTQyYy00Yjk5LTkzODUtNGJlZmJiMTUxOTgy',
      chainId: 8453,
    },
  ]} // [!code focus]
/> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-selectordropdown--default&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>

## Props

### TokenSelectDropdownProps

```ts
type TokenSelectDropdownProps = {
  options: Token[]; // List of tokens
  setToken: (token: Token) => void; // Token setter
  token?: Token; // Selected token
};
```

## CSS

```css
.ock-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #ffffff;
}
```
