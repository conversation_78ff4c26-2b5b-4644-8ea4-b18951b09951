---
title: TokenSearch
sidebarTitle: TokenSearch
description: Search component with debounce delay (deprecated)
---

The `<TokenSearch />` is a search component with an optional debounce delay.

If you want to handle debounce delay outside of this component, set `delayMs` to `0`.

## Usage

Use [`getTokens`](/onchainkit/api/get-tokens) and `<TokenSearch />` combined to search the [`Token`](/onchainkit/token/types#token).

```tsx
import { useCallback } from 'react';
import { base } from 'viem/chains';
// ---cut-before---
import { OnchainKitProvider } from '@coinbase/onchainkit';
import { getTokens } from '@coinbase/onchainkit/api'; // [!code focus]
import { TokenSearch } from '@coinbase/onchainkit/token'; // [!code focus]
import type { Token } from '@coinbase/onchainkit/token'; // [!code focus]

...

// example of async onChange handler
const handleChange = useCallback((value: string) => {
  async function getData(value) {
    const tokens: Token[] = await getTokens({ search: value }); // [!code focus]
    // Do something with tokens here
  }
  getData(value);
}, []);

...

<OnchainKitProvider
  chain={base}
  apiKey="YOUR_API_KEY"
>
  <TokenSearch onChange={handleChange} delayMs={200} />
</OnchainKitProvider>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-token-search--default&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>

## Props

```ts
type TokenSearchProps = {
  className?: string;
  delayMs?: number; // Debounce delay in milliseconds
  onChange: (value: string) => void; // Search callback function
};
```
