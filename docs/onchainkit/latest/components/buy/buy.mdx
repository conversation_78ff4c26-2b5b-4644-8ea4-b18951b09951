---
title: "Buy"
---

import { Danger } from "/snippets/danger.mdx";

<Frame>
<img alt="Buy"
  src="/images/onchainkit/buy.gif"
  height="364"/>
</Frame>

The `Buy` components provide a comprehensive interface for users to purchase [Tokens](/onchainkit/token/types#token).

The `Buy` component supports token swaps from USDC and ETH by default with the option to provide an additional token of choice using the `fromToken` prop. In addition, users are able to purchase tokens using their Coinbase account, Apple Pay, or debit card.

<Note>
The Apple Pay and Debit Card onramp options are only available for Coinbase supported assets.  
</Note>

<Note>
This component requires a `projectId` to be set in the `OnchainKitProvider`. You can find your `projectId` on [Coinbase Developer Platform](https://portal.cdp.coinbase.com/products/onchainkit).
</Note>


## Usage

Example using `@coinbase/onchainkit/buy`.

```tsx
import { Buy } from '@coinbase/onchainkit/buy'; // [!code focus]
import type { Token } from '@coinbase/onchainkit/token';

export default function BuyComponents() { // [!code focus]
  const degenToken: Token = {
    name: '<PERSON><PERSON><PERSON>',
    address: '******************************************',
    symbol: 'DEGEN',
    decimals: 18,
    image:
    'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/3b/bf/3bbf118b5e6dc2f9e7fc607a6e7526647b4ba8f0bea87125f971446d57b296d2-MDNmNjY0MmEtNGFiZi00N2I0LWIwMTItMDUyMzg2ZDZhMWNm',
    chainId: 8453,
  };

  return ( // [!code focus]
    <Buy toToken={degenToken} /> // [!code focus]
  ) // [!code focus]
} // [!code focus]

```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-buy--disabled&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>

{/* <App>
  <BuyWrapper>
    {({ address, toToken }) => {
      return (
        <Buy toToken={toToken} disabled />
      )
    }}
  </BuyWrapper>
</App> */}

<Danger>
**Note: This interface is for demonstration purposes only.**

Swap and Onramp flows will execute and work out of the box when you implement the component in your own app.
</Danger>


### Sponsor gas with Paymaster

To sponsor swap transactions for your users, toggle the Paymaster using the `isSponsored` prop.

By default, this will use the [Coinbase Developer Platform](https://portal.cdp.coinbase.com/products/bundler-and-paymaster) Paymaster.

You can configure sponsorship settings on the [Paymaster](https://portal.cdp.coinbase.com/products/bundler-and-paymaster) page.
For security reasons, we recommend setting up a contract allowlist in the Portal. Without a contract allowlist defined, your Paymaster will only be able to sponsor up to $1.

The contract used in our Swap API is Uniswap's [Universal Router](https://basescan.org/address/******************************************), which is deployed on Base at `******************************************`.

Note that gas sponsorship will only work for Smart Wallets.

```tsx
import { Buy } from '@coinbase/onchainkit/buy'; // [!code focus]
import type { Token } from '@coinbase/onchainkit/token';

export default function BuyComponents() { // [!code focus]
  const degenToken: Token = {
    name: 'DEGEN',
    address: '******************************************',
    symbol: 'DEGEN',
    decimals: 18,
    image:
    'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/3b/bf/3bbf118b5e6dc2f9e7fc607a6e7526647b4ba8f0bea87125f971446d57b296d2-MDNmNjY0MmEtNGFiZi00N2I0LWIwMTItMDUyMzg2ZDZhMWNm',
    chainId: 8453,
  };

  return ( // [!code focus]
    <Buy toToken={degenToken} isSponsored /> // [!code focus]
  ) // [!code focus]
} // [!code focus]
```

## Props

[`BuyProps`](#buyprops)

```ts
type BuyProps = {
  /** Optional className override for top div element. */
  className?: string;
  config?: {
    /** Maximum acceptable slippage for a swap (e.g., 3 for 3%). This is a percentage, not basis points. */
    maxSlippage: number;
  };
  /** Disables Buy button */
  disabled?: boolean;
  experimental?: {
    /** Whether to use a DEX aggregator. (default: false) */
    useAggregator: boolean;
  };
  /** An optional setting to sponsor swaps with a Paymaster. (default: false) */
  isSponsored?: boolean;
  /** An optional callback function that handles errors within the provider. */
  onError?: (error: SwapError) => void;
  /** An optional callback function that exposes the component lifecycle state */
  onStatus?: (lifecycleStatus: LifecycleStatus) => void;
  /** An optional callback function that exposes the transaction receipt */
  onSuccess?: (transactionReceipt?: TransactionReceipt) => void;
  /** An optional token to swap from */
  fromToken?: Token;
  /** The token to swap to */
  toToken: Token;
};
```

