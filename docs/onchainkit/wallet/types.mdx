---
title: Wallet components & utilities types
sidebarTitle: Wallet
description: Glossary of Types in Wallet components & utilities.
---

## `ConnectWalletReact`

```ts
type ConnectWalletReact = {
  children?: React.ReactNode; // Children can be utilized to display customized content when the wallet is connected.
  className?: string; // Optional className override for button element
  text?: string; // Optional text override for button. Note: Prefer using `disconnectedLabel` prop instead as this will be deprecated in a future version.
  disconnectedLabel?: React.ReactNode; // Optional text override for button.
  onConnect?: () => void; // Optional callback function that is called when the wallet is connected. Can be used to trigger SIWE prompts or other actions.
};
```

## `IsValidAAEntrypointOptions`

```ts
export type IsValidAAEntrypointOptions = {
  entrypoint: string;
};
```

## `IsWalletACoinbaseSmartWalletOptions`

```ts
export type IsWalletACoinbaseSmartWalletOptions = {
  client: PublicClient;
  userOp: UserOperation<'v0.6'>;
};
```

## `IsWalletACoinbaseSmartWalletResponse`

```ts
export type IsWalletACoinbaseSmartWalletResponse =
  | { isCoinbaseSmartWallet: true }
  | { isCoinbaseSmartWallet: false; error: string; code: string };
```

## `WalletContextType`

```ts
type WalletContextType = {
  address?: Address | null; // The Ethereum address to fetch the avatar and name for.
  chain?: Chain; // Optional chain for domain resolution
  isConnectModalOpen: boolean;
  setIsConnectModalOpen: Dispatch<SetStateAction<boolean>>;
  isSubComponentOpen: boolean;
  setIsSubComponentOpen: Dispatch<SetStateAction<boolean>>;
  isSubComponentClosing: boolean;
  setIsSubComponentClosing: Dispatch<SetStateAction<boolean>>;
  handleClose: () => void;
  connectRef: React.RefObject<HTMLDivElement>;
  showSubComponentAbove: boolean;
  alignSubComponentRight: boolean;
  activeFeature: WalletAdvancedFeature | null;
  setActiveFeature: Dispatch<SetStateAction<WalletAdvancedFeature | null>>;
  isActiveFeatureClosing: boolean;
  setIsActiveFeatureClosing: Dispatch<SetStateAction<boolean>>;
  tokenBalances: PortfolioTokenWithFiatValue[] | undefined;
  portfolioFiatValue: number | undefined;
  isFetchingPortfolioData: boolean;
  portfolioDataUpdatedAt: number | undefined;
  refetchPortfolioData: () => Promise<QueryObserverResult<Portfolio, Error>>;
  animations: {
    container: string;
    content: string;
  };
};
```

## `WalletReact`

```ts
type WalletReact = {
  children?: React.ReactNode;
  className?: string;
} & (
  | { draggable?: true; draggableStartingPosition?: { x: number; y: number } }
  | { draggable?: false; draggableStartingPosition?: never }
); // discriminated union to allow for optional draggable and draggableStartingPosition
```

## `WalletDropdownBasenameReact`

```ts
type WalletDropdownBasenameReact = {
  className?: string; // Optional className override for the element
};
```

## `WalletDropdownReact`

```ts
type WalletDropdownReact = {
  children?: React.ReactNode;
  className?: string; // Optional className override for top div element;
  classNames?: {
    container?: string;
    qr?: WalletAdvancedQrReceiveProps['classNames'];
    swap?: WalletAdvancedSwapProps['classNames'];
  };
  swappableTokens?: Token[];
};
```

## `WalletDropdownDisconnectReact`

```ts
export type WalletDropdownDisconnectReact = {
  className?: string; // Optional className override for the element
  text?: string; // Optional text override for the button
};
```

## `WalletDropdownFundLinkReact`

```ts
export type WalletDropdownFundLinkReact = {
  className?: string; // Optional className override for the element
  icon?: ReactNode; // Optional icon override
  openIn?: 'popup' | 'tab'; // Whether to open the funding flow in a tab or a popup window
  popupSize?: 'sm' | 'md' | 'lg'; // Size of the popup window if `openIn` is set to `popup`
  rel?: string; // Specifies the relationship between the current document and the linked document
  target?: string; // Where to open the target if `openIn` is set to tab
  text?: string; // Optional text override
};
```

## `WalletDropdownLinkReact`

```ts
export type WalletDropdownLinkReact = {
  children: string;
  className?: string; // Optional className override for the element
  href: string;
  icon?: 'wallet' & ReactNode;
  rel?: string;
  target?: string;
};
```

## `WalletAdvancedReact`

```ts
export type WalletAdvancedReact = {
  children?: React.ReactNode;
  swappableTokens?: Token[];
};
```

## `WalletAdvancedContextType`

```ts
export type WalletAdvancedContextType = {
  showSwap: boolean;
  setShowSwap: Dispatch<SetStateAction<boolean>>;
  isSwapClosing: boolean;
  setIsSwapClosing: Dispatch<SetStateAction<boolean>>;
  showQr: boolean;
  setShowQr: Dispatch<SetStateAction<boolean>>;
  isQrClosing: boolean;
  setIsQrClosing: Dispatch<SetStateAction<boolean>>;
  tokenBalances: PortfolioTokenWithFiatValue[] | undefined;
  portfolioFiatValue: number | undefined;
  isFetchingPortfolioData: boolean;
  portfolioDataUpdatedAt: number | undefined;
  refetchPortfolioData: () => Promise<
    QueryObserverResult<PortfolioTokenBalances, Error>
  >;
  animations: {
    container: string;
    content: string;
  };
};
```
