---
title: <WalletIsland /> · OnchainKit
sidebarTitle: <WalletIsland />
description: The `<WalletIsland />` component provides an advanced Wallet interface for users.
---

<Frame>
<img alt="WalletIsland"
  src="/images/onchainkit/wallet-island.gif"
  height="364"/>
</Frame>

`WalletIsland` is our flagship implementation of the new advanced Wallet
components, providing an advanced Wallet interface for users, including:

- a QR code for receiving funds
- a link to buy crypto with fiat
- a swap interface
- the user's token portfolio
- a button that moves anywhere on the screen

Designed for desktop experiences, `WalletIsland` component gives
users a seamless way to interact with their wallet and manage their assets.

Before using these components, ensure you've completed all [Getting Started steps](/onchainkit/getting-started).

<Note>
This component requires a `projectId` and an `apiKey`to be set in the `OnchainKitProvider`. You
can find your both of these values on [Coinbase Developer Platform](https://portal.cdp.coinbase.com/products/onchainkit).
</Note>


<Note>
Please note: token balances are only available for Base mainnet and Ethereum mainnet.
You can control the network in the `OnchainKitProvider` by setting the `chain` prop.
</Note>


## Quick start

Advanced `Wallet` has two default implementations:

- `WalletAdvancedDefault`
- `WalletIsland`

`WalletAdvancedDefault` provides the user with an advanced wallet
interface, anchored to the screen like our standard `Wallet` component.
`WalletIsland` provides the same powerful interface, while enabling
the user to drag the component around the window.

If you'd like more customization, follow the implementation guide below.

```tsx
import { WalletAdvancedDefault } from '@coinbase/onchainkit/wallet';
import { WalletIsland } from '@coinbase/onchainkit/wallet';
<>
  <WalletAdvancedDefault />
  <WalletIsland />
</>
```

You will see two Wallet buttons below. Right below is `WalletAdvancedDefault`.
This version will stay anchored to its current position.

`WalletIsland` is in the bottom-right corner of the window. Feel free to drag
it around.

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-wallet-island--main&viewMode=story&dark=true&hero=true"
  width="100%"
  height="800"
/>
{/* <WalletComponents>
  <WalletAdvancedDefault />
  <WalletIsland />
</WalletComponents> */}

While this behavior is more noticeable with the `WalletIsland` component, you'll
notice that for both components, the `WalletAdvanced` container will appear in
different positions depending on the component's location in the window:

- If there is sufficient space to the right, it will be left-aligned.
- If there is not enough space to the right, it will be right-aligned.
- If there is enough space below, it will open below.
- If there is not enough space below, it will open above.

<Note>
If you are attempting to use this component and the Buy button is not working,
make sure you have a `projectId` configured correctly in your providers.

If transaction history is not loading, make sure you have an `apiKey` configured
correctly in your providers.
</Note>


## Configuring `WalletIsland` and advanced `Wallet`

Experience the magic by simply dropping in `<Wallet />` and advanced `Wallet`
components, and watch your component seamlessly come to life.

As with [`WalletDefault`](/onchainkit/wallet/wallet) , `WalletAdvancedDefault` leverages
several [`<Identity>`](/onchainkit/identity/identity) components like [`<Avatar>`](/onchainkit/identity/avatar),
[`<Name>`](/onchainkit/identity/name), and [`<Address>`](/onchainkit/identity/address).

We've introduces several advanced wallet components, including
`<WalletAdvancedAddressDetails>`, `<WalletAdvancedTokenHoldings>`, and
`<WalletAdvancedTransactionActions>`.

```tsx
import {
  ConnectWallet,
  Wallet,
  WalletDropdown,
  WalletAdvancedAddressDetails,
  WalletAdvancedTokenHoldings,
  WalletAdvancedTransactionActions,
  WalletAdvancedWalletActions,
} from '@coinbase/onchainkit/wallet';
import { Address, Avatar, Name, Identity } from '@coinbase/onchainkit/identity';
import { color } from '@coinbase/onchainkit/theme';

export function YourWalletAdvanced() {
  return (
    <div className="flex justify-end">
      <Wallet>
        <ConnectWallet>
          <Avatar className="h-6 w-6" />
          <Name />
        </ConnectWallet>
        <WalletDropdown>
          <WalletAdvancedWalletActions />
          <WalletAdvancedAddressDetails />
          <WalletAdvancedTransactionActions />
          <WalletAdvancedTokenHoldings />
        </WalletDropdown>
      </Wallet>
    </div>
  );
}
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-wallet-island--wallet-and-advanced&viewMode=story&dark=true&hero=true"
  width="100%"
  height="800"
/>
{/* <WalletComponents>
  <Wallet>
    <ConnectWallet disconnectedLabel="Connect Wallet">
      <Avatar className="h-6 w-6" />
      <Name />
    </ConnectWallet>
    <WalletDropdown>
      <WalletAdvancedWalletActions />
      <WalletAdvancedAddressDetails />
      <WalletAdvancedTransactionActions />
      <WalletAdvancedTokenHoldings />
    </WalletDropdown>
  </Wallet>
</WalletComponents> */}

When customizing your advanced `Wallet` implementation, use the `draggable` prop on
`Wallet` to enable draggability. `draggable` defaults to `false`, but
when `draggable` is set to `true`, you can also set a `draggableStartingPosition` prop to
specify the initial position of your `WalletIsland`.

```tsx
import {
  ConnectWallet,
  Wallet,
  WalletDropdown,
  WalletAdvancedAddressDetails,
  WalletAdvancedTokenHoldings,
  WalletAdvancedTransactionActions,
  WalletAdvancedWalletActions,
} from '@coinbase/onchainkit/wallet';
import { Address, Avatar, Name, Identity } from '@coinbase/onchainkit/identity';
import { color } from '@coinbase/onchainkit/theme';

export function DraggableWalletAdvanced() {
  return (
    <div className="flex justify-end">
      <Wallet
        draggable={true} // [!code focus]
        draggableStartingPosition={{ // [!code focus]
          x: window.innerWidth - 300, // [!code focus]
          y: window.innerHeight - 100, // [!code focus]
        }}
      >
        <ConnectWallet>
          <Avatar className="h-6 w-6" />
          <Name />
        </ConnectWallet>
        <WalletDropdown>
          <WalletAdvancedWalletActions />
          <WalletAdvancedAddressDetails />
          <WalletAdvancedTransactionActions />
          <WalletAdvancedTokenHoldings />
        </WalletDropdown>
      </Wallet>
    </div>
  );
}
```

## Customize Connect button text and style

Each OnchainKit component offers the flexibility to customize `className`
and adjust the style of the React components it represents.

Explore the options for customizing the Connect button text and style [here](/onchainkit/wallet/wallet#customize-connect-button-text-and-style).

## Using Wallet Modal

<Frame>
<img
  alt="Wallet Modal"
  src="/images/onchainkit/wallet-modal.png"
  height="364"
  width="364"
  className="mx-400"
/>
</Frame>

Wallet modal offers users multiple wallet connection options. Explore these options
[here](/onchainkit/wallet/wallet-modal).

## Example usage

### Usage with Sign In With Ethereum (SIWE)

To use [Sign In With Ethereum (SIWE)](https://docs.login.xyz/general-information/siwe-overview) with OnchainKit,
check out our [SIWE example](/onchainkit/wallet/wallet#usage-with-sign-in-with-ethereum-siwe).

## Components

The components are designed to work together hierarchically. For each component, ensure the following:

- `<Wallet />` - Serves as the main container for all wallet-related components.
- `<WalletDropdown />` - Contains additional wallet information and options. Place inside the `<Wallet />` component.
- `<WalletAdvancedWalletActions />` - Provides wallet actions like View Transaction History, view QR Code, and Disconnect wallet. Place inside the `<WalletDropdown />` component.
- `<WalletAdvancedAddressDetails />` - Displays user address, avatar, and portfolio balance in fiat. Place inside the `<WalletDropdown />` component.
- `<WalletAdvancedTransactionActions />` - Buttons for buying crypto with fiat, transferring crypto, and swapping. Place inside the `<WalletDropdown />` component.
- `<WalletAdvancedTokenHoldings />` - Displays token balances and their value in fiat. Place inside the `<WalletDropdown />` component.
- `<ConnectWallet />` - Handles the wallet connection process. Place child components inside to customize the connect button appearance.
- `<AppWithWalletModal />` - Enables a wallet aggregation experience.
- `<Identity />` - Displays user identity information. Place inside `<WalletDropdown />` for a complete profile view.
- `<WalletDropdownBasename />` - Displays the user's Basename within the dropdown.
- `<WalletDropdownLink />` - Creates a custom link within the dropdown. Use the `icon` prop to add an icon, and `href` to specify the destination.
- `<WalletDropdownDisconnect />` - Provides a disconnect option within the dropdown.

Additional components for customizing the wallet interface include:

- `<Avatar />` - Displays the user's avatar image.
- `<Name />` - Shows the user's name or ENS.
- `<Badge />` - Can be used to display additional user status or information.
- `<Address />` - Shows the user's wallet address.
- `<EthBalance />` - Displays the user's ETH balance.

The Wallet component automatically handles the wallet connection state and updates the UI accordingly.
You need to wrap your application or relevant part of it with these components
to provide a complete wallet interaction experience.

## Component types

- [`WalletReact`](/onchainkit/wallet/types#walletreact)
- [`ConnectWalletReact`](/onchainkit/wallet/types#connectwalletreact)
- [`WalletDropdownReact`](/onchainkit/wallet/types#walletdropdownreact)
- [`WalletDropdownBasenameReact`](/onchainkit/wallet/types#walletdropdownbasenamereact)
- [`WalletDropdownDisconnectReact`](/onchainkit/wallet/types#walletdropdowndisconnectreact)
- [`WalletDropdownLinkReact`](/onchainkit/wallet/types#walletdropdownlinkreact)

