---
title: <Swap /> · OnchainKit
sidebarTitle: <Swap />
description: Swap components & utilities
---

import { Danger } from "/snippets/danger.mdx";

The `Swap` components provide a comprehensive interface for users to execute [Token](/onchainkit/token/types#token) swaps.

Before using them, ensure you've completed all [Getting Started steps](/onchainkit/getting-started).

## Quick start

The `SwapDefault` component is a simplified version of the `Swap` component, designed to streamline the integration process for developers. Instead of manually defining each subcomponent and prop, developers can use this shorthand version which renders our suggested implementation of the component and includes `SwapAmountInput`, `SwapSettings`, `SwapToggleButton`, `SwapButton`, and `SwapToast`.

If you'd like more customization, follow the implementation guide for our `Swap` component below.

```tsx
import { SwapDefault } from '@coinbase/onchainkit/swap'; // [!code focus]
import type { Token } from '@coinbase/onchainkit/token';

const eth: Token = {
  name: 'ETH',
  address: '',
  symbol: 'ETH',
  decimals: 18,
  image:
    'https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png',
  chainId: 8453,
};

const usdc: Token = {
  name: 'USDC',
  address: '******************************************',
  symbol: 'USDC',
  decimals: 6,
  image:
    'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2',
  chainId: 8453,
};

<SwapDefault // [!code focus]
  from={[eth]} // [!code focus]
  to={[usdc]} // [!code focus]
/> // [!code focus]
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-swap--default&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <SwapDefault from={swappableTokens} to={swappableTokens.slice().reverse()} disabled />
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Props

[`SwapDefaultReact`](/onchainkit/swap/types#swapdefaultreact)

## Usage

Example using `@coinbase/onchainkit/swap` and `@coinbase/onchainkit/wallet`.

```tsx
import { Avatar, Name } from '@coinbase/onchainkit/identity';
import { // [!code focus]
  Swap, // [!code focus]
  SwapAmountInput, // [!code focus]
  SwapToggleButton, // [!code focus]
  SwapButton, // [!code focus]
  SwapMessage, // [!code focus]
  SwapToast, // [!code focus]
} from '@coinbase/onchainkit/swap'; // [!code focus]
import { Wallet, ConnectWallet } from '@coinbase/onchainkit/wallet';
import { useAccount } from 'wagmi';
import type { Token } from '@coinbase/onchainkit/token';

export default function SwapComponents() {
  const { address } = useAccount();

  const ETHToken: Token = {
    address: "",
    chainId: 8453,
    decimals: 18,
    name: "Ethereum",
    symbol: "ETH",
    image: "https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png",
  };

  const USDCToken: Token = {
    address: "******************************************",
    chainId: 8453,
    decimals: 6,
    name: "USDC",
    symbol: "USDC",
    image: "https://dynamic-assets.coinbase.com/3c15df5e2ac7d4abbe9499ed9335041f00c620f28e8de2f93474a9f432058742cdf4674bd43f309e69778a26969372310135be97eb183d91c492154176d455b8/asset_icons/9d67b728b6c8f457717154b3a35f9ddc702eae7e76c4684ee39302c4d7fd0bb8.png",
  };

  // add other tokens here to display them as options in the swap
  const swappableTokens: Token[] = [ETHToken, USDCToken];


  return address ? (
    <Swap> // [!code focus]
      <SwapAmountInput // [!code focus]
        label="Sell" // [!code focus]
        swappableTokens={swappableTokens} // [!code focus]
        token={ETHToken} // [!code focus]
        type="from" // [!code focus]
      /> // [!code focus]
      <SwapToggleButton /> // [!code focus]
      <SwapAmountInput // [!code focus]
        label="Buy" // [!code focus]
        swappableTokens={swappableTokens} // [!code focus]
        token={USDCToken} // [!code focus]
        type="to" // [!code focus]
      /> // [!code focus]
      <SwapButton /> // [!code focus]
      <SwapMessage /> // [!code focus]
      <SwapToast /> // [!code focus]
    </Swap> // [!code focus]
  ) : (
    <Wallet>
      <ConnectWallet>
        <Avatar className="h-6 w-6" />
        <Name />
      </ConnectWallet>
    </Wallet>
  );
}
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-swap--full-swap&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapAmountInput
              label="Sell"
              swappableTokens={swappableTokens}
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              swappableTokens={swappableTokens}
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
            <SwapToast />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

<Danger>
**Note: This interface is for demonstration purposes only.**

The swap will execute and work out of the box when you implement the component in your own app.
</Danger>


### Supported Swap Routers

The `Swap` component supports two swap routers:

- [Uniswap V3](https://app.uniswap.org/) (default)
- [0x Aggregator](https://0x.org/)

To use the 0x Aggregator, set the `experimental.useAggregator` prop to `true`.

### Sponsor gas with Paymaster

To sponsor swap transactions for your users, toggle the Paymaster using the `isSponsored` prop.

By default, this will use the [Coinbase Developer Platform](https://portal.cdp.coinbase.com/products/bundler-and-paymaster) Paymaster.

You can configure sponsorship settings on the [Paymaster](https://portal.cdp.coinbase.com/products/bundler-and-paymaster) page.
For security reasons, we recommend setting up a contract allowlist in the Portal. Without a contract allowlist defined, your Paymaster will only be able to sponsor up to $1.

The contract used in our Swap API is Uniswap's [Universal Router](https://basescan.org/address/******************************************), which is deployed on Base at `******************************************`.

Note that gas sponsorship will only work for Smart Wallets.

```tsx
import { Avatar, Name } from '@coinbase/onchainkit/identity';
import {
  Swap,
  SwapAmountInput,
  SwapToggleButton,
  SwapButton,
  SwapMessage,
} from '@coinbase/onchainkit/swap';
import { Wallet, ConnectWallet } from '@coinbase/onchainkit/wallet';
import { useAccount } from 'wagmi';
import type { Token } from '@coinbase/onchainkit/token';

export default function SwapComponents() {
  const { address } = useAccount();

  const ETHToken: Token = {
    address: "",
    chainId: 8453,
    decimals: 18,
    name: "Ethereum",
    symbol: "ETH",
    image: "https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png",
  };

  const USDCToken: Token = {
    address: "******************************************",
    chainId: 8453,
    decimals: 6,
    name: "USDC",
    symbol: "USDC",
    image: "https://dynamic-assets.coinbase.com/3c15df5e2ac7d4abbe9499ed9335041f00c620f28e8de2f93474a9f432058742cdf4674bd43f309e69778a26969372310135be97eb183d91c492154176d455b8/asset_icons/9d67b728b6c8f457717154b3a35f9ddc702eae7e76c4684ee39302c4d7fd0bb8.png",
  };

return (
// ---cut-before---
// omitted for brevity

// Set isSponsored to true // [!code focus]
<Swap isSponsored > // [!code focus]
  ...
</Swap> // [!code focus]
// ---cut-after---
);
}
```

### Custom token pair

You can adjust to only allow swap between a token pair.

```tsx
import { Avatar, Name } from '@coinbase/onchainkit/identity';
import {
  Swap,
  SwapAmountInput,
  SwapToggleButton,
  SwapButton,
  SwapMessage,
  SwapToast,
} from '@coinbase/onchainkit/swap';
import { Wallet, ConnectWallet } from '@coinbase/onchainkit/wallet';
import { useAccount } from 'wagmi';
import type { Token } from '@coinbase/onchainkit/token';

export default function SwapComponents() {
  const { address } = useAccount();

  const ETHToken: Token = {
    address: "",
    chainId: 8453,
    decimals: 18,
    name: "Ethereum",
    symbol: "ETH",
    image: "https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png",
  };

  const USDCToken: Token = {
    address: "******************************************",
    chainId: 8453,
    decimals: 6,
    name: "USDC",
    symbol: "USDC",
    image: "https://dynamic-assets.coinbase.com/3c15df5e2ac7d4abbe9499ed9335041f00c620f28e8de2f93474a9f432058742cdf4674bd43f309e69778a26969372310135be97eb183d91c492154176d455b8/asset_icons/9d67b728b6c8f457717154b3a35f9ddc702eae7e76c4684ee39302c4d7fd0bb8.png",
  };

return (
// ---cut-before---
// omitted for brevity

<Swap> // [!code focus]
  <SwapAmountInput // [!code focus]
    label="Sell" // [!code focus]
    token={ETHToken} // [!code focus]
    type="from" // [!code focus]
  /> // [!code focus]
  <SwapToggleButton /> // [!code focus]
  <SwapAmountInput // [!code focus]
    label="Buy" // [!code focus]
    token={USDCToken} // [!code focus]
    type="to" // [!code focus]
  /> // [!code focus]
  <SwapButton /> // [!code focus]
  <SwapMessage /> // [!code focus]
  <SwapToast /> // [!code focus]
</Swap> // [!code focus]
// ---cut-after---
);
}
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-swap--without-swappable-prop&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapAmountInput
              label="Sell"
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
            <SwapToast />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Remove toggle button

You can remove `SwapToggleButton` to make swap unidirectional.

```tsx
import { Avatar, Name } from '@coinbase/onchainkit/identity';
import {
  Swap,
  SwapAmountInput,
  SwapToggleButton,
  SwapButton,
  SwapMessage,
  SwapToast,
} from '@coinbase/onchainkit/swap';
import { Wallet, ConnectWallet } from '@coinbase/onchainkit/wallet';
import { useAccount } from 'wagmi';
import type { Token } from '@coinbase/onchainkit/token';

export default function SwapComponents() {
  const { address } = useAccount();

  const ETHToken: Token = {
    address: "",
    chainId: 8453,
    decimals: 18,
    name: "Ethereum",
    symbol: "ETH",
    image: "https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png",
  };

  const USDCToken: Token = {
    address: "******************************************",
    chainId: 8453,
    decimals: 6,
    name: "USDC",
    symbol: "USDC",
    image: "https://dynamic-assets.coinbase.com/3c15df5e2ac7d4abbe9499ed9335041f00c620f28e8de2f93474a9f432058742cdf4674bd43f309e69778a26969372310135be97eb183d91c492154176d455b8/asset_icons/9d67b728b6c8f457717154b3a35f9ddc702eae7e76c4684ee39302c4d7fd0bb8.png",
  };

return (
// ---cut-before---
// omitted for brevity

<Swap> // [!code focus]
  <SwapAmountInput // [!code focus]
    label="Sell" // [!code focus]
    token={ETHToken} // [!code focus]
    type="from" // [!code focus]
  /> // [!code focus]
  <SwapAmountInput // [!code focus]
    label="Buy" // [!code focus]
    token={USDCToken} // [!code focus]
    type="to" // [!code focus]
  /> // [!code focus]
  <SwapButton /> // [!code focus]
  <SwapMessage /> // [!code focus]
  <SwapToast /> // [!code focus]
</Swap> // [!code focus]
// ---cut-after---
);
}
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-swap--no-toggle&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapAmountInput
              label="Sell"
              token={swappableTokens[1]}
              type="from"
            />
            <SwapAmountInput
              label="Buy"
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
            <SwapToast />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Remove swap message

You can remove `SwapMessage` component.

```tsx
import { Avatar, Name } from '@coinbase/onchainkit/identity';
import {
  Swap,
  SwapAmountInput,
  SwapToggleButton,
  SwapButton,
  SwapMessage,
  SwapToast,
} from '@coinbase/onchainkit/swap';
import { Wallet, ConnectWallet } from '@coinbase/onchainkit/wallet';
import { useAccount } from 'wagmi';
import type { Token } from '@coinbase/onchainkit/token';

export default function SwapComponents() {
  const { address } = useAccount();

  const ETHToken: Token = {
    address: "",
    chainId: 8453,
    decimals: 18,
    name: "Ethereum",
    symbol: "ETH",
    image: "https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png",
  };

  const USDCToken: Token = {
    address: "******************************************",
    chainId: 8453,
    decimals: 6,
    name: "USDC",
    symbol: "USDC",
    image: "https://dynamic-assets.coinbase.com/3c15df5e2ac7d4abbe9499ed9335041f00c620f28e8de2f93474a9f432058742cdf4674bd43f309e69778a26969372310135be97eb183d91c492154176d455b8/asset_icons/9d67b728b6c8f457717154b3a35f9ddc702eae7e76c4684ee39302c4d7fd0bb8.png",
  };

return (
// ---cut-before---
// omitted for brevity

<Swap> // [!code focus]
  <SwapAmountInput // [!code focus]
    label="Sell" // [!code focus]
    token={ETHToken} // [!code focus]
    type="from" // [!code focus]
  /> // [!code focus]
  <SwapToggleButton />
  <SwapAmountInput // [!code focus]
    label="Buy" // [!code focus]
    token={USDCToken} // [!code focus]
    type="to" // [!code focus]
  /> // [!code focus]
  <SwapButton /> // [!code focus]
  <SwapToast /> // [!code focus]
</Swap> // [!code focus]
// ---cut-after---
);
}
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-swap--no-message&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapAmountInput
              label="Sell"
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapToast />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Override styles

You can override component styles using `className`.

```tsx
import { Avatar, Name } from '@coinbase/onchainkit/identity';
import {
  Swap,
  SwapAmountInput,
  SwapToggleButton,
  SwapButton,
  SwapMessage,
  SwapToast,
} from '@coinbase/onchainkit/swap';
import { Wallet, ConnectWallet } from '@coinbase/onchainkit/wallet';
import { useAccount } from 'wagmi';
import type { Token } from '@coinbase/onchainkit/token';

export default function SwapComponents() {
  const { address } = useAccount();

  const ETHToken: Token = {
    address: "",
    chainId: 8453,
    decimals: 18,
    name: "Ethereum",
    symbol: "ETH",
    image: "https://dynamic-assets.coinbase.com/dbb4b4983bde81309ddab83eb598358eb44375b930b94687ebe38bc22e52c3b2125258ffb8477a5ef22e33d6bd72e32a506c391caa13af64c00e46613c3e5806/asset_icons/4113b082d21cc5fab17fc8f2d19fb996165bcce635e6900f7fc2d57c4ef33ae9.png",
  };

  const USDCToken: Token = {
    address: "******************************************",
    chainId: 8453,
    decimals: 6,
    name: "USDC",
    symbol: "USDC",
    image: "https://dynamic-assets.coinbase.com/3c15df5e2ac7d4abbe9499ed9335041f00c620f28e8de2f93474a9f432058742cdf4674bd43f309e69778a26969372310135be97eb183d91c492154176d455b8/asset_icons/9d67b728b6c8f457717154b3a35f9ddc702eae7e76c4684ee39302c4d7fd0bb8.png",
  };

return (
// ---cut-before---
// omitted for brevity

<Swap>
  <SwapAmountInput
    label="Sell"
    token={ETHToken}
    type="from"
  />
  <SwapToggleButton className='border-[#EA580C]'/> // [!code focus]
  <SwapAmountInput
    label="Buy"
    token={USDCToken}
    type="to"
  />
  <SwapButton className='bg-[#EA580C]'/> // [!code focus]
  <SwapMessage />
  <SwapToast />
</Swap>
// ---cut-after---
);
}
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-swap--styled&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapAmountInput
              label="Sell"
              swappableTokens={swappableTokens}
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton className='border-[#EA580C]' />
            <SwapAmountInput
              label="Buy"
              swappableTokens={swappableTokens}
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton className='bg-[#EA580C]' disabled />
            <SwapMessage/>
            <SwapToast />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

## Components

The components are designed to work together hierarchically. For each component, ensure the following:

- `<Swap />` - Set the user's address and error handling.
- `<SwapAmountInput />` - Set the [Token](/onchainkit/token/types#token) to swap and specify the input type (`from` or `to`).
- `<SwapToggleButton />` - Optional component to toggle between input types.
- `<SwapMessage />` - Optional component that displays a message related to the swap operation's current state.
- `<SwapButton />` - Set the onSuccess and onError callbacks.
- `<SwapToast />` - Optional component to notify user of successful swap transaction.

## Props

- [`SwapReact`](/onchainkit/swap/types#swapreact)
- [`SwapDefaultReact`](/onchainkit/swap/types#swapdefaultreact)
- [`SwapAmountInputReact`](/onchainkit/swap/types#swapamountinputreact)
- [`SwapButtonReact`](/onchainkit/swap/types#swapbuttonreact)
- [`SwapMessageReact`](/onchainkit/swap/types#swapmessagereact)
- [`SwapToggleButtonReact`](/onchainkit/swap/types#swaptogglebuttonreact)
- [`SwapToastReact`](/onchainkit/swap/types#swaptoastreactt)

