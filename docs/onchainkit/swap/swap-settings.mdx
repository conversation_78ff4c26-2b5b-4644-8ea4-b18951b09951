---
title: <SwapSettings /> · OnchainKit
sidebarTitle: <SwapSettings />
description: Customizable components for token Swap settings and slippage configuration
---

The `SwapSettings` component enables customizable slippage configuration in the `Swap` component.

## Usage

```tsx
// omitted for brevity
import {
  Swap,
  SwapSettings,
  SwapSettingsSlippageDescription,
  SwapSettingsSlippageInput,
  SwapSettingsSlippageTitle,
} from '@coinbase/onchainkit/swap';

<Swap>
  <SwapSettings>
    <SwapSettingsSlippageTitle>
      Max. slippage
    </SwapSettingsSlippageTitle>
    <SwapSettingsSlippageDescription>
      Your swap will revert if the prices change by more than the selected
      percentage.
    </SwapSettingsSlippageDescription>
    <SwapSettingsSlippageInput />
  </SwapSettings>
</Swap>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-settings--default&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapSettings>
             <SwapSettingsSlippageTitle>Max. slippage</SwapSettingsSlippageTitle>
             <SwapSettingsSlippageDescription>
              Your swap will revert if the prices change by more than the selected
              percentage.
             </SwapSettingsSlippageDescription>
             <SwapSettingsSlippageInput />
            </SwapSettings>
            <SwapAmountInput
              label="Sell"
              swappableTokens={swappableTokens}
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              swappableTokens={swappableTokens}
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Override styles

You can override component styles using `className`.

```tsx
import {
  Swap,
  SwapSettings,
  SwapSettingsSlippageDescription,
  SwapSettingsSlippageInput,
  SwapSettingsSlippageTitle,
} from '@coinbase/onchainkit/swap';
// ---cut-before---
// omitted for brevity
<Swap>
  <SwapSettings>
    <SwapSettingsSlippageTitle className="text-[#EA580C]">
      Max. slippage
    </SwapSettingsSlippageTitle>
    <SwapSettingsSlippageDescription className="text-[#EA580C]">
      Your swap will revert if the prices change by more than the selected
      percentage.
    </SwapSettingsSlippageDescription>
    <SwapSettingsSlippageInput/>
  </SwapSettings>
</Swap>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-settings--styled-title-and-description&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapSettings>
             <SwapSettingsSlippageTitle className="text-[#EA580C]">Max. slippage</SwapSettingsSlippageTitle>
             <SwapSettingsSlippageDescription className="text-[#EA580C]">
              Your swap will revert if the prices change by more than the selected
              percentage.
             </SwapSettingsSlippageDescription>
             <SwapSettingsSlippageInput/>
            </SwapSettings>
            <SwapAmountInput
              label="Sell"
              swappableTokens={swappableTokens}
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              swappableTokens={swappableTokens}
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Override icon

You can override the icon using the icon prop.

```tsx
// suppress twoslash error
declare const baseIcon: any;

import {
  Swap,
  SwapSettings,
  SwapSettingsSlippageDescription,
  SwapSettingsSlippageInput,
  SwapSettingsSlippageTitle,
} from '@coinbase/onchainkit/swap';
// ---cut-before---
// omitted for brevity
<SwapSettings icon={baseIcon}>
// ---cut-after---
</SwapSettings>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-settings--with-icon&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapSettings icon={walletDropdownLinkCustomBaseIconSvg}>
             <SwapSettingsSlippageTitle>Max. slippage</SwapSettingsSlippageTitle>
             <SwapSettingsSlippageDescription>
              Your swap will revert if the prices change by more than the selected
              percentage.
             </SwapSettingsSlippageDescription>
             <SwapSettingsSlippageInput/>
            </SwapSettings>
            <SwapAmountInput
              label="Sell"
              swappableTokens={swappableTokens}
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              swappableTokens={swappableTokens}
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Add text

You can add text next to the `SwapSettings` component using text.

```tsx
import {
  Swap,
  SwapSettings,
  SwapSettingsSlippageDescription,
  SwapSettingsSlippageInput,
  SwapSettingsSlippageTitle,
} from '@coinbase/onchainkit/swap';
// ---cut-before---
// omitted for brevity
<SwapSettings text="Settings">
// ---cut-after---
</SwapSettings>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-settings--with-text-prop&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapSettings text="Settings">
             <SwapSettingsSlippageTitle>Max. slippage</SwapSettingsSlippageTitle>
             <SwapSettingsSlippageDescription>
              Your swap will revert if the prices change by more than the selected
              percentage.
             </SwapSettingsSlippageDescription>
             <SwapSettingsSlippageInput/>
            </SwapSettings>
            <SwapAmountInput
              label="Sell"
              swappableTokens={swappableTokens}
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              swappableTokens={swappableTokens}
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

### Override text

You can override component text by providing children text.

```tsx
import {
  Swap,
  SwapSettings,
  SwapSettingsSlippageDescription,
  SwapSettingsSlippageInput,
  SwapSettingsSlippageTitle,
} from '@coinbase/onchainkit/swap';
<Swap>
// ---cut-before---
// omitted for brevity

<SwapSettingsSlippageTitle>
  Slippage Settings
</SwapSettingsSlippageTitle>
<SwapSettingsSlippageDescription>
  Set a max slippage you are willing to accept.
</SwapSettingsSlippageDescription>
// ---cut-after---
</Swap>
```

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=onchainkit-swap-settings--custom-title-and-description&viewMode=story&dark=true&hero=true"
  width="100%"
  height="520px"
/>
{/* <App>
  <SwapWrapper>
    {({ address, swappableTokens }) => {
      if (address) {
        return (
          <Swap>
            <SwapSettings>
             <SwapSettingsSlippageTitle>Slippage Settings</SwapSettingsSlippageTitle>
             <SwapSettingsSlippageDescription>
              Set a max slippage you are willing to accept.
             </SwapSettingsSlippageDescription>
             <SwapSettingsSlippageInput/>
            </SwapSettings>
            <SwapAmountInput
              label="Sell"
              swappableTokens={swappableTokens}
              token={swappableTokens[1]}
              type="from"
            />
            <SwapToggleButton />
            <SwapAmountInput
              label="Buy"
              swappableTokens={swappableTokens}
              token={swappableTokens[2]}
              type="to"
            />
            <SwapButton disabled />
            <SwapMessage />
          </Swap>
        )
      }
      return <>
        <Wallet>
          <ConnectWallet>
            <Avatar className="h-6 w-6" />
            <Name />
          </ConnectWallet>
        </Wallet>
      </>;
    }}
  </SwapWrapper>
</App> */}

## Components

- `<SwapSettings />` - Container component for swap slippage settings.
- `<SwapSettingsSlippageDescription />` - Displays description text explaining the slippage setting.
- `<SwapSettingsSlippageInput />` - Input field for users to enter their desired max slippage percentage
- `<SwapSettingsSlippageTitle />` - Displays the title for the slippage settings section

## Props

- [`SwapSettingsReact`](/onchainkit/swap/types#swapsettingsreact)
- [`SwapSettingsSlippageDescriptionReact`](/onchainkit/swap/types#swapsettingsslippagedescriptionreact)
- [`SwapSettingsSlippageInputReact`](/onchainkit/swap/types#swapsettingsslippageinputreact)
- [`SwapSettingsSlippageTitleReact`](/onchainkit/swap/types#swapsettingsslippagetitlereact)

