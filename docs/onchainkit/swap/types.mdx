---
title: Swap components & utilities Types
description: Glossary of Types in Swap components & utilities.
sidebarTitle: Swap
---

## `Fee`

```ts
type Fee = {
  amount: string; // The amount of the fee
  baseAsset: Token; // The base asset for the fee
  percentage: string; // The percentage of the fee
};
```

## `QuoteWarning`

```ts
type QuoteWarning = {
  description?: string; // The description of the warning
  message?: string; // The message of the warning
  type?: string; // The type of the warning
};
```

## `LifecycleStatus`

```ts
type LifecycleStatusDataShared = {
  isMissingRequiredField: boolean;
  maxSlippage: number;
};

type LifecycleStatus =
  | {
      statusName: 'init';
      statusData: LifecycleStatusDataShared;
    }
  | {
      statusName: 'error';
      statusData: SwapError & LifecycleStatusDataShared;
    }
  | {
      statusName: 'amountChange';
      statusData: {
        amountFrom: string;
        amountTo: string;
        tokenFrom?: Token;
        tokenTo?: Token;
      } & LifecycleStatusDataShared;
    }
  | {
      statusName: 'slippageChange';
      statusData: LifecycleStatusDataShared;
    }
  | {
      statusName: 'transactionPending';
      statusData: LifecycleStatusDataShared;
    }
  | {
      statusName: 'transactionApproved';
      statusData: {
        transactionHash: Hex;
        transactionType: 'ERC20' | 'Permit2';
      } & LifecycleStatusDataShared;
    }
  | {
      statusName: 'success';
      statusData: {
        transactionReceipt: TransactionReceipt;
      } & LifecycleStatusDataShared;
    };
```

## `SwapAmountInputReact`

```ts
type SwapAmountInputReact = {
  className?: string; // Optional className override for top div element.
  delayMs?: number; // The debounce delay in milliseconds
  label: string; // Descriptive label for the input field
  swappableTokens?: Token[]; // Swappable tokens
  token?: Token; // Selected token
  type: 'to' | 'from'; // Identifies if component is for toToken or fromToken
};
```

## `SwapButtonReact`

```ts
type SwapButtonReact = {
  className?: string; // Optional className override for top div element.
  disabled?: boolean; // Disables swap button
};
```

## `SwapError`

```ts
type SwapError = {
  code: string; // The error code representing the type of swap error.
  error: string; // The error message providing details about the swap error.
  message: string; // The error message providing details about the swap error.
};
```

## `SwapMessageReact`

```ts
type SwapMessageReact = {
  className?: string; // Optional className override for top div element.
};
```

## `SwapQuote`

```ts
type SwapQuote = {
  amountReference: string; // The reference amount for the quote
  from: Token; // The source token for the swap
  fromAmount: string; // The amount of the source token
  fromAmountUSD: string; // The USD value of the source token
  hasHighPriceImpact: boolean; // Whether the price impact is high
  priceImpact: string; // The price impact of the swap
  slippage: string; // The slippage of the swap
  to: Token; // The destination token for the swap
  toAmount: string; // The amount of the destination token
  toAmountUSD: string; // The USD value of the destination token
  warning?: QuoteWarning; // The warning associated with the quote
};
```

## `SwapReact`

```ts
type SwapReact = {
  children: ReactNode;
  className?: string; // Optional className override for top div element.
  config?: {
    maxSlippage: number; // Maximum acceptable slippage for a swap (e.g., 3 for 3%). This is a percentage, not basis points;
  };
  experimental?: {
    /**
     * Whether to use a DEX aggregator.
     * true - 0x Aggregator
     * false - Uniswap V3
     * @default false
     */
    useAggregator: boolean;
  };
  isSponsored?: boolean; // An optional setting to sponsor swaps with a Paymaster. (default: false)
  onError?: (error: SwapError) => void; // An optional callback function that handles errors within the provider.
  onStatus?: (lifecycleStatus: LifecycleStatus) => void; // An optional callback function that exposes the component lifecycle state
  onSuccess?: (transactionReceipt: TransactionReceipt) => void; // An optional callback function that exposes the transaction receipt
  title?: string; // Title for the Swap component. (default: "Swap")
};
```

## `SwapDefaultReact`

```ts
export type SwapDefaultReact = {
  to: Token[]; // Swappable tokens (first token in array will be initial token selected)
  from: Token[]; // Swappable tokens (first token in array will be initial token selected)
  disabled?: boolean; // Disables swap button
} & Omit<SwapReact, 'children'>;
```

## `SwapSettingsReact`

```ts
type SwapSettingsReact = {
  children: ReactNode;
  className?: string; // Optional className override for top div element.
  icon?: ReactNode; // Optional icon override
  text?: string; // Optional text override
};
```

## `SwapSettingsSlippageDescriptionReact`

```ts
type SwapSettingsSlippageDescriptionReact = {
  children: ReactNode;
  className?: string; // Optional className override for top div element.
};
```

## `SwapSettingsSlippageInputReact`

```ts
type SwapSettingsSlippageInputReact = {
  className?: string; // Optional className override for top div element.
};
```

## `SwapSettingsSlippageTitleReact`

```ts
type SwapSettingsSlippageTitleReact = {
  children: ReactNode;
  className?: string; // Optional className override for top div element.
};
```

## `SwapToastReact`

```ts
type SwapToastReact = {
  className?: string; // An optional CSS class name for styling the toast component.
  durationMs?: number; // An optional value to customize time until toast disappears
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right'; // An optional position property to specify the toast's position on the screen.
};
```

## `SwapToggleButtonReact`

```ts
type SwapToggleButtonReact = {
  className?: string; // Optional className override for top div element.
};
```

## `Transaction`

```ts
type Transaction = {
  chainId: number; // The chain ID
  data: Hex; // The data for the transaction
  gas: bigint; // The gas limit
  maxFeePerGas?: bigint | undefined; // The maximum fee per gas
  maxPriorityFeePerGas?: bigint | undefined; // The maximum priority fee per gas
  nonce?: number; // The nonce for the transaction
  to: Address; // The recipient address
  value: bigint; // The value of the transaction
};
```

