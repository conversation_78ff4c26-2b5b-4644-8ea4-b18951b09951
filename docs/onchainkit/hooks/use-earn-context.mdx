---
title: useEarnContext
---


The `useEarnContext` hook is used to access the context values of the `Earn` component.

It can be used to build fully custom deposit and withdraw interfaces and contains all relevant data about a Morpho vault.


## Usage

Note: This hook should be used within a `<Earn />` or `<EarnProvider />` (headless) component.

```tsx code
import { useEarnContext } from '@coinbase/onchainkit/earn';

const { depositAmount, 
        setDepositAmount, 
        apy, 
        nativeApy, 
        rewards, 
        vaultFee, 
        vaultToken, 
        liquidity, 
        deposits 
       // ...  
        } = useEarnContext();

// Use the values to build a custom deposit interface!
```

## Returns

[`EarnContextType`](/onchainkit/earn/types#earncontexttype)

