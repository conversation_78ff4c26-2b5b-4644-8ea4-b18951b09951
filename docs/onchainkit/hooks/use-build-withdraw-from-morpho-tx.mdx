---
title: useBuildWithdrawFromMorphoTx
---


The `useBuildWithdrawFromMorphoTx` hook is used to build a withdraw transaction from Morpho from within a React component.

The hook handles the following:

- Fetching data required to build the transaction
- Using [`buildWithdrawFromMorphoTx`](/onchainkit/api/build-withdraw-from-morpho-tx) to build the transaction
- Returning the transaction calls to be used with the `<Transaction />` component

## Usage

```tsx code
import { useBuildWithdrawFromMorphoTx } from '@coinbase/onchainkit/earn';

const { calls } = useBuildWithdrawFromMorphoTx({
  vaultAddress: '0x...',
  recipientAddress: '0x...',
  amount: '1000000000000000000',
  tokenDecimals: 18,
});
```

## Returns

[`{ calls: Call[] }`](/onchainkit/transaction/types#calls)

## Parameters

[`UseBuildWithdrawFromMorphoTxParams`](/onchainkit/earn/types#usebuildwithdrawfrommorphotxparams)
