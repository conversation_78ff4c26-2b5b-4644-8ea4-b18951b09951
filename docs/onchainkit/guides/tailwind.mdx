---
title: Tailwind CSS Integration Guide · OnchainKit
sidebarTitle: Tailwind CSS Integration Guide
description: Learn how to integrate Tailwind CSS with OnchainKit
---

OnchainKit comes with first class support for `tailwindcss`.

<Steps>
  <Step title="Use default OnchainKit's style">
  You can use the default styles without any customization.
  Just place this at the top of your application's entry point to have the components work out of the box.

  ```javascript
  import '@coinbase/onchainkit/styles.css';
  ```
  </Step>
  <Step title="Tailwind CSS Config">
  Depending on your dark mode setup, you may have to add `safelist: ['dark']` to your Tailwind config.

  ```javascript filename="tailwind.config.js"
  /** @type {import('tailwindcss').Config} */
  export default {
    content: ['./src/**/*.{ts,tsx}'],
    darkMode: ['class'], // [!code focus]
    safelist: ['dark'], // [!code focus]
    theme: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
    plugins: [],
  };
  ```
  </Step>
  <Step title="Toggling light / dark mode">
  There are many ways to handle color mode.

  In OnchainKit, toggling color mode works by adding / removing class name `dark` to the root html tag.
  </Step>
  <Step title="Colorscheme override">
  To override default colorscheme, you need to modify the following css variables:

  ```css
  @tailwind base;

  @layer base {
    :root {
      --ock-font-family: 'your-custom-value';
      --ock-border-radius: 'your-custom-value';
      --ock-border-radius-inner: 'your-custom-value';
      --ock-text-inverse: 'your-custom-value';
      --ock-text-foreground: 'your-custom-value';
      --ock-text-foreground-muted: 'your-custom-value';
      --ock-text-error: 'your-custom-value';
      --ock-text-primary: 'your-custom-value';
      --ock-text-success: 'your-custom-value';
      --ock-text-warning: 'your-custom-value';
      --ock-text-disabled: 'your-custom-value';

      --ock-bg-default: 'your-custom-value';
      --ock-bg-default-hover: 'your-custom-value';
      --ock-bg-default-active: 'your-custom-value';
      --ock-bg-alternate: 'your-custom-value';
      --ock-bg-alternate-hover: 'your-custom-value';
      --ock-bg-alternate-active: 'your-custom-value';
      --ock-bg-inverse: 'your-custom-value';
      --ock-bg-inverse-hover: 'your-custom-value';
      --ock-bg-inverse-active: 'your-custom-value';
      --ock-bg-primary: 'your-custom-value';
      --ock-bg-primary-hover: 'your-custom-value';
      --ock-bg-primary-active: 'your-custom-value';
      --ock-bg-primary-washed: 'your-custom-value';
      --ock-bg-primary-disabled: 'your-custom-value';
      --ock-bg-secondary: 'your-custom-value';
      --ock-bg-secondary-hover: 'your-custom-value';
      --ock-bg-secondary-active: 'your-custom-value';
      --ock-bg-error: 'your-custom-value';
      --ock-bg-warning: 'your-custom-value';
      --ock-bg-success: 'your-custom-value';
      --ock-bg-default-reverse: 'your-custom-value';

      --ock-icon-color-primary: 'your-custom-value';
      --ock-icon-color-foreground: 'your-custom-value';
      --ock-icon-color-foreground-muted: 'your-custom-value';
      --ock-icon-color-inverse: 'your-custom-value';
      --ock-icon-color-error: 'your-custom-value';
      --ock-icon-color-success: 'your-custom-value';
      --ock-icon-color-warning: 'your-custom-value';

      --ock-line-primary: 'your-custom-value';
      --ock-line-default: 'your-custom-value';
      --ock-line-heavy: 'your-custom-value';
      --ock-line-inverse: 'your-custom-value';
    }
  }
  ```
  </Step>
</Steps>

