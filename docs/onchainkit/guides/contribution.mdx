---
title: Contribution Guide · OnchainKit
sidebarTitle: Contribution Guide
description: Learn how to contribute to OnchainKit
---

Welcome to OnchainKit! So you want to contribute to this project? You came to the right place.

In this guide, you will learn how to:

- [Set up this project](#setup)
- [Navigate the codebase](#codebase)
- [Accomplish various workflows](#workflows)
- [Submit a feature request](#feature-request)

## Setup

### Clone the repo

```bash
<NAME_EMAIL>:coinbase/onchainkit.git
```

### Install Node and pnpm

Use nvm, mise, n or your favorite version manager to install Node.js.

For pnpm, see the installation instructions on the [pnpm website](https://pnpm.io/installation).

### Install dependencies

From the root of the repository:

```bash
pnpm install
```

## Codebase

This project is a monorepo managed with pnpm. The `@coinbase/onchainkit` package is located in:

```bash
packages/onchainkit/
```

Here is a rough layout of the codebase:

```bash
packages/onchainkit/
└── src/
   ├── api/                         - API related components and functions
   ├── core/                        - Files with zero dependencies
   ├── styles/                      - Styles
   │   ├── index-with-tailwind.css  - CSS entrypoint
   ├── {Component}/                 - Component folder
   │   ├── components/              - React components
   │   │   ├── {Name}.tsx
   │   │   ├── {Name}.test.tsx
   │   │   └── {Name}.css
   │   ├── core/                    - Utility functions
   │   ├── index.ts                 - Entrypoint for the folder
   │   └── types.ts                 - Export types
   │
   ├── index.ts                     - Main package entry point
   ├── types.ts                     - Core types
   └── OnchainKitProvider.tsx       - OnchainKit provider
```

## Workflows

### Development

To work on OnchainKit components with live UI feedback:

```bash
pnpm f:play dev
```

This will build the OnchainKit package in watch mode, and start a development environment (the playground) where you can see your components in action.

As you make changes, the playground will update automatically.

Navigate to [http://localhost:3000](http://localhost:3000) to open the playground.

### Building

To build the package:

```bash
pnpm f:ock build
```

### Testing

Write and update existing unit tests. You can run tests with:

```bash
pnpm f:ock test
```

For watching file changes and rerunning tests automatically:

```bash
pnpm f:ock test:watch
```

We expect 100% code coverage for any updates. You can get coverage information with:

```bash
pnpm f:ock test:coverage
```

If the coverage drops below 100%, look at the coverage report generated by the above command with:

```bash
open coverage/index.html
```

### Updating changelog

To update the change log, run:

```bash
pnpm changeset
```

Select `minor` and use the following format for the summary:

```markdown
- **feat**: feature update information. By @your-github-id #XX (XX is the PR number)
```

Possible values are:

- `feat`
- `fix`
- `docs`
- `chore`

## Feature request

Have a component in mind that we are not supporting yet? You can submit a feature request to our [Github](https://github.com/coinbase/onchainkit/issues). Create a **"New issue"** and label it "Feature Request: ...".

