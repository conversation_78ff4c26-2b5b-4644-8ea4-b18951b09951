---
title: OnchainKit Bug Reporting Guide
sidebarTitle: Reporting a bug
description: Help us make OnchainKit better
---

We look at all of your bug reports and will do our best to fix them as quickly as possible.

<Steps>
  <Step title="Create a new issue">
  Navigate to [Issues tab](https://github.com/coinbase/onchainkit/issues) on Github and click the "New issue" button.
  </Step>
  <Step title="Select 'Bug Report'">
  Pick the "Bug Report" option and fill out the form to the best of your ability. 
  </Step>
  <Step title="We'll be in touch">
  We'll do our best to respond to your issue on Github as soon as possible.
  </Step>
</Steps>

### Have a special request?
You can tag us on [Discord](https://discord.com/channels/1220414409550336183/1253768005863739565) or DM us on [X](https://x.com/Onchainkit).

We're most active on X and Discord, so if you're able to, please create an issue there.

### Found a security vulnerability?
If you've found a security vulnerability, please report it to our [HackerOne Program](https://hackerone.com/coinbase?type=team). We offer generous rewards for bounties. 


