---
title: OnchainKit top-level types
sidebarTitle: Config
description: Glossary of OnchainKit top-level Types.
---

## `AppConfig`

```ts
type AppConfig = {
  appearance?: {
    name?: string | null; // The name of your application
    logo?: string | null; // The URL of your application logo
    mode?: Mode | null; // Optionally determines color scheme based on OS preference or user selection
    theme?: ComponentTheme | null; // Optionally sets the visual style for components
  };
  paymaster?: string | null; // Paymaster URL for gas sponsorship
};
```

## `isBaseOptions`

```ts
type isBaseOptions = {
  chainId: number;
  isMainnetOnly?: boolean; // If the chainId check is only allowed on mainnet
};
```

## `isEthereumOptions`

```ts
type isEthereumOptions = {
  chainId: number;
  isMainnetOnly?: boolean; // If the chainId check is only allowed on mainnet
};
```

## `OnchainKitConfig`

```ts
type OnchainKitConfig = {
  address: Address | null; // Address is optional as we may not have an address for new users
  apiKey: string | null; // ApiKey for Coinbase Developer Platform APIs
  chain: Chain; // Chain must be provided as we need to know which chain to use
  config?: AppConfig; // Configuration options for the app
  rpcUrl: string | null; // RPC URL for onchain requests. Defaults to using CDP Node if the API Key is set
  schemaId: EASSchemaUid | null; // SchemaId is optional as not all apps need to use EAS
  projectId: string | null; // ProjectId from Coinbase Developer Platform, only required for Coinbase Onramp support
};
```

## `OnchainKitContextType`

```ts
type OnchainKitContextType = OnchainKitConfig;
```

## `OnchainKitProviderReact`

```ts
type OnchainKitProviderReact = {
  address?: Address;
  apiKey?: string;
  chain: Chain;
  children: ReactNode;
  config?: AppConfig;
  rpcUrl?: string;
  schemaId?: EASSchemaUid;
  projectId?: string;
};
```


