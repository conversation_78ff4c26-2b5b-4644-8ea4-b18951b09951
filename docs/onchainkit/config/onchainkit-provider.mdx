---
title: "<OnchainKitProvider />"
---


Provides the OnchainKit React Context to the app.

## Usage

```tsx app.tsx
// @noErrors: 2304 - Cannot find name 'MyComponent'
import { base } from 'viem/chains';
import { OnchainKitProvider } from '@coinbase/onchainkit';

const App = () => {
  return (
    <OnchainKitProvider
      config={{
        appearance: {
          name: 'OnchainKit Playground',
          logo: 'https://onchainkit.xyz/favicon/48x48.png?v4-19-24',
          mode: 'auto',
          theme: 'default',
        },
      }}
      chain={base}
    >
      <MyComponent />
    </OnchainKitProvider>
  );
};
```

## Props

[`OnchainKitProviderReact`](/onchainkit/config/types#onchainkitproviderreact)

| Prop                       | Description                                                                                                                                                                                    | Required |
| -------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| [`chain`](#chain)          | The chain that your OnchainKit project supports.                                                                                                                                               | Yes      |
| [`apiKey`](#apikey)        | Client API Key from Coinbase Developer Platform.                                                                                                                                               | No       |
| [`rpcUrl`](#rpc-url)       | RPC URL for onchain requests.                                                                                                                                                                  | No       |
| [`projectId`](#project-id) | Your Coinbase Developer Platform Project ID.                                                                                                                                                   | No       |
| [`config`](#config)        | - `config.appearance` — Customize your OnchainKit project's appearance <br /> - `config.paymaster` — Paymaster URL for gas sponsorship <br /> - `config.wallet` — Wallet configuration options | No       |
| [`schemaId`](#schema-id)   | _[Deprecation Pending]_ The schema ID for attestations from the Ethereum Attestation Service (EAS).                                                                                            | No       |
| [`address`](#address)      | _[Deprecation Pending]_ This prop is no longer used.                                                                                                                                           | No       |

### Chain

`chain` specifies the chain on which your OnchainKit project will operate.

This prop is required for all OnchainKit components.

We recommend importing chain data from [viem](https://viem.sh/docs/chains/introduction).

### `apiKey`

`apiKey` is your Coinbase Developer Platform Client API Key.

This prop is required for most OnchainKit components, including:

- [`<Checkout>`](/onchainkit/checkout/checkout)
- [`<NFTCard>`](/onchainkit/mint/nft-card)
- [`<NFTMintCard>`](/onchainkit/mint/nft-mint-card)
- [`<Swap>`](/onchainkit/swap/swap)
- [`<Transaction>`](/onchainkit/transaction/transaction)

You can get a [Client API Key](https://portal.cdp.coinbase.com/projects/project-id/api-keys/client-key)
from Coinbase Developer Platform.

<Frame>
<img
  alt="OnchainKit copy Client API Key"
  src="/images/onchainkit/copy-api-key-guide.png"
  height="364"
/>
</Frame>

### RPC URL

`rpcUrl` is required for any onchain requests. If you provide your own RPC URL,
OnchainKit will use it.

If you do not provide your own RPC URL, you must provide an `apiKey`, which
enables OnchainKit to use the
[Coinbase Developer Platform Node](https://portal.cdp.coinbase.com/products/node).

### Project ID

`projectId` is your Coinbase Developer Platform Project ID.

This prop is required for the `<FundButton />` component.

You can obtain a Project ID from the [Coinbase Developer Platform](https://portal.cdp.coinbase.com/projects).

<Frame>
<img
  alt="OnchainKit copy Project ID"
  src="/images/onchainkit/copy-project-id.png"
  height="364"
/>
</Frame>

### Config

`config` is an object that can be used to customize the appearance and behavior
of the OnchainKit components.

This prop has three keys: `appearance`, `paymaster`, and `wallet`.

#### Appearance

`appearance` manages the appearance of the OnchainKit components and has the following properties:

- `name` — The name of your OnchainKit project
- `logo` — The URL of the logo for your OnchainKit project
- `mode` — The mode of the OnchainKit components. Can be `auto`, `dark`, or `light`.
- `theme` — The theme of the OnchainKit components. Can be `base`, `cyberpunk`, `default`, `hacker`, or a custom theme.

Explore appearance options in the [OnchainKit Playground](https://onchainkit.xyz/playground).

#### Paymaster

`paymaster` represents the Paymaster URL that enables you to sponsor gas for your users.

You can configure your Paymaster and obtain your Paymaster URL from the
[Coinbase Developer Platform](https://portal.cdp.coinbase.com/products/bundler-and-paymaster).

#### Wallet

`wallet` configures the wallet connection experience and has the following properties:

- `display` — The display mode for the wallet interface. Can be either:
  - `'modal'` — Shows wallet connection in a modal overlay with wallet aggregation
  - `'classic'` — Shows wallet connection in the traditional inline style
- `termsUrl` — URL to your terms of service
- `privacyUrl` — URL to your privacy policy

### Address _[Deprecation Pending]_

`address` is no longer used and will be removed in a future version of
OnchainKit.

### Schema ID _[Deprecation Pending]_

`schemaId` is no longer used as OnchainKit now defaults to using Coinbase
attestations for the `<Badge />` component.

This prop will be removed in a future version of OnchainKit.

