---
title: "Getting Started"
---

import StartBuilding from "/snippets/start-building.mdx";
import InstallationOptions from '/snippets/installation-options.mdx';


OnchainKit is your go-to SDK for building beautiful onchain applications. Ship in minutes, not weeks.

Anyone can build an onchain app in 15 minutes with OnchainKit. No blockchain experience required.

## Why OnchainKit?

OnchainKit streamlines app development by providing a comprehensive toolkit that combines powerful onchain features with developer-friendly design:

- **Ergonomic design:** Full-stack tools that make complex onchain interactions intuitive
- **Battle-tested patterns:** Industry best practices packaged into ready-to-use solutions
- **Purpose-built components:** Pre-built modules for common onchain workflows
- **Framework agnostic:** Compatible with any React-supporting framework
- **Supercharged by Base:** Deep integration with Base's protocol features and ecosystem

## Automatic Installation

<Frame>
<img
  alt="OnchainKit Template"
  src="/images/onchainkit/quickstart.png"
  height="364"
/>
</Frame>

We recommend starting a new OnchainKit app using `create onchain`, which sets up everything automatically for you. To create a project, run:

```bash Terminal
npm create onchain@latest
```

After the prompts, `create onchain` will create a folder with your project name and install the required dependencies.

You can also checkout our pre-built templates:

- [Onchain Commerce](https://onchain-commerce-template.vercel.app/)
- [NFT minting](https://ock-mint.vercel.app/)
- [Funding flow](https://github.com/fakepixels/fund-component)
- [Social profile](https://github.com/fakepixels/ock-identity)

<Check>
  These docs are LLM-friendly—reference [OnchainKit AI Prompting Guide](/onchainkit/guides/ai-prompting-guide) in your code editor to streamline builds and prompt smarter.
</Check>

## Manual Installation

Add OnchainKit to your existing project manually.

<InstallationOptions />

## Testing Your OnchainKit App

Build reliable applications with comprehensive end-to-end testing using [OnchainTestKit](/onchainkit/guides/testing-with-onchaintestkit). Test wallet connections, transactions, and complex user flows with automated browser testing.

<StartBuilding />
