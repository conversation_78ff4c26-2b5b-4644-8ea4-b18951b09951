# https://docs.base.org/llms.txt

## Base Documentation — LLM Entry Point

> High‑signal index of section guides. Jump to a section’s llms.txt for concise intros, curated links, and fast navigation.

- [Get Started](./get-started/llms.txt) — Orientation, products, use cases, and quickstarts
- [Base Chain](./base-chain/llms.txt) — Deploy/connect, network info, tools, node ops, security
- [Base Account](./base-account/llms.txt) — Passkey smart wallet, payments, sponsored gas, sub‑accounts
- [Base App](./base-app/llms.txt) — Chat agents, transaction trays, getting featured
- [Mini Apps](./mini-apps/llms.txt) — MiniKit, manifests, features, growth, troubleshooting
- [OnchainKit](./onchainkit/llms.txt) — React SDK: provider, components, APIs, utilities, templates
- [Cookbook](./cookbook/llms.txt) — Task‑oriented recipes: payments, social, tokens, AI, MiniKit
- [Learn](./learn/llms.txt) — Curriculum: Ethereum concepts, Solidity, app dev, tools, tokens


