# https://docs.base.org/llms-full.txt

## Base Documentation — Full Context (Root)

> Rich, lightweight entry point for AI agents. Orients to cross-site concepts, SDK patterns, and links to per-product full shards. For deep dives, follow the Source links.

## How the docs are organized
- Products have two files each: `/<product>/llms.txt` (index) and `/<product>/llms-full.txt` (expanded)
- Main sections: Get Started, Base Chain, Base Account, Base App, Mini Apps, OnchainKit, Cookbook, Learn

## Cross-site concepts

### Networks and environments
Sources:
- `https://docs.base.org/base-chain/quickstart/connecting-to-base.md`
- `https://docs.base.org/base-chain/network-information/diffs-ethereum-base.md`
- `https://docs.base.org/base-chain/tools/node-providers.md`

> Base runs on Ethereum L2 with mainnet and Base Sepolia testnet. Use official RPCs from trusted node providers. Typical chain IDs: Base mainnet (8453), Base Sepolia (84532). Prefer HTTPS RPCs behind T<PERSON>, pin to provider domains, and handle occasional reorgs. For clients (e.g., Viem), configure the chain and transport explicitly; avoid relying on window-injected providers in server contexts.

### Authentication and API usage
Sources:
- `https://docs.base.org/base-account/guides/authenticate-users.md`
- `https://docs.base.org/mini-apps/features/Authentication.md`
- `https://docs.base.org/onchainkit/config/onchainkit-provider.md`

> Auth patterns vary by product. Use SIWE (ERC‑6492 compatible) for user sign-in where smart wallets may not yet exist. In Mini Apps, defer auth until needed; prefer scoped, revocable permissions and transaction trays for intent. For SDK/server APIs (e.g., OnchainKit), supply API keys via environment variables, rotate regularly, and verify server-side signatures or webhooks. Never store user passkeys or raw private keys server-side.

### Errors and retries
Sources:
- `https://docs.base.org/onchainkit/api/get-swap-quote.md`
- `https://docs.base.org/base-chain/node-operators/troubleshooting.md`

> Expect standard JSON error envelopes with `code`, `message`, and optional `details`. Treat 429 as rate-limited: respect `Retry-After` and apply exponential backoff with jitter. For idempotent operations (payments, tx-build), include an idempotency key if supported. Distinguish user-declined actions from transport or server errors and surface clear recovery guidance to users.

### Rate limits
Sources:
- `https://docs.base.org/onchainkit/api/get-tokens.md`
- `https://docs.base.org/base-chain/tools/node-providers.md`

> Rate limits vary by provider and endpoint. Batch reads when possible, cache static metadata, and use conditional requests. On 429, back off and retry after the indicated window. Avoid concurrent duplicate builds/quotes; fan out only when necessary.

### SDK install and hello world (JS/TS)

OnchainKit (UI + server APIs)

```bash
npm install @coinbase/onchainkit wagmi viem
```

```tsx
import { OnchainKitProvider } from '@coinbase/onchainkit'
import { base } from 'wagmi/chains'

export function Providers(props: { children: React.ReactNode }) {
  return (
    <OnchainKitProvider apiKey={process.env.NEXT_PUBLIC_ONCHAINKIT_API_KEY} chain={base}>
      {props.children}
    </OnchainKitProvider>
  )
}
```

Base Account (smart wallet + pay helper)

```bash
npm install @base-org/account
```

```ts
import { createBaseAccount, pay, getPaymentStatus } from '@base-org/account'

const account = await createBaseAccount({ owner: '0xYourEOA', chain: 'base-sepolia' })
const { id } = await pay({ amount: '5.00', to: '0xRecipient', testnet: true })
const { status } = await getPaymentStatus({ id })
```

Connect a client to Base (Viem)

```ts
import { createPublicClient, http } from 'viem'
import { base } from 'viem/chains'

const client = createPublicClient({ chain: base, transport: http() })
```

### Site-wide FAQ (very short)
- Can I use product X without Y? Yes. See each product’s Concepts for dependencies and synergies.
- Where are full code samples? In each product shard under Examples with links to repos.
- Which testnet should I use? Base Sepolia. Fund via listed faucets; verify contract addresses and RPC URLs.

## Directory of per-product full shards

- Base Account — `./base-account/llms-full.txt` — last updated 2025-08-19 — ~8.6 KB
- Base App — `./base-app/llms-full.txt` — last updated 2025-08-19 — ~4.2 KB
- Base Chain — `./base-chain/llms-full.txt` — last updated 2025-08-19 — ~8.1 KB
- Mini Apps — `./mini-apps/llms-full.txt` — last updated 2025-08-19 — ~9.9 KB
- OnchainKit — `./onchainkit/llms-full.txt` — last updated 2025-08-19 — ~6.9 KB
- Learn — `./learn/llms-full.txt` — last updated 2025-08-19 — ~5.0 KB
- Cookbook — `./cookbook/llms-full.txt` — last updated 2025-08-19 — ~2.5 KB

## Related indexes
- Root index: `./llms.txt`
- Get Started: `./get-started/llms.txt` and `./get-started/llms-full.txt`
- Each product section: `/<product>/llms.txt` (index)
 
