---
title: Base App Beta
description: Frequently asked questions about the Base  Wallet limited beta
---


Welcome to the Base app beta. Coinbase Wallet is now Base, an everything app to create, earn, trade, discover apps, and chat with friends all in one place.Here are answers to some frequently asked questions about the  beta! Thank you for building with us. 

## What is Base App

Base is the new name for Coinbase Wallet. A new experience is coming, with a current beta for some users. You can continue using the same Coinbase Wallet features in Base App. 


## Who can participate in the beta?

The beta is currently open to a limited group of testers. We’ll be rolling out to more users on our waitlist soon. 

## How do I get access to the beta app? 
Join the waitlist at [base.app](http://www.base.app) 

## Basenames

<AccordionGroup>
<Accordion title="I already have a basename but it isn’t showing up/ I don’t have the option to transfer it.">
A wallet can use multiple basenames. Sign up with a new basename, then transfer your existing basename to this new wallet. Here are the steps to transfer and use your existing basename.

1. [Transfer the basename between wallets](https://docs.base.org/identity/basenames/basenames-faq#10-how-do-i-transfer-my-basename-to-another-address).  
2. [Set the basename as the primary name on your new wallet.](https://docs.base.org/identity/basenames/basenames-faq#9-how-do-i-set-my-basename-as-my-primary-name-for-my-address)
</Accordion>
<Accordion title="Will my basename show up on Farcaster?">
Your basename will only be visible from users in the Base beta. Interaction from other clients will display your Farcaster username if you connected an account. If you create a new account your base name is set as the username on Farcaster.  
</Accordion>
</AccordionGroup>


## Wallet and Funds

### I logged into the beta, but don’t see my funds from my previous Coinbase Wallet. 
The Baset beta currently only supports smart wallets. Your funds are safe and still in the app. If you created a new smart wallet during the onboarding process, then your previous Externally Owned Account (EOA) wallet will only be available in the classic . 

You can return to your previous wallet by toggling beta mode off. 
Navigate to the Social tab (first icon), tap your profile pic, and toggle “beta mode” off.

### Smart Wallet 

What is a smart wallet?
A smart wallet is a passkey-secured, self-custodial onchain wallet that's embedded in the app. It's designed for easy onboarding and better user experience. No browser extensions, no app switching.

If you don't have a smart wallet, you will create one in the onboarding flow for the new beta app.

**I have Base, but how do I know if I have a smart wallet?**

If you use a passkey to sign onchain transactions, you have a smart wallet. If you don't know or you have a 12 word recovery phrase backed up somewhere, you use an EOA (externally owned account), not a smart wallet.

From the in-app browser, go to wallet.coinbase.com and log in. If you have a smart wallet, you'll see it say "smart wallet" in your account details.

You'll be asked to create or import a smart wallet on the way into the beta. If you are uncertain, create a new wallet.

**Do I need a smart wallet for the beta?**
Yes. The beta is smart wallet only


### Common Issues

<AccordionGroup>
<Accordion title="I logged into the beta, but don’t see my funds from my previous Coinbase Wallet.">
The Baset beta currently only supports smart wallets. Your funds are safe and still in the app. If you created a new smart wallet during the onboarding process, then your previous Externally Owned Account (EOA) wallet will only be available in the classic . 

<Info>
You can return to your previous wallet by toggling beta mode off.
Navigate to the Social tab (first icon), tap your profile pic, and toggle "beta mode" off.
</Info>
</Accordion>
</AccordionGroup>

## Farcaster Integration

<AccordionGroup>
<Accordion title="How do I connect my Farcaster account?">
Open the social tab and engage with any post (tap like or recast). You’ll be prompted to open the Farcaster app to connect your account. Follow the prompts to link Base Wallet to Farcaster. 
</Accordion>

<Accordion title="What if I don't have a Farcaster account?">
When signing up to the beta experience, you will be prompted to create a social account. 
</Accordion>
</AccordionGroup>

## Beta Management

### Toggling Beta Mode

**How can I toggle the beta off in Base again:** Navigate to the Social tab (first icon), tap your profile photo, and toggle “beta mode” off.

**I toggled beta mode off - how do I rejoin?** Navigate to the Assets tab (last tab on the right), select the settings icon in the upper right, and toggle “Beta mode”. 



### Additional Questions

<AccordionGroup>
<Accordion title="I needed to reinstall Base app and no longer have access to the beta - can I get another invite?">
Unfortunately, our invites are one time use. If you uninstall the app, we aren’t able to add you back into the beta. However, all your wallets will still be available as long as you have your passkeys, backups, and recovery phrases. 
</Accordion>
</AccordionGroup>

## Launch Timeline

<AccordionGroup>
<Accordion title="When will the official app launch?">
We will announce the official app launch date soon - thanks for being a part of the beta!
</Accordion>
</AccordionGroup>
