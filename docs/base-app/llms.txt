# https://docs.base.org/base-app/llms.txt

## Base App Documentation

> Base App is a social, onchain everything app (formerly Coinbase Wallet) that lets users discover Mini Apps and chat with AI messaging agents, with built-in smart-wallet transactions.

## Introduction
- [Beta FAQ](https://docs.base.org/base-app/introduction/beta-faq.md) — Beta overview, smart wallet behavior, and access

## Chat Agents
- [Getting Started](https://docs.base.org/base-app/agents/getting-started.md) — Create, test, and deploy an XMTP agent for Base App
- [Content Types](https://docs.base.org/base-app/agents/content-types.md) — Message schemas and payload formats for agents
- [Transaction Trays](https://docs.base.org/base-app/agents/transaction-trays.md) — Surface safe onchain actions in chat
- [Best Practices](https://docs.base.org/base-app/agents/best-practices.md) — Guidance for robust, user-safe agents

## Optional
- [Why Agents](https://docs.base.org/base-app/agents/why-agents.md) — Rationale and examples for messaging agents
- [Getting Featured](https://docs.base.org/base-app/agents/getting-featured.md) — How to get listed and discovered in Base App


