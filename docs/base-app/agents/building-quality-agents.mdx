---
title: 'Building Quality Agents for Base App'
description: Learn the best practices and guidelines for creating high-quality agents that get featured in Base App
sidebarTitle: 'Building Quality Agents'
---

As you start building, review these guidelines to understand what we look for when featuring agents in the Base app. We recommend trying out existing agents in the app first to get a feel for the quality bar, what works well, and areas for improvement.

## Build a high quality foundation

Your agent should provide a seamless, professional experience that users will want to engage with repeatedly. Here are the core requirements:

### Response Behavior

**Multi-Channel Support**
- Respond to both DMs and group chats appropriately
- Maintain consistent functionality across different conversation types

**Immediate Feedback**
- React to messages with a simple reaction (👀, 👍, ⌛, etc.) to show acknowledgment
- This gives users confidence their message was received while processing

**Fast Response Times**
- Provide responses quickly (< 5 seconds)
- Users expect near-instant communication in messaging apps

### Group Chat Etiquette

In group chats, agents should only respond when:

1. **Mentioned directly** with "@" + agent name (e.g., @bankr)
2. **Replied to directly** when a user replies to the agent's message using the reply content type

This prevents spam and ensures agents participate naturally in group conversations.

### Communication Style

**Sound Human**
- Use conversational, fun, and clear language
- Keep responses polished but not robotic
- Match the energy and tone of the conversation

**Privacy Conscious**
- Only ask for personal information when absolutely necessary
- Always explain why the information is needed
- Respect user privacy and data minimization principles

## Craft compelling onboarding

Your agent's first impression is critical. The onboarding message should immediately communicate value and give users a clear path forward.

### Great Onboarding Message Structure

1. **Introduce the agent** - Quick, friendly greeting with the agent's name
2. **Explain capabilities** - Clear, specific examples of what it can do
3. **Provide next steps** - Give users an obvious action to take

### Example: High-Quality Onboarding

```
hey, i'm bankr. i can help you trade, transfer, and manage your crypto. here's the rundown:

• trade anything: buy, sell, swap tokens on base, polygon, and mainnet. try "buy 0.1 eth of degen."
• send it: transfer crypto to anyone on x, farcaster, or a wallet address.
• get alpha: token recs, market data, charts.
• automate: set up recurring buys/sells. e.g. "buy $10 of $bnkr every week."

what do you want to do first?
```

**Why this works:**
- Friendly, conversational tone
- Specific feature examples with concrete commands
- Clear value propositions
- Ends with a direct call-to-action

### Example: Poor Onboarding

```
Gm! What can I help you with?
```

**Why this fails:**
- Generic greeting with no context
- No explanation of capabilities
- Puts burden on user to figure out what to do
- No clear value proposition

## Showcase unique value

### Solve Real Problems

Your agent should:
- **Address a unique pain point** or bring a delightful twist to an existing space
- **Help users accomplish tasks** more easily than existing solutions
- **Provide clear benefits** that users can understand immediately

### Enable User Success

Focus on helping users:
- **Earn** - Generate income, rewards, or value
- **Connect** - Build relationships or communities
- **Have fun** - Provide entertainment or engaging experiences
- **Complete tasks** - Streamline workflows or processes

### Design for Engagement

**Build Natural Growth Loops**
- Include features that encourage sharing and re-engagement
- Make it beneficial for users to invite others
- Create ongoing value that brings users back

**Plan the User Journey**
1. **Define the ideal user experience first**
2. **Craft agent messages around that journey**
3. **Guide users through progressive value discovery**

### Continuous Engagement Strategy

As users complete actions with your agent:

- **Show clear next steps** - Always give users something else to try
- **Highlight ongoing value** - Explain how continued use benefits them
- **Create habit loops** - Design features that encourage regular interaction
- **Prevent one-and-done usage** - Build features that require return visits

### Examples of Engagement Features

- **Progressive features** - Unlock new capabilities as users engage more
- **Personalization** - Learn user preferences and customize experiences  
- **Social elements** - Enable sharing achievements or inviting friends
- **Recurring value** - Automated tasks, alerts, or regular check-ins
- **Gamification** - Points, levels, or achievement systems

## Next steps

Once you've built your agent following these guidelines:

1. **Test thoroughly** - Try your agent in different scenarios and conversation types
2. **Get feedback** - Have others test and provide honest feedback
3. **Iterate quickly** - Make improvements based on real user interactions
4. **Submit for review** - Follow the submission guidelines when ready

Remember: agents often go through multiple rounds of feedback before being featured. Focus on creating genuine value and a polished user experience that people will love using repeatedly.

For technical implementation details, see our [Chat Agents guide](/base-app/agents/chat-agents).
