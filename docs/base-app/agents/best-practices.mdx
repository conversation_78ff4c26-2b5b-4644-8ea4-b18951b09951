---
title: 'Chat Agent Best Practices'
description: 'Guidelines and recommendations for building effective chat agents'
sidebarTitle: 'Best Practices'
---

### Best practices

1. Always validate content structures
2. Provide fallbacks for unsupported clients
3. Test across client versions
4. Limit message and metadata size
5. Follow [XIP-67](https://community.xmtp.org/t/xip-67-inline-actions/941)
6. Your agent should respond to both direct messages and group chat messages (if applicable).
7. For direct messages, it should automatically respond.
8. We recommend reacting to messages with an emoji (like 👀or ⌛) to indicate the message was received, while the response is processing.
9. For group chat messages, agents should only respond if they are mentioned with the "@" symbol and the agent's name. (example "@bankr"), OR if the user replies directly to a message from the agent (using the reply content type).

For group chat messages, agents should only respond if they are mentioned with the "@" symbol and the agent's name. (example "@bankr"), OR if the user replies directly to a message from the agent (using the reply content type).
