---
title: 'Getting Started with Chat Agents'
description: 'Step-by-step guide to creating, testing, and deploying your first XMTP messaging agent'
sidebarTitle: 'Getting Started'
---

This guide will walk you through creating, testing, and deploying your first XMTP messaging agent. By the end, you'll have a fully functional agent that can send and receive messages on the XMTP messaging network.

## Prerequisites

Before you begin, make sure you have:

- Node.js (v20 or higher)
- Git
- A code editor
- Basic knowledge of JavaScript/TypeScript

## Helpful Resources

- [Getting Started with XMTP (Video)](https://www.youtube.com/watch?v=djRLnWUvwIA)
- [Building Agents on XMTP](https://github.com/ephemeraHQ/xmtp-agent-examples)
- [Cursor Rules](https://github.com/ephemeraHQ/xmtp-agent-examples/blob/main/.cursor/rules/xmtp.md)
- [XMTP Documentation](https://docs.xmtp.org/)
- [Faucets](https://portal.cdp.coinbase.com/products/faucet)

## Development Guide

<Steps>
  <Step title="Set Up Your Development Environment">
    Clone the XMTP Agent Examples Repository:

    ```bash
    git clone https://github.com/ephemeraHQ/xmtp-agent-examples.git
    cd xmtp-agent-examples
    ```

  </Step>

  <Step title="Install Dependencies">
    Install all required packages:

    ```bash
    npm install
    ```

  </Step>

  <Step title="Generate Keys for Your Agent">
    Run the key generation script to create your agent's wallet:

    ```bash
    npm run gen:keys
    ```

    This creates a .env file with:

    ```bash
    WALLET_KEY=0x... # Your agent's private key
    ENCRYPTION_KEY=... # Encryption key for local database
    XMTP_ENV=dev # Environment (local, dev, production)
    ```

  </Step>

  <Step title="Write Your Agent Logic">
    Create a basic agent that responds to messages:

    ```javascript
    // import the xmtp sdk
    import { Client, type XmtpEnv, type Signer } from "@xmtp/node-sdk";

    // encryption key, must be consistent across runs
    const encryptionKey: Uint8Array = ...;
    const signer: Signer = ...;
    const env: XmtpEnv = "dev";

    // create the client
    const client = await Client.create(signer, {encryptionKey, env });
    // sync the client to get the latest messages
    await client.conversations.sync();

    // listen to all messages
    const stream = await client.conversations.streamAllMessages();
    for await (const message of  stream) {
      // ignore messages from the agent
      if (message?.senderInboxId === client.inboxId ) continue;
      // get the conversation by id
      const conversation = await client.conversations.getConversationById(message.conversationId);
      // send a message from the agent
      await conversation.send("gm");
    }
    ```

    Then run your agent:

    ```bash
    npm run dev
    ```

    ### Getting the address of a user

    Each user has a unique inboxId for retrieving their associated addresses (identifiers). One inboxId can have multiple identifiers like passkeys or EVM wallet addresses.

    ```tsx
    const inboxState = await client.preferences.inboxStateFromInboxIds([
      message.senderInboxId,
    ]);
    const addressFromInboxId = inboxState[0].identifiers[0].identifier;
    ```

    ### Example Implementations

    Explore these examples in the `/examples` folder:

    - [xmtp-gm](https://github.com/ephemeraHQ/xmtp-agent-examples/tree/main/examples/xmtp-gm): A simple agent that replies to all text messages with "gm"
    - [xmtp-gpt](https://github.com/ephemeraHQ/xmtp-agent-examples/tree/main/examples/xmtp-gpt): An example using GPT API's to answer messages
    - [xmtp-nft-gated-group](https://github.com/ephemeraHQ/xmtp-agent-examples/tree/main/examples/xmtp-nft-gated-group): Add members to a group based on an NFT
    - [xmtp-coinbase-agentkit](https://github.com/ephemeraHQ/xmtp-agent-examples/tree/main/examples/xmtp-coinbase-agentkit): Agent that uses a CDP for gassless USDC on base
    - [xmtp-transactions](https://github.com/ephemeraHQ/xmtp-agent-examples/tree/main/examples/xmtp-transactions): Use XMTP content types to send transactions
    - [xmtp-smart-wallet](https://github.com/ephemeraHQ/xmtp-agent-examples/tree/main/examples/xmtp-smart-wallet): Agent that uses a smart wallet to send messages

  </Step>

  <Step title="Test Your Agent">
    ### Development Testing

    1. Start your agent:
    ```javascript
    npm run dev
    ```

    2. Test on [xmtp.chat](https://xmtp.chat/conversations):
    - Go to https://xmtp.chat
    - Connect your personal wallet
    - Switch to Dev environment in settings
    - Start a new conversation with your agent's public address (from .env)
    - Send a test message and verify the agent responds

    ### Production Testing

    1. Update environment:
    ```javascript
    XMTP_ENV = production;
    ```

    2. Test on Base App:
    - Open Base App mobile app
    - Go to messaging
    - Start conversation with your agent's address
    - Verify functionality

  </Step>

  <Step title="Get a Basename for Your Agent">
    Give your agent a human-readable name:

    1. **Import agent wallet to Base App extension:**
    - Install Base App browser extension
    - Import using your agent's private key

    2. **Purchase a basename:**
    - Visit https://base.org/names
    - Connect your agent's wallet
    - Search and purchase your desired basename (e.g., myagent.base.eth)
    - Set as primary name

    3. **Verify setup:**
    - Your agent can now be reached via the basename instead of the long address
    - Users can message myagent.base.eth instead of 0x123...

  </Step>

  <Step title="Deploy Your Agent">
    ### Option 1: Railway (Recommended)

    - Visit https://railway.app
    - Connect your GitHub repository
    - Add environment variables in Railway dashboard:
      - XMTP_ENV=production
      - WALLET_KEY=your_agent_private_key
      - ENCRYPTION_KEY=your_agent_encryption_key
    - Deploy and monitor logs

    ### Option 2: Other Platforms

    Heroku, Vercel, or any Node.js hosting platform:
    - Ensure your package.json has the correct start script
    - Set environment variables in your hosting platform
    - Deploy your repository

  </Step>

  <Step title="Monitor and Maintain" icon="chart-line">
    ### Best Practices

    1. Logging: Add comprehensive logging to track agent activity
    2. Error Handling: Implement try-catch blocks for network issues
    3. Rate Limiting: Respect XMTP rate limits in your agent logic
    4. Security: Never expose private keys; use environment variables

    ### Monitoring

    Add to your agent for basic monitoring:

    ```javascript
    const inboxState = await client.preferences.inboxState();
    const address = inboxState.identifiers[0].identifier;
    const inboxId = client.inboxId;
    const installationId = client.installationId;
    const conversations = await client.conversations.list();

    console.log(`
    ✓ XMTP Client:
    • InboxId: ${inboxId}
    • Address: ${address}
    • Conversations: ${conversations.length}
    • Installations: ${inboxState.installations.length}
    • InstallationId: ${installationId}
    • Network: ${process.env.XMTP_ENV}`);

    console.log(`Agent started. Address: ${xmtp.address}`)
    console.log(`Environment: ${process.env.XMTP_ENV}`)
    console.log(`Listening for messages...`)
    ```

  </Step>
</Steps>
