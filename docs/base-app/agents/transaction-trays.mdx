---
title: 'Custom Transaction Trays'
description: 'Learn how to customize transaction trays with agent branding and information'
sidebarTitle: 'Transaction Trays'
---

## Custom Transaction Trays

If you would like to display agent information such as favicon and branded title, utilize the metadata property of wallet send calls data.

#### **Example: Display agent information**

```ts
// example of wallet send calls data shape{
  version: "1.0",
  from: request.from as `0x${string}`,
  chainId: this.networkConfig.chainId,
  calls: [
	{
	  ...
  metadata: {
    description: "Transfer token",
    ...other fields,
    hostname: "tba.chat",
    faviconUrl: "https://tba.chat/favicon",
    title: "Your favorite Agent"
  }
 	}
  ]
}
```

<Frame caption='transaction tray'>
  <img src='/images/transaction_tray.jpeg' alt='Transaction tray' />
</Frame>

You can send "GM" to **tbachat.base.eth** to get more details about message content types we support and get the firsthand experience on by interacting with our agent

Our agent repo is found [here](https://github.com/siwan-cb/tba-chat-example-bot)
