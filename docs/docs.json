{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "Base Documentation", "colors": {"primary": "#578BFA", "light": "#578BFA", "dark": "#578BFA"}, "favicon": "/logo/favicon.png", "contextual": {"options": ["copy", "chatgpt", "claude"]}, "api": {"playground": {"display": "simple"}, "examples": {"languages": ["javascript"]}}, "banner": {"content": "[On September 17th, Base Chain will begin enforcing a per-transaction gas cap. Read more here](/base-chain/network-information/block-building#per-transaction-gas-maximum)."}, "navigation": {"tabs": [{"tab": "Get Started", "groups": [{"group": "Introduction", "pages": ["get-started/base"]}, {"group": "Browse by", "pages": ["get-started/products", "get-started/use-cases"]}, {"group": "Quickstart", "pages": ["get-started/build-app", "get-started/launch-token", "get-started/deploy-chain", "get-started/deploy-smart-contracts"]}, {"group": "Builder Support", "pages": ["get-started/get-funded", "get-started/base-services-hub", "get-started/base-mentorship-program", "get-started/country-leads-and-ambassadors"]}, {"group": "Build with AI", "pages": ["get-started/ai-prompting", "get-started/prompt-library"]}], "global": {"anchors": [{"anchor": "Status", "href": "https://status.base.org/", "icon": "signal-bars"}, {"anchor": "Faucet", "href": "https://docs.base.org/base-chain/tools/network-faucets", "icon": "gas-pump"}, {"anchor": "Bridge", "href": "https://docs.base.org/base-chain/network-information/bridges-mainnet", "icon": "coin"}]}}, {"tab": "Base Chain", "groups": [{"group": "Quickstart", "pages": ["base-chain/quickstart/why-base", "base-chain/quickstart/deploy-on-base", "base-chain/quickstart/connecting-to-base", "base-chain/quickstart/bridge-token"]}, {"group": "Network Information", "pages": ["base-chain/network-information/base-contracts", "base-chain/network-information/network-fees", "base-chain/network-information/ecosystem-contracts", "base-chain/network-information/block-building", "base-chain/network-information/diffs-ethereum-base"]}, {"group": "Flashblocks", "pages": ["base-chain/flashblocks/apps", "base-chain/flashblocks/node-providers", "base-chain/flashblocks/docs"]}, {"group": "Node Operators", "pages": ["base-chain/node-operators/run-a-base-node", "base-chain/node-operators/performance-tuning", "base-chain/node-operators/snapshots", "base-chain/node-operators/troubleshooting"]}, {"group": "Tools", "pages": ["base-chain/tools/base-products", "base-chain/tools/onchain-registry-api", "base-chain/tools/node-providers", "base-chain/tools/block-explorers", "base-chain/tools/network-faucets", "base-chain/tools/oracles", "base-chain/tools/onboarding", "base-chain/tools/data-indexers", "base-chain/tools/cross-chain", "base-chain/tools/account-abstraction", "base-chain/tools/onramps", "base-chain/tools/tokens-in-wallet"]}, {"group": "Security", "pages": ["base-chain/security/security-council", "base-chain/security/avoid-malicious-flags", "base-chain/security/report-vulnerability"]}], "global": {"anchors": [{"anchor": "GitHub", "href": "https://github.com/base", "icon": "github"}, {"anchor": "Status", "href": "https://status.base.org/", "icon": "signal-bars"}, {"anchor": "Chain Stats", "href": "https://www.base.org/stats", "icon": "chart-line"}, {"anchor": "Explorer", "href": "https://basescan.org/", "icon": "magnifying-glass"}, {"anchor": "Support", "href": "https://discord.com/invite/buildonbase", "icon": "discord"}]}}, {"tab": "Base Account", "groups": [{"group": "Introduction", "pages": ["base-account/overview/what-is-base-account"]}, {"group": "Quickstart", "pages": ["base-account/quickstart/web", "base-account/quickstart/web-react", "base-account/quickstart/mobile-integration"]}, {"group": "Guides", "pages": ["base-account/guides/authenticate-users", "base-account/guides/accept-payments", "base-account/guides/accept-recurring-payments", "base-account/improve-ux/batch-transactions", "base-account/improve-ux/sponsor-gas/paymasters", "base-account/improve-ux/sub-accounts", "base-account/improve-ux/spend-permissions", "base-account/improve-ux/magic-spend", "base-account/guides/sign-and-verify-typed-data", "base-account/improve-ux/sponsor-gas/erc20-paymasters"]}, {"group": "Framework Integrations", "pages": [{"group": "<PERSON><PERSON><PERSON>", "pages": ["base-account/framework-integrations/wagmi/setup", "base-account/framework-integrations/wagmi/sign-in-with-base", "base-account/framework-integrations/wagmi/base-pay", "base-account/framework-integrations/wagmi/other-use-cases"]}, {"group": "Privy", "pages": ["base-account/framework-integrations/privy/setup", "base-account/framework-integrations/privy/sub-accounts"]}, "base-account/framework-integrations/nextjs-with-dynamic"]}, {"group": "Reference", "pages": [{"group": "Account SDK", "pages": ["base-account/reference/core/createBaseAccount", {"group": "Base Pay", "pages": ["base-account/reference/base-pay/pay", "base-account/reference/base-pay/getPaymentStatus"]}, {"group": "Subscriptions", "pages": ["base-account/reference/base-pay/subscriptions-overview", "base-account/reference/base-pay/subscribe", "base-account/reference/base-pay/getStatus", "base-account/reference/base-pay/prepareCharge"]}, "base-account/reference/core/getProvider", "base-account/reference/spend-permission-utilities/requestSpendPermission", "base-account/reference/spend-permission-utilities/prepareSpendCallData", "base-account/reference/spend-permission-utilities/fetchPermissions", "base-account/reference/spend-permission-utilities/fetchPermission", "base-account/reference/spend-permission-utilities/getPermissionStatus", "base-account/reference/spend-permission-utilities/requestRevoke", "base-account/reference/spend-permission-utilities/prepareRevokeCallData", "base-account/reference/core/generateKeyPair", "base-account/reference/core/getKeypair", "base-account/reference/core/getCryptoKeyAccount"]}, {"group": "Provider", "pages": [{"group": "Methods", "pages": ["base-account/reference/core/provider-rpc-methods/request-overview", "base-account/reference/core/provider-rpc-methods/wallet_connect", "base-account/reference/core/provider-rpc-methods/wallet_sendCalls", "base-account/reference/core/provider-rpc-methods/wallet_getCallsStatus", "base-account/reference/core/provider-rpc-methods/wallet_getCapabilities", "base-account/reference/core/provider-rpc-methods/wallet_addSubAccount", "base-account/reference/core/provider-rpc-methods/wallet_getSubAccounts", "base-account/reference/core/provider-rpc-methods/coinbase_fetchPermissions", "base-account/reference/core/provider-rpc-methods/coinbase_fetchPermission", "base-account/reference/core/provider-rpc-methods/eth_accounts", "base-account/reference/core/provider-rpc-methods/eth_requestAccounts", "base-account/reference/core/provider-rpc-methods/eth_chainId", "base-account/reference/core/provider-rpc-methods/eth_blockNumber", "base-account/reference/core/provider-rpc-methods/eth_coinbase", "base-account/reference/core/provider-rpc-methods/eth_getBalance", "base-account/reference/core/provider-rpc-methods/eth_getTransactionCount", "base-account/reference/core/provider-rpc-methods/eth_getTransactionByHash", "base-account/reference/core/provider-rpc-methods/eth_getTransactionReceipt", "base-account/reference/core/provider-rpc-methods/eth_getBlockByNumber", "base-account/reference/core/provider-rpc-methods/eth_getBlockByHash", "base-account/reference/core/provider-rpc-methods/eth_getBlockTransactionCountByNumber", "base-account/reference/core/provider-rpc-methods/eth_getBlockTransactionCountByHash", "base-account/reference/core/provider-rpc-methods/eth_sendTransaction", "base-account/reference/core/provider-rpc-methods/eth_sendRawTransaction", "base-account/reference/core/provider-rpc-methods/eth_estimateGas", "base-account/reference/core/provider-rpc-methods/eth_gasPrice", "base-account/reference/core/provider-rpc-methods/eth_feeHistory", "base-account/reference/core/provider-rpc-methods/eth_getCode", "base-account/reference/core/provider-rpc-methods/eth_getStorageAt", "base-account/reference/core/provider-rpc-methods/eth_getLogs", "base-account/reference/core/provider-rpc-methods/eth_getProof", "base-account/reference/core/provider-rpc-methods/personal_sign", "base-account/reference/core/provider-rpc-methods/eth_signTypedData_v4", "base-account/reference/core/provider-rpc-methods/wallet_addEthereumChain", "base-account/reference/core/provider-rpc-methods/wallet_switchEthereumChain", "base-account/reference/core/provider-rpc-methods/wallet_watchAsset", "base-account/reference/core/provider-rpc-methods/eth_getTransactionByBlockHashAndIndex", "base-account/reference/core/provider-rpc-methods/eth_getTransactionByBlockNumberAndIndex", "base-account/reference/core/provider-rpc-methods/eth_getUncleCountByBlockHash", "base-account/reference/core/provider-rpc-methods/eth_getUncleCountByBlockNumber", "base-account/reference/core/provider-rpc-methods/web3_clientVersion"]}, {"group": "Capabilities", "pages": ["base-account/reference/core/capabilities/overview", "base-account/reference/core/capabilities/signInWithEthereum", "base-account/reference/core/capabilities/atomic", "base-account/reference/core/capabilities/flowControl", "base-account/reference/core/capabilities/paymasterService", "base-account/reference/core/capabilities/auxiliaryFunds", "base-account/reference/core/capabilities/datacallback"]}]}, {"group": "UI Elements", "pages": ["base-account/reference/ui-elements/base-pay-button", "base-account/reference/ui-elements/sign-in-with-base-button", "base-account/reference/ui-elements/brand-guidelines"]}, {"group": "Onchain Contracts", "pages": ["base-account/reference/onchain-contracts/spend-permissions", "base-account/reference/onchain-contracts/smart-wallet", "base-account/reference/onchain-contracts/basenames"]}]}, {"group": "More", "pages": [{"group": "Troubleshooting", "pages": ["base-account/more/troubleshooting/usage-details/popups", "base-account/more/troubleshooting/usage-details/gas-usage", "base-account/more/troubleshooting/usage-details/unsupported-calls", "base-account/more/troubleshooting/usage-details/simulations", "base-account/more/troubleshooting/usage-details/wallet-library-support"]}, "base-account/more/base-gasless-campaign", "base-account/more/telemetry", "base-account/guides/migration-guide"]}, {"group": "Basenames", "pages": ["base-account/basenames/basenames-faq", "base-account/basenames/basename-transfer", "base-account/basenames/basenames-onchainkit-tutorial", "base-account/basenames/basenames-wagmi-tutorial"]}, {"group": "Contribute", "pages": ["base-account/contribute/contribute-to-base-account-docs", "base-account/contribute/security-and-bug-bounty"]}], "global": {"anchors": [{"anchor": "GitHub", "href": "https://github.com/coinbase/onchainkit", "icon": "github"}, {"anchor": "Support", "href": "https://discord.com/invite/buildonbase", "icon": "discord"}]}}, {"tab": "Base App", "groups": [{"group": "Introduction", "pages": ["base-app/introduction/beta-faq"]}, {"group": "Chat Agents", "pages": ["base-app/agents/why-agents", "base-app/agents/chat-agents", "base-app/agents/building-quality-agents", "base-app/agents/getting-started", "base-app/agents/content-types", "base-app/agents/transaction-trays", "base-app/agents/deeplinks", "base-app/agents/x402-agents", "base-app/agents/mini-apps-and-agents", "base-app/agents/best-practices", "base-app/agents/getting-featured"]}]}, {"tab": "Mini Apps", "groups": [{"group": "Quickstart", "pages": ["mini-apps/quickstart/migrate-existing-apps", "mini-apps/quickstart/create-new-miniapp", "mini-apps/quickstart/launch-checklist"]}, {"group": "Get Featured", "pages": ["mini-apps/get-featured/requirements"]}, {"group": "Features", "pages": ["mini-apps/features/overview", "mini-apps/features/manifest", "mini-apps/features/authentication", "mini-apps/features/context", "mini-apps/features/embeds-and-previews", "mini-apps/features/search-and-discovery", "mini-apps/features/sharing-and-social-graph", "mini-apps/features/notifications", "mini-apps/features/wallet", "mini-apps/features/links"]}, {"group": "Design Guidelines", "pages": [{"group": "Foundations", "pages": ["mini-apps/design-ux/foundations/app-icon", "mini-apps/design-ux/foundations/colors", "mini-apps/design-ux/foundations/typography", "mini-apps/design-ux/foundations/spacing", "mini-apps/design-ux/foundations/navigation", "mini-apps/design-ux/foundations/mobile-first"]}, "mini-apps/design-ux/onchain-ux", "mini-apps/design-ux/components", "mini-apps/design-ux/resources"]}, {"group": "Growth Playbook", "pages": ["mini-apps/overview", "mini-apps/growth/optimize-onboarding", "mini-apps/growth/build-viral-mini-apps", "mini-apps/growth/data-driven-growth", "mini-apps/growth/rewards"]}, {"group": "Troubleshooting", "pages": ["mini-apps/troubleshooting/common-issues", "mini-apps/troubleshooting/base-app-compatibility"]}, {"group": "Technical Reference", "pages": [{"group": "MiniKit", "pages": ["mini-apps/technical-reference/minikit/overview", "mini-apps/technical-reference/minikit/provider-and-initialization", {"group": "<PERSON>s", "pages": ["mini-apps/technical-reference/minikit/hooks/useMiniKit", "mini-apps/technical-reference/minikit/hooks/useOpenUrl", "mini-apps/technical-reference/minikit/hooks/useClose", "mini-apps/technical-reference/minikit/hooks/usePrimaryButton", "mini-apps/technical-reference/minikit/hooks/useViewProfile", "mini-apps/technical-reference/minikit/hooks/useComposeCast", "mini-apps/technical-reference/minikit/hooks/useViewCast", "mini-apps/technical-reference/minikit/hooks/useAuthenticate", "mini-apps/technical-reference/minikit/hooks/useAddFrame", "mini-apps/technical-reference/minikit/hooks/useNotification"]}]}]}, {"group": "Resources", "pages": ["mini-apps/resources/templates", "mini-apps/llms-full.txt"]}]}, {"tab": "OnchainKit", "versions": [{"version": "latest", "groups": [{"group": "Getting started", "pages": ["onchainkit/latest/getting-started/overview", "onchainkit/latest/getting-started/quickstart-guide", "onchainkit/latest/getting-started/manual-installation", "onchainkit/latest/getting-started/troubleshooting"]}, {"group": "Configuration", "pages": ["onchainkit/latest/configuration/onchainkit-provider", "onchainkit/latest/configuration/wagmi-viem-integration", "onchainkit/latest/configuration/themes"]}, {"group": "Components", "pages": [{"group": "Appchain", "pages": ["onchainkit/latest/components/appchain/bridge"]}, {"group": "Buy", "pages": ["onchainkit/latest/components/buy/buy"]}, {"group": "Checkout", "pages": ["onchainkit/latest/components/checkout/checkout"]}, {"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/latest/components/earn/earn"]}, {"group": "Fund", "pages": ["onchainkit/latest/components/fund/fund-button", "onchainkit/latest/components/fund/fund-card"]}, {"group": "Identity", "pages": ["onchainkit/latest/components/identity/identity", "onchainkit/latest/components/identity/address", "onchainkit/latest/components/identity/avatar", "onchainkit/latest/components/identity/badge", "onchainkit/latest/components/identity/identity-card", "onchainkit/latest/components/identity/name", "onchainkit/latest/components/identity/socials"]}, {"group": "Mint", "pages": ["onchainkit/latest/components/mint/nft-card", "onchainkit/latest/components/mint/nft-mint-card"]}, {"group": "Signature", "pages": ["onchainkit/latest/components/signature/signature"]}, {"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/latest/components/swap/swap", "onchainkit/latest/components/swap/swap-settings"]}, {"group": "Token", "pages": ["onchainkit/latest/components/token/token-chip", "onchainkit/latest/components/token/token-image", "onchainkit/latest/components/token/token-row", "onchainkit/latest/components/token/token-search", "onchainkit/latest/components/token/token-select-dropdown"]}, {"group": "Transaction", "pages": ["onchainkit/latest/components/transaction/transaction"]}, {"group": "Wallet", "pages": ["onchainkit/latest/components/wallet/wallet", "onchainkit/latest/components/wallet/wallet-dropdown-basename", "onchainkit/latest/components/wallet/wallet-dropdown-disconnect", "onchainkit/latest/components/wallet/wallet-dropdown-fund-link", "onchainkit/latest/components/wallet/wallet-dropdown-link", "onchainkit/latest/components/wallet/wallet-island", "onchainkit/latest/components/wallet/wallet-modal"]}, {"group": "Connected", "pages": ["onchainkit/latest/components/connected/connected"]}, {"group": "MiniKit", "pages": ["onchainkit/latest/components/minikit/autoconnect", "onchainkit/latest/components/minikit/if-in-miniapp", "onchainkit/latest/components/minikit/safe-area"]}]}, {"group": "Utilities", "pages": [{"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/latest/utilities/earn/build-deposit-to-morpho-tx", "onchainkit/latest/utilities/earn/build-withdraw-from-morpho-tx"]}, {"group": "Fund", "pages": ["onchainkit/latest/utilities/fund/get-onramp-buy-url", "onchainkit/latest/utilities/fund/fetch-onramp-config", "onchainkit/latest/utilities/fund/fetch-onramp-quote", "onchainkit/latest/utilities/fund/fetch-onramp-options", "onchainkit/latest/utilities/fund/fetch-onramp-transaction-status", "onchainkit/latest/utilities/fund/setup-onramp-event-listeners"]}, {"group": "Identity", "pages": ["onchainkit/latest/utilities/identity/get-address", "onchainkit/latest/utilities/identity/get-attestations", "onchainkit/latest/utilities/identity/get-avatar", "onchainkit/latest/utilities/identity/get-avatars", "onchainkit/latest/utilities/identity/get-name", "onchainkit/latest/utilities/identity/get-names"]}, {"group": "Token", "pages": ["onchainkit/latest/utilities/token/format-amount"]}, {"group": "Wallet", "pages": ["onchainkit/latest/utilities/wallet/is-valid-aa-entrypoint", "onchainkit/latest/utilities/wallet/is-wallet-a-coinbase-smart-wallet"]}]}, {"group": "<PERSON>s", "pages": [{"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/latest/hooks/earn/use-earn-context", "onchainkit/latest/hooks/earn/use-build-deposit-to-morpho-tx", "onchainkit/latest/hooks/earn/use-build-withdraw-from-morpho-tx", "onchainkit/latest/hooks/earn/use-morpho-vault"]}, {"group": "Identity", "pages": ["onchainkit/latest/hooks/identity/use-address", "onchainkit/latest/hooks/identity/use-avatar", "onchainkit/latest/hooks/identity/use-avatars", "onchainkit/latest/hooks/identity/use-name", "onchainkit/latest/hooks/identity/use-names"]}, {"group": "Mint", "pages": ["onchainkit/latest/hooks/mint/use-token-details", "onchainkit/latest/hooks/mint/use-mint-details"]}]}, {"group": "Guides", "pages": ["onchainkit/latest/guides/ai-prompting-guide", "onchainkit/latest/guides/contributing"]}]}, {"version": "v0.38.x", "groups": [{"group": "Introduction", "pages": ["onchainkit/getting-started", "onchainkit/guides/telemetry", "onchainkit/guides/troubleshooting"]}, {"group": "Installation", "pages": ["onchainkit/installation/nextjs", "onchainkit/installation/vite", "onchainkit/installation/remix", "onchainkit/installation/astro"]}, {"group": "Config", "pages": ["onchainkit/config/onchainkit-provider", "onchainkit/config/supplemental-providers"]}, {"group": "Guides", "pages": ["onchainkit/guides/lifecycle-status", "onchainkit/guides/tailwind", "onchainkit/guides/themes", "onchainkit/guides/use-basename-in-onchain-app", "onchainkit/guides/using-ai-powered-ides", "onchainkit/guides/ai-prompting-guide", "onchainkit/guides/testing-with-onchaintestkit"]}, {"group": "Templates", "pages": ["onchainkit/templates/onchain-nft-app", "onchainkit/templates/onchain-commerce-app", "onchainkit/templates/onchain-social-profile"]}, {"group": "Components", "pages": [{"group": "Appchain", "pages": ["onchainkit/appchain/bridge"]}, {"group": "Buy", "pages": ["onchainkit/buy/buy"]}, {"group": "Checkout", "pages": ["onchainkit/checkout/checkout"]}, {"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/earn/earn"]}, {"group": "Fund", "pages": ["onchainkit/fund/fund-button", "onchainkit/fund/fund-card"]}, {"group": "Identity", "pages": ["onchainkit/identity/identity", "onchainkit/identity/address", "onchainkit/identity/avatar", "onchainkit/identity/badge", "onchainkit/identity/identity-card", "onchainkit/identity/name", "onchainkit/identity/socials"]}, {"group": "Mint", "pages": ["onchainkit/mint/nft-card", "onchainkit/mint/nft-mint-card"]}, "onchainkit/signature/signature", {"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/swap/swap", "onchainkit/swap/swap-settings"]}, {"group": "Token", "pages": ["onchainkit/token/token-chip", "onchainkit/token/token-image", "onchainkit/token/token-row", "onchainkit/token/token-search", "onchainkit/token/token-select-dropdown"]}, "onchainkit/transaction/transaction", {"group": "Wallet", "pages": ["onchainkit/wallet/wallet", "onchainkit/wallet/wallet-dropdown-basename", "onchainkit/wallet/wallet-dropdown-disconnect", "onchainkit/wallet/wallet-dropdown-fund-link", "onchainkit/wallet/wallet-dropdown-link", "onchainkit/wallet/wallet-island", "onchainkit/wallet/wallet-modal"]}]}, {"group": "API", "pages": [{"group": "Mint", "pages": ["onchainkit/api/get-token-details", "onchainkit/api/get-mint-details", "onchainkit/api/build-mint-transaction"]}, {"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/api/build-swap-transaction", "onchainkit/api/get-swap-quote"]}, {"group": "Token", "pages": ["onchainkit/api/get-tokens"]}, {"group": "Wallet", "pages": ["onchainkit/api/get-portfolios"]}]}, {"group": "Utilities", "pages": [{"group": "Config", "pages": ["onchainkit/config/is-base", "onchainkit/config/is-ethereum"]}, {"group": "<PERSON><PERSON><PERSON>", "pages": ["onchainkit/api/build-deposit-to-morpho-tx", "onchainkit/api/build-withdraw-from-morpho-tx", "onchainkit/hooks/use-build-deposit-to-morpho-tx", "onchainkit/hooks/use-build-withdraw-from-morpho-tx", "onchainkit/hooks/use-earn-context"]}, {"group": "Fund", "pages": ["onchainkit/fund/get-onramp-buy-url", "onchainkit/fund/fetch-onramp-config", "onchainkit/fund/fetch-onramp-quote", "onchainkit/fund/fetch-onramp-options", "onchainkit/fund/fetch-onramp-transaction-status", "onchainkit/fund/setup-onramp-event-listeners"]}, {"group": "Identity", "pages": ["onchainkit/identity/get-address", "onchainkit/identity/get-attestations", "onchainkit/identity/get-avatar", "onchainkit/identity/get-avatars", "onchainkit/identity/get-name", "onchainkit/identity/get-names", "onchainkit/identity/use-address", "onchainkit/identity/use-avatar", "onchainkit/identity/use-avatars", "onchainkit/identity/use-name", "onchainkit/identity/use-names"]}, {"group": "Mint", "pages": ["onchainkit/hooks/use-token-details", "onchainkit/hooks/use-mint-details"]}, {"group": "Token", "pages": ["onchainkit/token/format-amount"]}, {"group": "Wallet", "pages": ["onchainkit/wallet/is-valid-aa-entrypoint", "onchainkit/wallet/is-wallet-a-coinbase-smart-wallet"]}]}, {"group": "Types", "pages": ["onchainkit/api/types", "onchainkit/appchain/types", "onchainkit/checkout/types", "onchainkit/config/types", "onchainkit/earn/types", "onchainkit/fund/types", "onchainkit/identity/types", "onchainkit/mint/types", "onchainkit/signature/types", "onchainkit/swap/types", "onchainkit/token/types", "onchainkit/transaction/types", "onchainkit/wallet/types"]}, {"group": "Contribution", "pages": ["onchainkit/guides/contribution", "onchainkit/guides/reporting-bug"]}]}], "global": {"anchors": [{"anchor": "GitHub", "href": "https://github.com/coinbase/onchainkit", "icon": "github"}, {"anchor": "Playground", "href": "https://onchainkit.xyz/playground", "icon": "gamepad"}, {"anchor": "Support", "href": "https://discord.com/invite/buildonbase", "icon": "discord"}]}}, {"tab": "Cookbook", "groups": [{"group": "Use Cases", "pages": ["cookbook/onboard-any-user", "cookbook/accept-crypto-payments", "cookbook/spend-permissions-ai-agent", "cookbook/launch-ai-agents", "cookbook/launch-tokens", "cookbook/deploy-a-chain", "cookbook/onchain-social", "cookbook/defi-your-app", "cookbook/go-gasless", "cookbook/base-app-coins", "cookbook/testing-onchain-apps"]}, {"group": "Build with AI", "pages": ["cookbook/ai-prompting", "cookbook/base-builder-mcp"]}, {"group": "Vibe Code a Mini App", "pages": [{"group": "Foundations", "pages": ["cookbook/introduction-to-mini-apps", "cookbook/ai-powered-development-fundamentals", "cookbook/mastering-ai-prompt-engineering"]}, {"group": "Documentation & Reading", "pages": ["cookbook/essential-documentation-resources", "cookbook/ai-assisted-documentation-reading"]}, {"group": "Building", "pages": ["cookbook/successful-miniapps-in-tba", "cookbook/minikit/build-your-mini-app-with-prompt", "cookbook/converting-customizing-mini-apps", "cookbook/minikit/fork-and-customize"]}, {"group": "Add MiniKit to Your App", "pages": ["cookbook/minikit/install", "cookbook/minikit/add-minikit", "cookbook/minikit/configure-environment", "cookbook/minikit/manifest-cli", "cookbook/minikit/create-manifest", "cookbook/minikit/add-frame-metadata", "cookbook/minikit/test-and-deploy"]}]}]}, {"tab": "Showcase", "pages": ["showcase"]}, {"tab": "Learn", "groups": [{"group": "Building Onchain", "pages": ["learn/welcome"]}, {"group": "Onchain Concepts", "pages": ["learn/onchain-concepts/core-concepts", "learn/onchain-concepts/understanding-the-onchain-tech-stack", {"group": "Web2 vs Building Onchain", "pages": ["learn/onchain-concepts/building-onchain-wallets", "learn/onchain-concepts/building-onchain-identity", "learn/onchain-concepts/building-onchain-gas", "learn/onchain-concepts/building-onchain-nodes", "learn/onchain-concepts/building-onchain-frontend-development", "learn/onchain-concepts/building-onchain-onramps", "learn/onchain-concepts/building-onchain-social-networks", "learn/onchain-concepts/building-onchain-ai"]}, "learn/onchain-concepts/development-flow"]}, {"group": "Introduction to Ethereum", "pages": ["learn/introduction-to-ethereum/introduction-to-ethereum-vid", "learn/introduction-to-ethereum/ethereum-dev-overview-vid", "learn/introduction-to-ethereum/ethereum-applications", "learn/introduction-to-ethereum/gas-use-in-eth-transactions", "learn/introduction-to-ethereum/evm-diagram", "learn/introduction-to-ethereum/guide-to-base"]}, {"group": "Onchain App Development", "pages": [{"group": "Frontend Setup", "pages": ["learn/onchain-app-development/frontend-setup/overview", "learn/onchain-app-development/frontend-setup/building-an-onchain-app", "learn/onchain-app-development/frontend-setup/wallet-connectors", "learn/onchain-app-development/frontend-setup/introduction-to-providers", "learn/onchain-app-development/frontend-setup/viem", "learn/onchain-app-development/frontend-setup/web3"]}, {"group": "Writing to Contracts", "pages": ["learn/onchain-app-development/writing-to-contracts/useWriteContract", "learn/onchain-app-development/writing-to-contracts/useSimulateContract"]}, {"group": "Reading and Displaying Data", "pages": ["learn/onchain-app-development/reading-and-displaying-data/useReadContract", "learn/onchain-app-development/reading-and-displaying-data/useAccount", "learn/onchain-app-development/reading-and-displaying-data/configuring-useReadContract"]}, {"group": "Account Abstraction", "pages": ["learn/onchain-app-development/account-abstraction/gasless-transactions-with-paymaster", "learn/onchain-app-development/account-abstraction/account-abstraction-on-base-using-biconomy", "learn/onchain-app-development/account-abstraction/account-abstraction-on-base-using-privy-and-the-base-paymaster", "learn/onchain-app-development/account-abstraction/account-abstraction-on-base-using-particle-network"]}, {"group": "Cross-Chain Development", "pages": ["learn/onchain-app-development/cross-chain/bridge-tokens-with-layerzero", "learn/onchain-app-development/cross-chain/send-messages-and-tokens-from-base-chainlink"]}, {"group": "Finance", "pages": ["learn/onchain-app-development/finance/access-real-time-asset-data-pyth-price-feeds", "learn/onchain-app-development/finance/access-real-world-data-chainlink", "learn/onchain-app-development/finance/build-a-smart-wallet-funding-app"]}, {"group": "Deploy with Fleek", "pages": ["learn/onchain-app-development/deploy-with-fleek"]}]}, {"group": "Smart Contract Development", "pages": [{"group": "Introduction to Solidity", "pages": ["learn/introduction-to-solidity/introduction-to-solidity-overview", "learn/introduction-to-solidity/anatomy-of-a-smart-contract-vid", {"group": "Introduction to Solidity", "pages": ["learn/introduction-to-solidity/introduction-to-solidity-vid", "learn/introduction-to-solidity/solidity-overview", "learn/introduction-to-solidity/introduction-to-remix-vid", "learn/introduction-to-solidity/introduction-to-remix", "learn/introduction-to-solidity/deployment-in-remix-vid", "learn/introduction-to-solidity/deployment-in-remix"]}]}, {"group": "Contracts and Basic Functions", "pages": ["learn/contracts-and-basic-functions/intro-to-contracts-vid", "learn/contracts-and-basic-functions/hello-world-step-by-step", "learn/contracts-and-basic-functions/basic-types", "learn/contracts-and-basic-functions/basic-functions-exercise"]}, {"group": "Deploying to a Testnet", "pages": ["learn/deployment-to-testnet/overview-of-test-networks-vid", "learn/deployment-to-testnet/test-networks", "learn/deployment-to-testnet/deployment-to-base-sepolia-sbs", "learn/deployment-to-testnet/contract-verification-sbs", "learn/deployment-to-testnet/deployment-to-testnet-exercise"]}, {"group": "Control Structures", "pages": ["learn/control-structures/standard-control-structures-vid", "learn/control-structures/loops-vid", "learn/control-structures/require-revert-error-vid", "learn/control-structures/control-structures", "learn/control-structures/control-structures-exercise"]}, {"group": "Storage in Solidity", "pages": ["learn/storage/simple-storage-video", "learn/storage/simple-storage-sbs", "learn/storage/how-storage-works-video", "learn/storage/how-storage-works", "learn/storage/storage-exercise"]}, {"group": "Arrays in Solidity", "pages": ["learn/arrays/arrays-in-solidity-vid", "learn/arrays/writing-arrays-in-solidity-vid", "learn/arrays/arrays-in-solidity", "learn/arrays/filtering-an-array-sbs", "learn/arrays/fixed-size-arrays-vid", "learn/arrays/array-storage-layout-vid", "learn/arrays/arrays-exercise"]}, {"group": "The Mapping Type", "pages": ["learn/mappings/mappings-vid", "learn/mappings/using-msg-sender-vid", "learn/mappings/mappings-sbs", "learn/mappings/how-mappings-are-stored-vid", "learn/mappings/mappings-exercise"]}, {"group": "Advanced Functions", "pages": ["learn/advanced-functions/function-visibility-vid", "learn/advanced-functions/function-visibility", "learn/advanced-functions/function-modifiers-vid", "learn/advanced-functions/function-modifiers"]}, {"group": "Structs", "pages": ["learn/structs/structs-vid", "learn/structs/structs-sbs", "learn/structs/structs-exercise"]}, {"group": "Inheritance", "pages": ["learn/inheritance/inheritance-vid", "learn/inheritance/inheritance-sbs", "learn/inheritance/multiple-inheritance-vid", "learn/inheritance/multiple-inheritance", "learn/inheritance/abstract-contracts-vid", "learn/inheritance/abstract-contracts-sbs", "learn/inheritance/inheritance-exercise"]}, {"group": "Imports", "pages": ["learn/imports/imports-vid", "learn/imports/imports-sbs", "learn/imports/imports-exercise"]}, {"group": "Errors", "pages": ["learn/error-triage/error-triage-vid", "learn/error-triage/error-triage", "learn/error-triage/error-triage-exercise"]}, {"group": "The new Keyword", "pages": ["learn/new-keyword/creating-a-new-contract-vid", "learn/new-keyword/new-keyword-sbs", "learn/new-keyword/new-keyword-exercise"]}, {"group": "Contract to Contract Interactions", "pages": ["learn/interfaces/intro-to-interfaces-vid", "learn/interfaces/calling-another-contract-vid", "learn/interfaces/testing-the-interface-vid", "learn/interfaces/contract-to-contract-interaction"]}, {"group": "Events", "pages": ["learn/events/hardhat-events-sbs"]}, {"group": "Address and Payable", "pages": ["learn/address-and-payable/address-and-payable"]}]}, {"group": "Development with Foundry", "pages": ["learn/foundry/deploy-with-foundry", "learn/foundry/setup-with-base", "learn/foundry/testing-smart-contracts", "learn/foundry/verify-contract-with-basescan", "learn/foundry/generate-random-numbers-contracts"]}, {"group": "Development with Hardhat", "pages": [{"group": "Hardhat Setup and Overview", "pages": ["learn/hardhat/hardhat-setup-overview/hardhat-overview-vid", "learn/hardhat/hardhat-setup-overview/creating-a-project-vid", "learn/hardhat/hardhat-setup-overview/hardhat-setup-overview-sbs"]}, {"group": "Testing with Typescript", "pages": ["learn/hardhat/hardhat-testing/testing-overview-vid", "learn/hardhat/hardhat-testing/writing-tests-vid", "learn/hardhat/hardhat-testing/contract-abi-and-testing-vid", "learn/hardhat/hardhat-testing/hardhat-testing-sbs"]}, {"group": "Etherscan", "pages": ["learn/hardhat/etherscan/etherscan-sbs", "learn/hardhat/etherscan/etherscan-vid"]}, {"group": "Deploying Smart Contracts", "pages": ["learn/hardhat/hardhat-deploy/installing-hardhat-deploy-vid", "learn/hardhat/hardhat-deploy/setup-deploy-script-vid", "learn/hardhat/hardhat-deploy/testing-our-deployment-vid", "learn/hardhat/hardhat-deploy/test-network-configuration-vid", "learn/hardhat/hardhat-deploy/deployment-vid", "learn/hardhat/hardhat-deploy/hardhat-deploy-sbs"]}, {"group": "Verifying Smart Contracts", "pages": ["learn/hardhat/hardhat-verify/hardhat-verify-vid", "learn/hardhat/hardhat-verify/hardhat-verify-sbs"]}, {"group": "Mainnet Forking", "pages": ["learn/hardhat/hardhat-forking/mainnet-forking-vid", "learn/hardhat/hardhat-forking/hardhat-forking"]}, {"group": "Hardhat Tools and Testing", "pages": ["learn/hardhat/hardhat-tools-and-testing/overview", "learn/hardhat/hardhat-tools-and-testing/debugging-smart-contracts", "learn/hardhat/hardhat-tools-and-testing/analyzing-test-coverage", "learn/hardhat/hardhat-tools-and-testing/optimizing-gas-usage", "learn/hardhat/hardhat-tools-and-testing/reducing-contract-size", "learn/hardhat/hardhat-tools-and-testing/deploy-with-hardhat"]}]}, {"group": "Token Development", "pages": [{"group": "Introduction to <PERSON><PERSON><PERSON>", "pages": ["learn/token-development/intro-to-tokens/intro-to-tokens-vid", "learn/token-development/intro-to-tokens/misconceptions-about-tokens-vid", "learn/token-development/intro-to-tokens/tokens-overview"]}, {"group": "Minimal <PERSON>kens", "pages": ["learn/token-development/minimal-tokens/creating-a-minimal-token-vid", "learn/token-development/minimal-tokens/transferring-a-minimal-token-vid", "learn/token-development/minimal-tokens/minimal-token-sbs", "learn/token-development/minimal-tokens/minimal-tokens-exercise"]}, {"group": "ERC-20 Tokens", "pages": ["learn/token-development/erc-20-token/analyzing-erc-20-vid", "learn/token-development/erc-20-token/erc-20-standard", "learn/token-development/erc-20-token/openzeppelin-erc-20-vid", "learn/token-development/erc-20-token/erc-20-testing-vid", "learn/token-development/erc-20-token/erc-20-token-sbs", "learn/token-development/erc-20-token/erc-20-exercise"]}, {"group": "ERC-721 Tokens", "pages": ["learn/token-development/erc-721-token/erc-721-standard-video", "learn/token-development/erc-721-token/erc-721-standard", "learn/token-development/erc-721-token/erc-721-on-opensea-vid", "learn/token-development/erc-721-token/openzeppelin-erc-721-vid", "learn/token-development/erc-721-token/implementing-an-erc-721-vid", "learn/token-development/erc-721-token/erc-721-sbs", "learn/token-development/erc-721-token/erc-721-exercise"]}, {"group": "NFT Guides", "pages": ["learn/token-development/nft-guides/signature-mint", "learn/token-development/nft-guides/dynamic-nfts", "learn/token-development/nft-guides/complex-onchain-nfts", "learn/token-development/nft-guides/simple-onchain-nfts", "learn/token-development/nft-guides/thirdweb-unreal-nft-items"]}]}, {"group": "Exercise Contracts", "pages": ["learn/exercise-contracts"]}]}]}, "logo": {"light": "/logo/logo_light.svg", "dark": "/logo/logo_dark.svg"}, "navbar": {"links": [{"label": "Blog", "href": "https://blog.base.dev/"}, {"label": "GitHub", "href": "https://github.com/base"}, {"label": "Support", "href": "https://discord.com/invite/buildonbase"}, {"label": "Base.org", "href": "https://base.org"}]}, "footer": {"socials": {"x": "https://x.com/base", "github": "https://github.com/base", "reddit": "https://www.reddit.com/r/BASE/", "linkedin": "https://linkedin.com/company/coinbase"}, "links": [{"header": "Base", "items": [{"label": "Base.org", "href": "https://base.org"}, {"label": "Privacy Policy", "href": "https://docs.base.org/privacy-policy"}, {"label": "Terms of Service", "href": "https://docs.base.org/terms-of-service"}, {"label": "<PERSON><PERSON>", "href": "https://docs.base.org/cookie-policy"}]}]}, "redirects": [{"source": "/privacy-policy-2025", "destination": "/privacy-policy"}, {"source": "/", "destination": "/get-started/base"}, {"source": "/base-services-hub", "destination": "/get-started/base-services-hub"}, {"source": "/buildathons/2025-02-flash", "destination": "/get-started/base"}, {"source": "/builderkits", "destination": "/get-started/products"}, {"source": "/builderkits/minikit/debugging", "destination": "/base-app/build-with-minikit/debugging"}, {"source": "/builderkits/minikit/existing-app-integration", "destination": "/base-app/build-with-minikit/existing-app-integration"}, {"source": "/builderkits/minikit/overview", "destination": "/base-app/build-with-minikit/overview"}, {"source": "/builderkits/minikit/quickstart", "destination": "/base-app/build-with-minikit/quickstart"}, {"source": "/builderkits/minikit/thinking-social", "destination": "/base-app/guides/thinking-social"}, {"source": "/builderkits/onchainkit/:slug*", "destination": "/onchainkit/:slug*"}, {"source": "/builderkits/onchainkit/appchain/bridge", "destination": "/onchainkit/appchain/bridge"}, {"source": "/builderkits/onchainkit/buy/buy", "destination": "/onchainkit/buy/buy"}, {"source": "/builderkits/onchainkit/checkout/checkout", "destination": "/onchainkit/checkout/checkout"}, {"source": "/builderkits/onchainkit/config/onchainkit-provider", "destination": "/onchainkit/config/onchainkit-provider"}, {"source": "/builderkits/onchainkit/config/supplemental-providers", "destination": "/onchainkit/config/supplemental-providers"}, {"source": "/builderkits/onchainkit/create-a-basename-profile-component", "destination": "/onchainkit/guides/use-basename-in-onchain-app"}, {"source": "/builderkits/onchainkit/earn/earn", "destination": "/onchainkit/earn/earn"}, {"source": "/builderkits/onchainkit/fund/fund-button", "destination": "/onchainkit/fund/fund-button"}, {"source": "/builderkits/onchainkit/fund/fund-card", "destination": "/onchainkit/fund/fund-card"}, {"source": "/builderkits/onchainkit/getting-started", "destination": "/onchainkit/getting-started"}, {"source": "/builderkits/onchainkit/guides/ai-prompting-guide", "destination": "/onchainkit/guides/ai-prompting-guide"}, {"source": "/builderkits/onchainkit/guides/contribution", "destination": "/onchainkit/guides/contribution"}, {"source": "/builderkits/onchainkit/guides/lifecycle-status", "destination": "/onchainkit/guides/lifecycle-status"}, {"source": "/builderkits/onchainkit/guides/reporting-bug", "destination": "/onchainkit/guides/reporting-bug"}, {"source": "/builderkits/onchainkit/guides/tailwind", "destination": "/onchainkit/guides/tailwind"}, {"source": "/builderkits/onchainkit/guides/telemetry", "destination": "/onchainkit/guides/telemetry"}, {"source": "/builderkits/onchainkit/guides/themes", "destination": "/onchainkit/guides/themes"}, {"source": "/builderkits/onchainkit/guides/troubleshooting", "destination": "/onchainkit/guides/troubleshooting"}, {"source": "/builderkits/onchainkit/guides/use-basename-in-onchain-app", "destination": "/onchainkit/guides/use-basename-in-onchain-app"}, {"source": "/builderkits/onchainkit/guides/using-ai-powered-ides", "destination": "/onchainkit/guides/using-ai-powered-ides"}, {"source": "/builderkits/onchainkit/installation", "destination": "/onchainkit/installation/nextjs"}, {"source": "/builderkits/onchainkit/installation/astro", "destination": "/onchainkit/installation/astro"}, {"source": "/builderkits/onchainkit/installation/nextjs", "destination": "/onchainkit/installation/nextjs"}, {"source": "/builderkits/onchainkit/installation/remix", "destination": "/onchainkit/installation/remix"}, {"source": "/builderkits/onchainkit/installation/vite", "destination": "/onchainkit/installation/vite"}, {"source": "/builderkits/onchainkit/restricted", "destination": "/onchainkit/getting-started"}, {"source": "/builderkits/onchainkit/signature/signature", "destination": "/onchainkit/signature/signature"}, {"source": "/builderkits/onchainkit/transaction/transaction", "destination": "/onchainkit/transaction/transaction"}, {"source": "/builderkits/onchainkit/use-coinbase-smart-wallet-and-eoas", "destination": "/smart-wallet/quickstart"}, {"source": "/chain/account-abstraction", "destination": "/base-chain/tools/account-abstraction"}, {"source": "/chain/app-blocklist", "destination": "/base-chain/security/avoid-malicious-flags"}, {"source": "/chain/base-contracts", "destination": "/base-chain/network-information/base-contracts"}, {"source": "/chain/block-explorers", "destination": "/base-chain/tools/block-explorers"}, {"source": "/chain/bridge-an-l1-token-to-base", "destination": "/base-chain/quickstart/bridge-token"}, {"source": "/chain/bridges-mainnet", "destination": "/base-chain/network-information/bridges-mainnet"}, {"source": "/chain/builder-anniversary-nft", "destination": "/base-chain/quickstart/why-base"}, {"source": "/chain/connecting-to-base", "destination": "/base-chain/quickstart/connecting-to-base"}, {"source": "/chain/contracts", "destination": "/base-chain/network-information/ecosystem-contracts"}, {"source": "/chain/cross-chain", "destination": "/base-chain/tools/cross-chain"}, {"source": "/chain/data-indexers", "destination": "/base-chain/tools/data-indexers"}, {"source": "/chain/decentralizing-base-with-optimism", "destination": "/base-chain/quickstart/why-base"}, {"source": "/chain/deploy-on-base-quickstart", "destination": "/base-chain/quickstart/deploy-on-base"}, {"source": "/chain/differences-between-ethereum-and-base", "destination": "/base-chain/network-information/diffs-ethereum-base"}, {"source": "/chain/fees", "destination": "/base-chain/network-information/network-fees"}, {"source": "/chain/flashblocks", "destination": "/base-chain/flashblocks/apps"}, {"source": "/chain/flashblocks/apps", "destination": "/base-chain/flashblocks/apps"}, {"source": "/chain/flashblocks/node-providers", "destination": "/base-chain/flashblocks/node-providers"}, {"source": "/chain/network-faucets", "destination": "/base-chain/tools/network-faucets"}, {"source": "/chain/network-information", "destination": "/base-chain/quickstart/connecting-to-base"}, {"source": "/chain/node-performance", "destination": "/base-chain/node-operators/performance-tuning"}, {"source": "/chain/node-providers", "destination": "/base-chain/tools/node-providers"}, {"source": "/chain/node-snapshots", "destination": "/base-chain/node-operators/snapshots"}, {"source": "/chain/node-troubleshooting", "destination": "/base-chain/node-operators/troubleshooting"}, {"source": "/chain/onramps", "destination": "/base-chain/tools/onramps"}, {"source": "/chain/oracles", "destination": "/base-chain/tools/oracles"}, {"source": "/chain/registry-api", "destination": "/base-chain/tools/onchain-registry-api"}, {"source": "/chain/registry-faq", "destination": "/base-chain/tools/onchain-registry-api"}, {"source": "/chain/report", "destination": "/base-chain/security/report-vulnerability"}, {"source": "/chain/run-a-base-node", "destination": "/base-chain/node-operators/run-a-base-node"}, {"source": "/chain/security-council", "destination": "/base-chain/security/security-council"}, {"source": "/chain/security/app-blocklist", "destination": "/base-chain/security/avoid-malicious-flags"}, {"source": "/chain/security/bounty", "destination": "/base-chain/security/report-vulnerability"}, {"source": "/chain/security/report", "destination": "/base-chain/security/report-vulnerability"}, {"source": "/chain/using-base", "destination": "/base-chain/quickstart/connecting-to-base"}, {"source": "/chain/wallet", "destination": "/base-chain/tools/tokens-in-wallet"}, {"source": "/chain/why-base", "destination": "/base-chain/quickstart/why-base"}, {"source": "/cookbook/account-abstraction/account-abstraction-on-base-using-biconomy", "destination": "/learn/onchain-app-development/account-abstraction/account-abstraction-on-base-using-biconomy"}, {"source": "/cookbook/account-abstraction/account-abstraction-on-base-using-particle-network", "destination": "/learn/onchain-app-development/account-abstraction/account-abstraction-on-base-using-particle-network"}, {"source": "/cookbook/account-abstraction/account-abstraction-on-base-using-privy-and-the-base-paymaster", "destination": "/learn/onchain-app-development/account-abstraction/account-abstraction-on-base-using-privy-and-the-base-paymaster"}, {"source": "/cookbook/account-abstraction/gasless-transactions-with-paymaster", "destination": "/learn/onchain-app-development/account-abstraction/gasless-transactions-with-paymaster"}, {"source": "/cookbook/client-side-development/introduction-to-providers", "destination": "/learn/onchain-app-development/frontend-setup/introduction-to-providers"}, {"source": "/cookbook/client-side-development/viem", "destination": "/learn/onchain-app-development/frontend-setup/viem"}, {"source": "/cookbook/client-side-development/web3", "destination": "/learn/onchain-app-development/frontend-setup/web3"}, {"source": "/cookbook/cross-chain/bridge-tokens-with-layerzero", "destination": "/learn/onchain-app-development/cross-chain/bridge-tokens-with-layerzero"}, {"source": "/cookbook/cross-chain/send-messages-and-tokens-from-base-chainlink", "destination": "/learn/onchain-app-development/cross-chain/send-messages-and-tokens-from-base-chainlink"}, {"source": "/cookbook/defi/access-real-time-asset-data", "destination": "/learn/onchain-app-development/finance/access-real-time-asset-data-pyth-price-feeds"}, {"source": "/cookbook/defi/access-real-world-data", "destination": "/learn/onchain-app-development/finance/access-real-world-data-chainlink"}, {"source": "/cookbook/defi/add-in-app-funding", "destination": "/learn/onchain-app-development/finance/build-a-smart-wallet-funding-app"}, {"source": "/cookbook/growth/cast-actions", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/growth/deploy-to-vercel", "destination": "/base-app/build-with-minikit/quickstart#deploying-to-vercel"}, {"source": "/cookbook/growth/email-campaigns", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/growth/gating-and-redirects", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/growth/hyperframes", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/growth/retaining-users", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/ipfs/deploy-with-fleek", "destination": "/learn/onchain-app-development/deploy-with-fleek"}, {"source": "/cookbook/nfts/complex-onchain-nfts", "destination": "/learn/token-development/nft-guides/complex-onchain-nfts"}, {"source": "/cookbook/nfts/dynamic-nfts", "destination": "/learn/token-development/nft-guides/dynamic-nfts"}, {"source": "/cookbook/nfts/nft-minting-zora", "destination": "/learn/token-development/intro-to-tokens/intro-to-tokens-vid"}, {"source": "/cookbook/nfts/signature-mint", "destination": "/learn/token-development/nft-guides/signature-mint"}, {"source": "/cookbook/nfts/simple-onchain-nfts", "destination": "/learn/token-development/nft-guides/simple-onchain-nfts"}, {"source": "/cookbook/nfts/thirdweb-unreal-nft-items", "destination": "/learn/token-development/nft-guides/thirdweb-unreal-nft-items"}, {"source": "/cookbook/payments/build-ecommerce-app", "destination": "/onchainkit/checkout/checkout"}, {"source": "/cookbook/payments/deploy-shopify-storefront", "destination": "/learn/welcome"}, {"source": "/cookbook/payments/transaction-guide", "destination": "/cookbook/defi-your-app"}, {"source": "/cookbook/smart-contract-development/foundry/:slug*", "destination": "/learn/foundry/:slug*"}, {"source": "/cookbook/smart-contract-development/hardhat/:slug*", "destination": "/learn/hardhat/hardhat-tools-and-testing/:slug*"}, {"source": "/cookbook/smart-contract-development/remix/:slug*", "destination": "/learn/introduction-to-solidity/deployment-in-remix"}, {"source": "/cookbook/social/convert-farcaster-frame", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/social/farcaster-nft-minting-guide", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/social/farcaster-no-code-nft-minting", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/token-gating/gate-irl-events-with-nouns", "destination": "/learn/welcome"}, {"source": "/cookbook/use-case-guides/cast-actions", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/commerce/build-an-ecommerce-app", "destination": "/onchainkit/checkout/checkout"}, {"source": "/cookbook/use-case-guides/commerce/deploy-a-shopify-storefront", "destination": "/learn/welcome"}, {"source": "/cookbook/use-case-guides/create-email-campaigns", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/creator/convert-farcaster-frame-to-open-frame", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/creator/nft-minting-with-zora", "destination": "/learn/token-development/intro-to-tokens/intro-to-tokens-vid"}, {"source": "/cookbook/use-case-guides/deploy-to-vercel", "destination": "/base-app/build-with-minikit/quickstart#deploying-to-vercel"}, {"source": "/cookbook/use-case-guides/finance/access-real-time-asset-data-pyth-price-feeds", "destination": "/learn/onchain-app-development/finance/access-real-time-asset-data-pyth-price-feeds"}, {"source": "/cookbook/use-case-guides/finance/access-real-world-data-chainlink", "destination": "/learn/onchain-app-development/finance/access-real-world-data-chainlink"}, {"source": "/cookbook/use-case-guides/finance/build-a-smart-wallet-funding-app", "destination": "/learn/onchain-app-development/finance/build-a-smart-wallet-funding-app"}, {"source": "/cookbook/use-case-guides/gating-and-redirects", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/hyperframes", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/nft-minting", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/no-code-minting", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/retaining-users", "destination": "/cookbook/onchain-social"}, {"source": "/cookbook/use-case-guides/transactions", "destination": "/cookbook/defi-your-app"}, {"source": "/docs", "destination": "/get-started/base"}, {"source": "/docs/arbitration", "destination": "/arbitration"}, {"source": "/docs/network-information", "destination": "/base-chain/quickstart/connecting-to-base"}, {"source": "/docs/tools/network-faucets", "destination": "/base-chain/tools/network-faucets"}, {"source": "/feedback", "destination": "/get-started/base"}, {"source": "/guides/run-a-base-node", "destination": "/base-chain/node-operators/run-a-base-node"}, {"source": "/identity/basenames/:slug*", "destination": "/smart-wallet/basenames/:slug*"}, {"source": "/identity/smart-wallet", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/checklist", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/concepts/features/optional/spend-limits", "destination": "/smart-wallet/concepts/features/optional/spend-permissions"}, {"source": "/identity/smart-wallet/concepts/features/optional/spend-permissions", "destination": "/smart-wallet/concepts/features/optional/spend-permissions"}, {"source": "/identity/smart-wallet/concepts/usage-details/self-calls", "destination": "/smart-wallet/concepts/usage-details/unsupported-calls"}, {"source": "/identity/smart-wallet/concepts/usage-details/simulations", "destination": "/smart-wallet/concepts/usage-details/simulations"}, {"source": "/identity/smart-wallet/FAQ", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/faq/:slug*", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/features/batch-operations", "destination": "/smart-wallet/concepts/features/optional/batch-operations"}, {"source": "/identity/smart-wallet/features/custom-gas-tokens", "destination": "/smart-wallet/concepts/features/optional/custom-gas-tokens"}, {"source": "/identity/smart-wallet/features/gas-free-transactions", "destination": "/smart-wallet/concepts/features/optional/gas-free-transactions"}, {"source": "/identity/smart-wallet/features/MagicSpend", "destination": "/smart-wallet/concepts/features/built-in/MagicSpend"}, {"source": "/identity/smart-wallet/features/networks", "destination": "/smart-wallet/concepts/features/built-in/networks"}, {"source": "/identity/smart-wallet/features/passkeys", "destination": "/smart-wallet/concepts/features/built-in/passkeys"}, {"source": "/identity/smart-wallet/features/recovery-keys", "destination": "/smart-wallet/concepts/features/built-in/recovery-keys"}, {"source": "/identity/smart-wallet/features/single-sign-on", "destination": "/smart-wallet/concepts/features/built-in/single-sign-on"}, {"source": "/identity/smart-wallet/features/spend-permissions", "destination": "/smart-wallet/concepts/features/optional/spend-permissions"}, {"source": "/identity/smart-wallet/features/sub-accounts", "destination": "/smart-wallet/concepts/features/optional/sub-accounts"}, {"source": "/identity/smart-wallet/guides/create-app/:slug*", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/guides/react-native-integration", "destination": "/smart-wallet/quickstart/react-native-project"}, {"source": "/identity/smart-wallet/guides/spend-limits", "destination": "/smart-wallet/guides/spend-permissions"}, {"source": "/identity/smart-wallet/guides/spend-permissions", "destination": "/smart-wallet/guides/spend-permissions"}, {"source": "/identity/smart-wallet/guides/spend-permissions/:slug*", "destination": "/smart-wallet/guides/spend-limits/"}, {"source": "/identity/smart-wallet/guides/sub-accounts/:slug*", "destination": "/smart-wallet/guides/sub-accounts/"}, {"source": "/identity/smart-wallet/guides/sub-accounts/add-sub-accounts-to-onchainkit-minikit", "destination": "/smart-wallet/guides/sub-accounts/add-sub-accounts-to-onchainkit"}, {"source": "/identity/smart-wallet/guides/sub-accounts/creating-sub-accounts", "destination": "/smart-wallet/guides/sub-accounts/"}, {"source": "/identity/smart-wallet/guides/sub-accounts/incorporate-spend-permissions", "destination": "/smart-wallet/guides/sub-accounts/"}, {"source": "/identity/smart-wallet/guides/sub-accounts/sub-accounts-with-privy", "destination": "/smart-wallet/guides/sub-accounts/sub-accounts-with-privy"}, {"source": "/identity/smart-wallet/guides/tips/:slug*", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/guides/update-existing-app", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/index", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/introduction/:slug*", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/introduction/base-gasless-campaign", "destination": "/smart-wallet/concepts/base-gasless-campaign"}, {"source": "/identity/smart-wallet/quick-start", "destination": "/smart-wallet/quickstart"}, {"source": "/identity/smart-wallet/sdk/:slug*", "destination": "/smart-wallet/technical-reference/sdk/"}, {"source": "/identity/smart-wallet/technical-reference/sdk/sub-account-reference", "destination": "/smart-wallet/technical-reference/sub-account-reference"}, {"source": "/identity/smart-wallet/usage-details/:slug*", "destination": "/smart-wallet/concepts/usage-details/:slug*"}, {"source": "/identity/smart-wallet/wallet-library-support", "destination": "/smart-wallet/concepts/usage-details/wallet-library-support"}, {"source": "/identity/smart-wallet/why", "destination": "/smart-wallet/concepts/what-is-smart-wallet"}, {"source": "/identity/smart-wallet/:slug*", "destination": "/smart-wallet/:slug*"}, {"source": "/smart-wallet", "destination": "/base-account/quickstart/web"}, {"source": "/smart-wallet/quickstart", "destination": "/base-account/quickstart/web"}, {"source": "/smart-wallet/quickstart/quick-demo", "destination": "/base-account/quickstart/quick-demo"}, {"source": "/smart-wallet/quickstart/nextjs-project", "destination": "/base-account/quickstart/web"}, {"source": "/smart-wallet/quickstart/react-native-project", "destination": "/base-account/quickstart/mobile-integration"}, {"source": "/smart-wallet/quickstart/ai-tools-available-for-devs", "destination": "/base-account/quickstart/ai-tools-available-for-devs"}, {"source": "/smart-wallet/concepts/what-is-smart-wallet", "destination": "/base-account/overview/what-is-base-account"}, {"source": "/smart-wallet/concepts/base-gasless-campaign", "destination": "/base-account/more/base-gasless-campaign"}, {"source": "/smart-wallet/concepts/telemetry", "destination": "/base-account/more/telemetry"}, {"source": "/smart-wallet/concepts/features/built-in/:slug*", "destination": "/base-account/overview/what-is-base-account"}, {"source": "/smart-wallet/concepts/features/optional/batch-operations", "destination": "/base-account/improve-ux/batch-transactions"}, {"source": "/smart-wallet/concepts/features/optional/custom-gas-tokens", "destination": "/base-account/improve-ux/batch-transactions"}, {"source": "/smart-wallet/concepts/features/optional/gas-free-transactions", "destination": "/base-account/improve-ux/sponsor-gas/paymasters"}, {"source": "/smart-wallet/concepts/features/optional/spend-permissions", "destination": "/base-account/improve-ux/spend-permissions"}, {"source": "/smart-wallet/concepts/features/optional/sub-accounts", "destination": "/base-account/improve-ux/sub-accounts"}, {"source": "/smart-wallet/concepts/features/optional/profiles", "destination": "/base-account/reference/core/capabilities/datacallback"}, {"source": "/smart-wallet/concepts/usage-details/popups", "destination": "/base-account/more/troubleshooting/usage-details/popups"}, {"source": "/smart-wallet/concepts/usage-details/gas-usage", "destination": "/base-account/more/troubleshooting/usage-details/gas-usage"}, {"source": "/smart-wallet/concepts/usage-details/unsupported-calls", "destination": "/base-account/more/troubleshooting/usage-details/unsupported-calls"}, {"source": "/smart-wallet/concepts/usage-details/simulations", "destination": "/base-account/more/troubleshooting/usage-details/simulations"}, {"source": "/smart-wallet/concepts/usage-details/wallet-library-support", "destination": "/base-account/more/troubleshooting/usage-details/wallet-library-support"}, {"source": "/smart-wallet/concepts/usage-details/signature-verification", "destination": "/base-account/guides/authenticate-users"}, {"source": "/smart-wallet/guides/siwe", "destination": "/base-account/guides/authenticate-users"}, {"source": "/smart-wallet/guides/signing-and-verifying-messages", "destination": "/base-account/guides/authenticate-users"}, {"source": "/smart-wallet/guides/signature-verification", "destination": "/base-account/guides/authenticate-users"}, {"source": "/smart-wallet/guides/magic-spend", "destination": "/base-account/improve-ux/magic-spend"}, {"source": "/smart-wallet/guides/batch-transactions", "destination": "/base-account/improve-ux/batch-transactions"}, {"source": "/smart-wallet/guides/paymasters", "destination": "/base-account/improve-ux/sponsor-gas/paymasters"}, {"source": "/smart-wallet/guides/erc20-paymasters", "destination": "/base-account/improve-ux/sponsor-gas/erc20-paymasters"}, {"source": "/smart-wallet/guides/spend-permissions", "destination": "/base-account/improve-ux/spend-permissions"}, {"source": "/smart-wallet/guides/sub-accounts", "destination": "/base-account/improve-ux/sub-accounts"}, {"source": "/smart-wallet/guides/sub-accounts/:slug*", "destination": "/base-account/improve-ux/sub-accounts"}, {"source": "/smart-wallet/guides/profiles", "destination": "/base-account/reference/core/capabilities/datacallback"}, {"source": "/smart-wallet/technical-reference/sdk", "destination": "/base-account/reference/core/getProvider"}, {"source": "/smart-wallet/technical-reference/sdk/:slug*", "destination": "/base-account/reference/core/sdk-utilities"}, {"source": "/smart-wallet/technical-reference/sub-account-reference", "destination": "/base-account/improve-ux/sub-accounts"}, {"source": "/smart-wallet/technical-reference/spend-permissions", "destination": "/base-account/reference/onchain-contracts/spend-permissions"}, {"source": "/smart-wallet/technical-reference/profiles-reference", "destination": "/base-account/reference/core/capabilities/datacallback"}, {"source": "/smart-wallet/basenames/:slug*", "destination": "/base-account/basenames/:slug*"}, {"source": "/smart-wallet/contribute/:slug*", "destination": "/base-account/contribute/:slug*"}, {"source": "/smart-wallet/examples/:slug*", "destination": "/base-account/quickstart/web"}, {"source": "/smart-wallet/:slug*", "destination": "/base-account/:slug*"}, {"source": "/learn/account-abstraction", "destination": "/learn/onchain-app-development/account-abstraction/gasless-transactions-with-paymaster"}, {"source": "/learn/client-side-development", "destination": "/learn/onchain-app-development/frontend-setup/introduction-to-providers"}, {"source": "/learn/cross-chain-development", "destination": "/learn/onchain-app-development/cross-chain/bridge-tokens-with-layerzero"}, {"source": "/learn/deploy-with-fleek", "destination": "/learn/onchain-app-development/deploy-with-fleek"}, {"source": "/learn/development-tools/overview", "destination": "/learn/welcome"}, {"source": "/learn/erc-20-token/:slug*", "destination": "/learn/token-development/erc-20-token/:slug*"}, {"source": "/learn/erc-721-token/:slug*", "destination": "/learn/token-development/erc-721-token/:slug*"}, {"source": "/learn/ethereum-applications", "destination": "/learn/introduction-to-ethereum/ethereum-applications"}, {"source": "/learn/ethereum-dev-overview", "destination": "/learn/introduction-to-ethereum/ethereum-dev-overview-vid"}, {"source": "/learn/etherscan/:slug*", "destination": "/learn/hardhat/etherscan/:slug*"}, {"source": "/learn/evm-diagram", "destination": "/learn/introduction-to-ethereum/evm-diagram"}, {"source": "/learn/frontend-setup/:slug*", "destination": "/learn/onchain-app-development/frontend-setup/:slug*"}, {"source": "/learn/gas-use-in-eth-transactions", "destination": "/learn/introduction-to-ethereum/gas-use-in-eth-transactions"}, {"source": "/learn/guide-to-base", "destination": "/learn/introduction-to-ethereum/guide-to-base"}, {"source": "/learn/hardhat-deploy/:slug*", "destination": "/learn/hardhat/hardhat-deploy/:slug*"}, {"source": "/learn/hardhat-forking/:slug*", "destination": "/learn/hardhat/hardhat-forking/:slug*"}, {"source": "/learn/hardhat-setup-overview/:slug*", "destination": "/learn/hardhat/hardhat-setup-overview/:slug*"}, {"source": "/learn/hardhat-testing/:slug*", "destination": "/learn/hardhat/hardhat-testing/:slug*"}, {"source": "/learn/hardhat-tools-and-testing/overview", "destination": "/learn/hardhat/hardhat-tools-and-testing/overview"}, {"source": "/learn/hardhat-verify/:slug*", "destination": "/learn/hardhat/hardhat-verify/:slug*"}, {"source": "/learn/help-on-discord", "destination": "/learn/welcome"}, {"source": "/learn/intro-to-tokens/:slug*", "destination": "/learn/token-development/intro-to-tokens/:slug*"}, {"source": "/learn/introduction-to-ethereum", "destination": "/learn/introduction-to-ethereum/introduction-to-ethereum-vid"}, {"source": "/learn/learning-objectives", "destination": "/learn/welcome"}, {"source": "/learn/minimal-tokens/:slug*", "destination": "/learn/token-development/minimal-tokens/:slug*"}, {"source": "/learn/reading-and-displaying-data/:slug*", "destination": "/learn/onchain-app-development/reading-and-displaying-data/:slug*"}, {"source": "/learn/writing-to-contracts/:slug*", "destination": "/learn/onchain-app-development/writing-to-contracts/:slug*"}, {"source": "/base-learn/progress", "destination": "/learn/welcome"}, {"source": "/tutorials/intro-to-foundry-setup", "destination": "/learn/foundry/deploy-with-foundry"}, {"source": "/tutorials/hardhat-profiling-gas", "destination": "/learn/hardhat/hardhat-tools-and-testing/optimizing-gas-usage"}, {"source": "/tutorials/hardhat-profiling-size", "destination": "/learn/hardhat/hardhat-tools-and-testing/reducing-contract-size"}, {"source": "/tutorials/hardhat-debugging", "destination": "/learn/hardhat/hardhat-tools-and-testing/debugging-smart-contracts"}, {"source": "/tutorials/hardhat-test-coverage", "destination": "/learn/hardhat/hardhat-tools-and-testing/analyzing-test-coverage"}, {"source": "/tutorials/intro-to-providers", "destination": "/learn/onchain-app-development/frontend-setup/introduction-to-providers"}, {"source": "/network-information", "destination": "/base-chain/quickstart/connecting-to-base"}, {"source": "/quickstart", "destination": "/get-started/build-app"}, {"source": "/tools/network-faucets", "destination": "/base-chain/tools/network-faucets"}, {"source": "/tutorials", "destination": "/learn"}, {"source": "/tutorials/deploy-with-foundry", "destination": "/cookbook/smart-contract-development/foundry/deploy-with-foundry"}, {"source": "/tutorials/deploy-with-remix", "destination": "/cookbook/smart-contract-development/remix/deploy-with-remix"}, {"source": "/use-cases/accept-crypto-payments", "destination": "/cookbook/accept-crypto-payments"}, {"source": "/use-cases/ai-instructions/eliza", "destination": "/cookbook/launch-ai-agents"}, {"source": "/use-cases/ai-instructions/langchain-local", "destination": "/cookbook/launch-ai-agents"}, {"source": "/use-cases/ai-instructions/langchain-replit", "destination": "/cookbook/launch-ai-agents"}, {"source": "/use-cases/decentralize-social-app", "destination": "/cookbook/onchain-social"}, {"source": "/use-cases/defi-your-app", "destination": "/cookbook/defi-your-app"}, {"source": "/use-cases/go-gasless", "destination": "/cookbook/go-gasless"}, {"source": "/use-cases/launch-ai-agents", "destination": "/cookbook/launch-ai-agents"}, {"source": "/use-cases/onboard-any-user", "destination": "/cookbook/onboard-any-user"}, {"source": "/wallet-app/beta-faq", "destination": "/base-app/introduction/beta-faq"}, {"source": "/wallet-app/getting-started", "destination": "/base-app/introduction/getting-started"}, {"source": "/wallet-app/mini-apps", "destination": "/base-app/introduction/mini-apps"}, {"source": "/wallet-app/chat-agents", "destination": "/wallet-app/guides/chat-agents"}, {"source": "/base-account/framework-integrations/nextjs-with-wagmi", "destination": "/base-account/framework-integrations/wagmi/setup"}, {"source": "/base-account/framework-integrations/nextjs-with-privy", "destination": "/base-account/framework-integrations/privy/setup"}, {"source": "/wallet-app/:slug*", "destination": "/base-app/:slug*"}, {"source": "/base-app/introduction/what-are-mini-apps", "destination": "/mini-apps/overview"}, {"source": "/base-app/introduction/why-mini-apps", "destination": "/mini-apps/overview"}, {"source": "/base-app/miniapps/overview", "destination": "/mini-apps/technical-reference/minikit/overview"}, {"source": "/base-app/build-with-minikit/overview", "destination": "/mini-apps/technical-reference/minikit/overview"}, {"source": "/base-app/miniapps/existing-app-integration", "destination": "/mini-apps/quickstart/existing-apps/install"}, {"source": "/base-app/build-with-minikit/existing-app-integration", "destination": "/mini-apps/quickstart/existing-apps/install"}, {"source": "/base-app/miniapps/quickstart", "destination": "/mini-apps/quickstart/new-apps/install"}, {"source": "/base-app/build-with-minikit/quickstart", "destination": "/mini-apps/quickstart/new-apps/install"}, {"source": "/base-app/miniapps/mini-apps", "destination": "/mini-apps/overview"}, {"source": "/base-app/build-with-minikit/mini-apps", "destination": "/mini-apps/overview"}, {"source": "/base-app/miniapps/search-and-discovery", "destination": "/mini-apps/features/search-and-discovery"}, {"source": "/base-app/build-with-minikit/search-and-discovery", "destination": "/mini-apps/features/search-and-discovery"}, {"source": "/base-app/miniapps/sharing-your-miniapp", "destination": "/mini-apps/features/sharing-and-social-graph"}, {"source": "/base-app/build-with-minikit/sharing-your-miniapp", "destination": "/mini-apps/features/sharing-and-social-graph"}, {"source": "/base-app/miniapps/how-manifest-work", "destination": "/mini-apps/features/manifest"}, {"source": "/base-app/build-with-minikit/how-manifest-work", "destination": "/mini-apps/features/manifest"}, {"source": "/base-app/miniapps/thinking-social", "destination": "/mini-apps/growth/build-viral-mini-apps"}, {"source": "/base-app/build-with-minikit/thinking-social", "destination": "/mini-apps/growth/build-viral-mini-apps"}, {"source": "/base-app/miniapps/debugging", "destination": "/mini-apps/troubleshooting/common-issues"}, {"source": "/base-app/build-with-minikit/debugging", "destination": "/mini-apps/troubleshooting/common-issues"}, {"source": "/mini-apps/design-ux/best-practices", "destination": "/mini-apps/design-ux/onchain-ux"}, {"source": "/mini-apps/design-ux/onchainkit", "destination": "/mini-apps/design-ux/components"}, {"source": "/mini-apps/quickstart/new-apps/features", "destination": "/mini-apps/features/overview"}, {"source": "/mini-apps/quickstart/new-apps/install", "destination": "/mini-apps/quickstart/create-new-miniapp"}, {"source": "/mini-apps/quickstart/new-apps/deploy", "destination": "/mini-apps/quickstart/create-new-miniapp"}, {"source": "/mini-apps/quickstart/new-apps/create-manifest", "destination": "/mini-apps/quickstart/create-new-miniapp"}], "integrations": {"ga4": {"measurementId": "G-TKCM02YFWN"}}}