---
title: 'Use Cases'
---

<CardGroup cols={2}>
  <Card title="DeFi" icon="money-bill-trend-up" href="/cookbook/defi-your-app">
    Unlock the power of DeFi protocols and services directly in your app. 
  </Card>
  <Card title="Agents" icon="robot" href="/cookbook/launch-ai-agents">
    Deploy AI agents that can interact with onchain data and smart contracts.
  </Card>
  <Card title="Payments" icon="credit-card" href="/cookbook/accept-crypto-payments">
    Integrate secure and efficient crypto payment solutions for your apps.
  </Card>
  <Card title="Onboarding" icon="user" href="/cookbook/onboard-any-user">
    Let users sign up and sign in with Smart Wallet — the universal account for the onchain world.
  </Card>
  <Card title="Social" icon="heart" href="/cookbook/onchain-social">
    Use decentralized social graphs to grow your app and find users — wherever they are.
  </Card>
  <Card title="Gasless" icon="gas-pump" href="/cookbook/go-gasless">
    Enable gasless transactions and simplify user onboarding.
  </Card>
</CardGroup>
