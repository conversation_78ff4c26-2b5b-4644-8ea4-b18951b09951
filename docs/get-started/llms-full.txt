# https://docs.base.org/get-started/llms-full.txt

## Get Started — Deep Guide for LLMs

> Orientation hub for Base: what Base is, how to browse products and use cases, and quickstarts to build apps, tokens, chains, and contracts.

### What you can do here
- Understand Base’s value prop and ecosystem
- Explore products and use cases
- Follow quickstarts to build app/token/chain/contracts
- Find funding, services, and community support
- Use AI prompting resources and prompt library

## Navigation (with brief descriptions)

### Introduction
- [Base](https://docs.base.org/get-started/base.md) — Why Base, platform overview

### Browse by
- [Products](https://docs.base.org/get-started/products.md) — Product index and entry points
- [Use Cases](https://docs.base.org/get-started/use-cases.md) — Scenario‑based navigation

### Quickstart
- [Build an App](https://docs.base.org/get-started/build-app.md) — Ship an app on Base
- [Launch a Token](https://docs.base.org/get-started/launch-token.md) — Token planning and launch
- [Deploy a Chain](https://docs.base.org/get-started/deploy-chain.md) — OP Stack chain
- [Deploy Smart Contracts](https://docs.base.org/get-started/deploy-smart-contracts.md) — Contracts

### Builder Support
- [Get Funded](https://docs.base.org/get-started/get-funded.md) — Grants and programs
- [Base Services Hub](https://docs.base.org/get-started/base-services-hub.md) — Official services/tools
- [Country Leads & Ambassadors](https://docs.base.org/get-started/country-leads-and-ambassadors.md) — Community

### Build with AI
- [AI Prompting](https://docs.base.org/get-started/ai-prompting.md) — AI productivity patterns
- [Prompt Library](https://docs.base.org/get-started/prompt-library.md) — Reusable prompts

## Minimal Critical Code
None — this section is orientation and navigation. See product sections for code.


