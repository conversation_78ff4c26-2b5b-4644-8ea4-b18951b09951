# https://docs.base.org/get-started/llms.txt

## Get Started Documentation

> Start here to understand Base, browse products and use cases, and follow quickstarts to build apps, tokens, chains, and contracts.

## Introduction
- [Base](https://docs.base.org/get-started/base.md) — What Base is and why build here

## Browse by
- [Products](https://docs.base.org/get-started/products.md) — Overview of Base products with links
- [Use Cases](https://docs.base.org/get-started/use-cases.md) — Common scenarios and examples

## Quickstart
- [Build an App](https://docs.base.org/get-started/build-app.md) — End‑to‑end guide to ship your first app on Base
- [Launch a Token](https://docs.base.org/get-started/launch-token.md) — Plan and launch tokens responsibly
- [Deploy a Chain](https://docs.base.org/get-started/deploy-chain.md) — Spin up an OP Stack chain
- [Deploy Smart Contracts](https://docs.base.org/get-started/deploy-smart-contracts.md) — Contracts on Base (testnet/mainnet)

## Builder Support
- [Get Funded](https://docs.base.org/get-started/get-funded.md) — Grants, RPGF, and funding programs
- [Base Services Hub](https://docs.base.org/get-started/base-services-hub.md) — Official tools and services
- [Country Leads & Ambassadors](https://docs.base.org/get-started/country-leads-and-ambassadors.md) — Community contacts

## Build with AI
- [AI Prompting](https://docs.base.org/get-started/ai-prompting.md) — Patterns for productive AI workflows
- [Prompt Library](https://docs.base.org/get-started/prompt-library.md) — Ready‑to‑use prompts

## Optional
- [Use Cases](https://docs.base.org/get-started/use-cases.md) — Additional inspiration for builders

