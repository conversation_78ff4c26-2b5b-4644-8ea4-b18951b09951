---
title: 'Get Funded'
description: "The Base ecosystem offers multiple funding pathways designed specifically for builders at every stage—from weekend experiments to full-scale ventures."
---

## Funding Pathways

Whether you're just starting to experiment or ready to become a full-time founder, Base provides structured funding opportunities that grow with your ambitions. Each pathway is designed for different stages of your builder journey, with clear progression paths between programs.

<CardGroup cols={2}>
<Card title="Weekly Rewards" icon="calendar" href="#weekly-rewards%3A-start-building-today">
  2 ETH in weekly rewards for prototypes and experiments through Talent Protocol.
</Card>

<Card title="Builder Grants" icon="bolt" href="#builder-grants%3A-live-on-base">
  Fast, retroactive grants ranging from 1-5 ETH. Perfect for shipped projects ready to scale.
</Card>

<Card title="OP Retro Funding" icon="trophy" href="#op-retro-funding%3A-long-term-impact">
  Get rewarded for creating public goods that benefit the entire Base ecosystem.
</Card>

<Card title="Base Batches" icon="rocket" href="#base-batches%3A-the-founder-track">
  Comprehensive founder program with mentorship, resources, and significant funding opportunities.
</Card>
</CardGroup>

## Before You Apply

<Steps>
<Step title="Build on Base">
    Deploy your project to Base mainnet. Some programs will consider early prototypes and testnet deployments but being deployed on Base mainnet gives you a leg up.
    
    <Check>
    Verify your contract is deployed and functional on Base.
    </Check>
</Step>

<Step title="Document Your Work">
    Create clear documentation including setup instructions, project goals, and demo materials.
    
    <Check>
    Ensure your README includes installation steps and your project has a working demo.
    </Check>
</Step>

<Step title="Track Your Impact">
    Measure relevant metrics like user adoption, transaction volume, or community engagement.
    
    <Tip>
    Even small metrics matter. Early usage data strengthens your application significantly.
    </Tip>
</Step>
</Steps>

## Weekly Rewards: Start Building Today

The Builder Rewards Program distributes 2 ETH weekly to active builders—perfect for experimentation and learning.

[Join Builder Rewards Program](https://www.builderscore.xyz/)

### How It Works
1. Build anything on Base (prototypes welcome)
2. Share your progress on social media
3. Earn weekly rewards based on activity
4. No minimum project size required

<Check>
Prototyping counts! Early-stage projects and experiments are explicitly encouraged.
</Check>

### Perfect For
- First-time Base builders learning the ecosystem
- Developers experimenting with new ideas
- Weekend projects and hackathon submissions
- Educational content creation

<Note>
Most successful Base builders started with weekly rewards while learning. Use this as your entry point to the ecosystem.
</Note>

## Builder Grants: Live on Base

Need funding for your shipped project? Base Builder Grants offer retroactive funding for projects that demonstrate real value to the ecosystem.

[Apply for Builder Grants](https://paragraph.com/@grants.base.eth/calling-based-builders)

### What You Get
- **Grant Range:** 1-5 ETH
- **Application Type:** Retroactive (for shipped projects)


<Tip>
Already have a working prototype? You're ready to apply. Builder Grants reward shipped code over perfect pitches.
</Tip>

## OP Retro Funding: Long-term Impact

Creating tools, infrastructure, or resources that benefit everyone? OP Retro Funding recognizes and rewards public goods contributions to the Base ecosystem.

[Track Your Impact on Atlas](https://atlas.optimism.io/)

### Ideal Projects
- Open-source libraries and development frameworks
- Community tools and ecosystem infrastructure
- Analytics dashboards and ecosystem insights

### Funding Approach
- **Focus:** Public goods and ecosystem-wide impact
- **Recognition:** Both funding and ecosystem acknowledgment

### Application Process

<Steps>
<Step title="Build Public Goods">
    Create tools, content, or infrastructure that benefits the entire Base community.
    
    <Check>
    Ensure your project is open-source and publicly accessible.
    </Check>
</Step>

<Step title="Document Impact">
    Track usage metrics, community adoption, and ecosystem benefits.
    
    <Tip>
    Include testimonials from other builders who've used your tools.
    </Tip>
</Step>

<Step title="Submit Through Atlas">
    Use the Optimism Atlas platform to track contributions and apply for funding.
    
    <Check>
    Complete your Atlas profile with detailed project information.
    </Check>
</Step>
</Steps>

## Base Batches: The Founder Track

For builders ready to become founders, Base Batches provides the most comprehensive support system in the ecosystem. This recurring initiative combines structured development, mentorship, and significant funding opportunities.

[Learn More About Base Batches](https://basebatches.xyz)

### Program Structure

<Steps>
<Step title="Buildathon Phase">
    Rapid development sprint with direct access to mentorship and technical resources from the Base team.
    
    <Check>
    Complete your MVP and receive detailed technical feedback from Base core team members.
    </Check>
</Step>

<Step title="Incubator Program">
    4-week structured support to refine your product, validate your market, and develop your business model.
    
    <Check>
    Achieve product-market fit indicators and prepare comprehensive pitch materials.
    </Check>
</Step>

<Step title="Pitch Day">
    Present to top-tier investors including Coinbase Ventures and other leading crypto VCs.
    
    <Check>
    Secure follow-on funding commitments from investor network.
    </Check>
</Step>
</Steps>

### What Each Cohort Receives
- **Direct mentorship** from Base team members and industry experts
- **Access to exclusive resources** including technical infrastructure and partnerships
- **Investor introductions** to Coinbase Ventures and leading crypto funds
- **Deep ecosystem integration** with Base protocol and community

<Note>
Base Batches #001 is currently in progress. The next Base Batches will be announced in the second half of 2025.
</Note>

## Get Started Today

Building something on Base? The ecosystem is ready to support you at every stage of your journey.

<Note>
The best builders in Base started exactly where you are now. Most successful applicants begin with smaller programs and progress to larger opportunities.
</Note>

### Your Next Steps

1. **Choose your starting point** based on your current project stage
2. **Join the Base community** on Discord and social media
3. **Start building** and documenting your progress
4. **Apply to your selected program** with confidence

<Warning>
Don't wait for the "perfect" project. The Base ecosystem values builders at all stages. Start where you are and grow with the community's support.
</Warning>

Remember: Whether you're tinkering with a weekend project or building the next breakthrough application, there's a funding path designed for your journey. The Base ecosystem has invested millions in builders who started with nothing more than an idea and the determination to build.

**Take the first step today.**
