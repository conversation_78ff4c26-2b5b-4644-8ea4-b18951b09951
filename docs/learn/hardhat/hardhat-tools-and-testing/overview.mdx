---
title: 'Overview'
slug: /hardhat-tools-and-testing/overview
description: What's in this learning material.
author: <PERSON>
keywords:
  [
    Hardhat Tools,
    Smart Contract Development,
    Gas Optimization,
    Debugging,
    Test Coverage,
    Contract Size,
    Solidity,
    Base network,
    Base blockchain,
    blockchain development,
  ]
hide_table_of_contents: false
displayed_sidebar: null
---

# Overview of Hardhat Tools and Testing

This series of guides shows you how to use a number of [Hardhat plugins] that will help you more effectively build and test your smart contracts.

Learn how to keep your contracts under the 24 kiB limit, improve gas costs for your users, make sure your unit tests fully cover your code, and practice debugging.

---

## Objectives

By the end of these guides, you should be able to:

### Profiling Size

- Use Hardhat Contract Sizer plugin to profile contract size
- Describe common strategies for managing the contract size limit
- Describe the impact that inheritance has on the byte code size limit
- Describe the impact that external contracts have on the byte code size limit
- Describe the impact of using libraries has on the byte code size limit
- Describe the impact of using the Solidity optimizer

### Profiling Gas

- Use the Hardhat Gas Reporter plugin to profile gas usage
- Describe common strategies for improving the gas usage of a contract

### Debugging

- Use `console.log` to write debugging logs
- List common errors and their resolutions
- Determine if an error is a contract error or an error in the test

### Test Coverage

- Use the Solidity Coverage plugin to analyze the coverage of your test suite
- Increase the coverage of your test suite

---

## Prerequisites

### 1. Basic understanding of writing smart contracts

These guides assume that you're reasonably comfortable writing basic smart contracts. If you're just getting started, jump over to our [Base Learn] guides and start learning!

### 2. Familiarity with Hardhat

We also assume that you've got Hardhat up and running, and can write unit tests for your smart contracts. If you're not there yet, but already know Solidity, you can [setup Hardhat here].

[setup Hardhat here]: https://docs.base.org/base-learn/docs/hardhat-setup-overview/hardhat-setup-overview-sbs
[Hardhat plugins]: https://hardhat.org/hardhat-runner/plugins
[Base Learn]: https://base.org/learn
