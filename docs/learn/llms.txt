# https://docs.base.org/learn/llms.txt

## Learn Documentation

> Structured curriculum for building onchain: Ethereum fundamentals, Solidity, app development, tools, and token development.

## Building Onchain
- [Welcome](https://docs.base.org/learn/welcome.md) — Program overview

## Onchain Concepts
- [Core Concepts](https://docs.base.org/learn/onchain-concepts/core-concepts.md) — Definitions and mental models
- [Understanding the Onchain Tech Stack](https://docs.base.org/learn/onchain-concepts/understanding-the-onchain-tech-stack.md) — Layers and components
- [Web2 vs Onchain (Wallets/Identity/Gas/Nodes/Frontend/Onramps/Social/AI)](https://docs.base.org/learn/onchain-concepts/understanding-the-onchain-tech-stack.md) — Comparative guides
- [Development Flow](https://docs.base.org/learn/onchain-concepts/development-flow.md) — From idea to production

## Introduction to Ethereum
- [Introduction](https://docs.base.org/learn/introduction-to-ethereum/introduction-to-ethereum-vid.md) — Video overview
- [Developer Overview](https://docs.base.org/learn/introduction-to-ethereum/ethereum-dev-overview-vid.md) — Video guide
- [Applications](https://docs.base.org/learn/introduction-to-ethereum/ethereum-applications.md) — What’s possible
- [Gas Use](https://docs.base.org/learn/introduction-to-ethereum/gas-use-in-eth-transactions.md) — Fees
- [EVM Diagram](https://docs.base.org/learn/introduction-to-ethereum/evm-diagram.md) — Architecture
- [Guide to Base](https://docs.base.org/learn/introduction-to-ethereum/guide-to-base.md) — Base specifics

## Onchain App Development
- [Frontend Setup](https://docs.base.org/learn/onchain-app-development/frontend-setup/overview.md) — App stack overview
- [Writing to Contracts](https://docs.base.org/learn/onchain-app-development/writing-to-contracts/useWriteContract.md) — Mutations
- [Reading & Displaying Data](https://docs.base.org/learn/onchain-app-development/reading-and-displaying-data/useReadContract.md) — Queries
- [Account Abstraction](https://docs.base.org/learn/onchain-app-development/account-abstraction/gasless-transactions-with-paymaster.md) — Gasless flows
- [Cross‑Chain Development](https://docs.base.org/learn/onchain-app-development/cross-chain/bridge-tokens-with-layerzero.md) — Interop
- [Finance](https://docs.base.org/learn/onchain-app-development/finance/access-real-time-asset-data-pyth-price-feeds.md) — Data and funding flows
- [Deploy with Fleek](https://docs.base.org/learn/onchain-app-development/deploy-with-fleek.md) — Hosting

## Smart Contract Development
- [Intro to Solidity](https://docs.base.org/learn/introduction-to-solidity/introduction-to-solidity-overview.md) — Fundamentals and Remix
- [Contracts & Basic Functions](https://docs.base.org/learn/contracts-and-basic-functions/intro-to-contracts-vid.md) — Basics
- [Deploying to a Testnet](https://docs.base.org/learn/deployment-to-testnet/overview-of-test-networks-vid.md) — Testnets and verification
- [Control Structures](https://docs.base.org/learn/control-structures/standard-control-structures-vid.md) — Logic
- [Storage](https://docs.base.org/learn/storage/simple-storage-video.md) — Layout
- [Arrays](https://docs.base.org/learn/arrays/arrays-in-solidity-vid.md) — Patterns
- [Mappings](https://docs.base.org/learn/mappings/mappings-vid.md) — Key/value storage
- [Advanced Functions](https://docs.base.org/learn/advanced-functions/function-visibility-vid.md) — Modifiers and visibility
- [Structs](https://docs.base.org/learn/structs/structs-vid.md) — Data modeling
- [Inheritance](https://docs.base.org/learn/inheritance/inheritance-vid.md) — Composition
- [Imports](https://docs.base.org/learn/imports/imports-vid.md) — Reuse
- [Errors](https://docs.base.org/learn/error-triage/error-triage-vid.md) — Debugging
- [The new Keyword](https://docs.base.org/learn/new-keyword/creating-a-new-contract-vid.md) — Deploying new contracts
- [Interfaces](https://docs.base.org/learn/interfaces/intro-to-interfaces-vid.md) — Contract calls
- [Events](https://docs.base.org/learn/events/hardhat-events-sbs.md) — Logging
- [Address & Payable](https://docs.base.org/learn/address-and-payable/address-and-payable.md) — Transfers

## Development with Foundry
- [Deploy with Foundry](https://docs.base.org/learn/foundry/deploy-with-foundry.md) — Setup and deploy
- [Setup with Base](https://docs.base.org/learn/foundry/setup-with-base.md) — Base specifics
- [Testing Smart Contracts](https://docs.base.org/learn/foundry/testing-smart-contracts.md) — Test flows
- [Verify Contract with Basescan](https://docs.base.org/learn/foundry/verify-contract-with-basescan.md) — Verification
- [Random Numbers](https://docs.base.org/learn/foundry/generate-random-numbers-contracts.md) — Patterns

## Development with Hardhat
- [Setup & Overview](https://docs.base.org/learn/hardhat/hardhat-setup-overview/hardhat-setup-overview-sbs.md) — Project setup
- [Testing with TypeScript](https://docs.base.org/learn/hardhat/hardhat-testing/testing-overview-vid.md) — Tests
- [Etherscan](https://docs.base.org/learn/hardhat/etherscan/etherscan-sbs.md) — Verification
- [Hardhat Deploy](https://docs.base.org/learn/hardhat/hardhat-deploy/hardhat-deploy-sbs.md) — Deployments
- [Verifying Contracts](https://docs.base.org/learn/hardhat/hardhat-verify/hardhat-verify-vid.md) — Verify
- [Mainnet Forking](https://docs.base.org/learn/hardhat/hardhat-forking/mainnet-forking-vid.md) — Forking
- [Tools & Testing](https://docs.base.org/learn/hardhat/hardhat-tools-and-testing/overview.md) — Debug, coverage, gas, size

## Token Development
- [Intro to Tokens](https://docs.base.org/learn/token-development/intro-to-tokens/tokens-overview.md) — Token types
- [Minimal Tokens](https://docs.base.org/learn/token-development/minimal-tokens/minimal-token-sbs.md) — Minimal ERCs
- [ERC‑20 Tokens](https://docs.base.org/learn/token-development/erc-20-token/erc-20-standard.md) — Fungible tokens
- [ERC‑721 Tokens](https://docs.base.org/learn/token-development/erc-721-token/erc-721-standard.md) — NFTs
- [NFT Guides](https://docs.base.org/learn/token-development/nft-guides/signature-mint.md) — Minting and dynamic NFTs

## Optional
- [Exercise Contracts](https://docs.base.org/learn/exercise-contracts.md) — Practice repo

