---
title: "Connecting Your Frontend"
description: "Learn how onchain frontends differ from Web2 by managing wallet connections, displaying real-time onchain data, and handling transaction flows using OnchainKit, wagmi, and Viem."
---

Unlike Web2 frontends that primarily handle user input and REST API calls, onchain frontends must manage wallet connections, display onchain data in real time, and handle transaction flows. Fortunately, libraries like OnchainKit, wagmi, and Viem help you abstract away much of the complexity. Here, you'll learn how to easily support multiple wallet types, show transaction status, and seamlessly integrate onchain actions into a modern UI.

## What's Unique for Onchain Frontends?

<Info>
Onchain frontends must handle unique requirements that don't exist in traditional Web2 applications:
- Wallet connections (Coinbase Wallet,MetaMask, Smart Wallet, etc.)
- Contract calls and transaction state management
- Token and NFT interactions
</Info>

## OnchainKit

OnchainKit provides a comprehensive set of React components and TypeScript utilities for building onchain applications:

<Check>
**Wallet Connection Flows**: Supporting multiple wallet types seamlessly
</Check>

<Check>
**Common Onchain Actions**: Swapping tokens, minting NFTs, sending transactions
</Check>

<Check>
**Integration Ready**: Works with popular client libraries like [wagmi](https://wagmi.sh/react/getting-started) and [Viem](https://viem.sh/docs/getting-started), connecting to the Base Node under the hood so you don't worry about low-level RPC details
</Check>

[wagmi]: https://wagmi.sh/react/getting-started
[Viem]: https://viem.sh/docs/getting-started
