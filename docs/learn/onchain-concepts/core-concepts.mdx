---
title: "Core Concepts"
description: "Learn the unique aspects of building onchain applications with Base's tools like OnchainKit, Paymaster, and Smart Wallet to tap into global payment networks and blockchain ecosystems."
---

Building onchain means tapping into a global, fast, and cheap payment network, plus a thriving ecosystem of NFTs, games, social networks, and more. This section will introduce the **unique aspects** of building onchain and show you **how Base's tools** (e.g., OnchainKit, Paymaster, Smart Wallet, etc.) simplify that process.

If you'd like to learn more about the broader vision and benefits, check out Why Base. If you want to **start building right away**, visit our Quickstart.

We will cover the following:

- [The Onchain Tech Stack](understanding-the-onchain-tech-stack)
- [Unique aspects of building onchain (and the tools that help)](onchain-aspects)
  - [Onchain Accounts (Wallets)](building-onchain-wallets)
  - [Onchain Identity](building-onchain-identity)
  - [Paying for transactions](building-onchain-gas)
  - [Connecting your Frontend](building-onchain-frontend-development)
  - [Funding Wallets (Onramps)](building-onchain-onramps)
  - [Onchain Social Networks](building-onchain-social-networks)
  - [AI Onchain](building-onchain-ai)
- [The Onchain Development Flow](development-flow)
