---
title: "Understanding Onchain in Your Tech Stack"
description: "Learn how blockchain technology augments traditional web stacks by adding globally accessible logic and data through smart contracts and onchain state."
---

In broad strokes, a traditional web application has a frontend, a backend, and a database. When we talk about "building onchain," we're adding a **blockchain** to that tech stack as globally accessible infrastructure that can provide both **logic** (smart contracts) and **data** (onchain state).

## Traditional vs Onchain Architecture

<Tabs>
<Tab title="Traditional Stack">
- **Frontend**: Usually React, Vue, or another framework
- **Backend**: Node.js, Python, or similar, handling business logic  
- **Database**: A solution like Postgres or MongoDB for persistent storage
</Tab>

<Tab title="Onchain as an Additional Layer">
- **Smart Contracts**: Globally accessible logic
- **Onchain State**: Data that lives on Base (balances, user profiles, etc.) and is accessible to any onchain or offchain application
</Tab>
</Tabs>

<Note>
Integrating a blockchain doesn't necessarily **replace** your database or your entire backend. Instead, it augments your stack. For instance, some business logic might remain in your backend, and sensitive data may remain in a traditional database.
</Note>

## What belongs onchain?

## Onchain State

Onchain state has several notable properties:

<Info>
**Immutable**: Once recorded, data can't be deleted ensuring a permanent historical record
</Info>

<Info>
**Transparent**: All data is publicly viewable and verifiable
</Info>

<Info>
**Global**: Data is stored across multiple nodes, eliminating single points of failure
</Info>

<Warning>
**Expensive**: Storing data onchain is expensive because it must be stored by every node globally
</Warning>

Because of these properties, carefully consider what data is stored onchain. Ask yourself if the data is necessary for onchain logic or if the data should be immutable. Alternatively, you may be able to store your data offchain in a traditional database or a decentralized file storage network such as [IPFS](https://ipfs.tech/). If the state change is more informational, it may be best to emit an event, providing a more cost-effective alternative to direct onchain storage.

## Onchain Events

<Tip>
Since storing data onchain is expensive, we oftentimes use a cheaper alternative: events. Events are not part of the state, which makes them optional to store when running a node. They are a lot cheaper to use than state storage.
</Tip>

Events are triggered when an onchain method is called. Events can have different data formats. In order to make sure events follow the format we are looking to receive on the frontend, we often use data indexers. For that, you can build your own indexer or use one of the [existing tools](/base-chain/tools/data-indexers).

## Onchain Logic

Onchain logic is stored in smart contracts. Similar to onchain data, it is immutable, transparent, and global. By default, onchain logic is accessible to every network participant, but can be permissioned so that only certain participants can interact. This makes onchain logic highly composable where builders can leverage existing onchain logic and extend that logic in novel ways instead of building from scratch.

<Check>
**Capabilities**: Transfer funds and digital assets, create onchain games, create content and social posts onchain, and more. Pretty much anything you can do offchain can be done onchain.
</Check>

<Warning>
**Cost Consideration**: Because every node in the network needs to execute every transaction, each transaction has a cost dependent on the computational expense of the onchain logic being run in that transaction.
</Warning>

<Note>
If you have proprietary logic which you don't want public, this shouldn't be onchain. Or if the logic doesn't need to be decentralized and is computationally expensive, it may be better kept in a traditional backend opposed to onchain.
</Note>
