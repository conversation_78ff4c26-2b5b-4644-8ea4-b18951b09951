---
title: "Funding Wallets (Onramps)"
description: "Learn how onramps like MagicSpend and CDP Onramp solve the fiat-to-crypto conversion challenge, enabling smooth user onboarding with FundButton components."
---

Before users can transact on Base, they need crypto in their wallets. Historically, this required lengthy steps via exchanges and bank transfers. Now, onramps like MagicSpend and CDP Onramp (and the drop-in FundButton components from OnchainKit) make this process faster and smoother. In this sub-section, discover how to integrate user-friendly fiat-to-crypto solutions directly into your app, reducing churn and lowering barriers to entry.

## The Funding Challenge

<Warning>
**User Barrier**: A user can't easily transact unless they have some crypto in their wallet. Traditional workflows are clunky (bank → CEX → withdraw to wallet).
</Warning>

## Base Onramp Solutions

<Tip>
**MagicSpend** and **CDP Onramp** enable quick conversions from fiat to onchain funds without forcing users to leave your app.
</Tip>

<Check>
**Easy Integration**: Components like **FundButton** or **FundCard** in OnchainKit let you drop in funding flows directly into your user interface.
</Check>
