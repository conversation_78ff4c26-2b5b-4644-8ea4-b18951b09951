---
title: "The Development Flow"
description: "Learn the different approaches to onchain development, from client-side apps using OnchainKit to smart contract development with Hardhat and Foundry."
---

## Client-side Development

If you're focusing on the front end or building a user interface, Kits like OnchainKit provide an opinionated, model-agnostic setup for handling user authentication, contract interactions, and transaction status updates. With a client-side approach:

<Steps>
<Step title="Set up an RPC endpoint">
    Configure an RPC endpoint to read chain data and interact with the blockchain.
</Step>

<Step title="Use OnchainKit">
    Use OnchainKit (or an equivalent framework) to handle function calls, sign messages, or sponsor gas if you've integrated Paymaster solutions.
</Step>

<Step title="Incorporate Identity Solutions">
    Add Identity Solutions like Basenames or Onchain Profiles for more user-friendly experiences.
</Step>
</Steps>


## Smart Contract Development

Base is an EVM-equivalent chain, so you can build or reuse your smart contracts written in Solidity. Smart wallets, Paymaster services, and other infrastructure components on Base can significantly streamline the user experience around your onchain programs.

<Steps>
<Step title="Choose a development environment">
    Select a development environment such as Hardhat or Foundry.
</Step>

<Step title="Write and compile your contracts">
    Write and compile your contracts in Solidity.
</Step>

<Step title="Test locally">
    Test locally or on a testnet before pushing to mainnet.
</Step>

<Step title="Deploy to Base">
    Deploy to Base using your EOA wallet (or specialized deployment tooling).
</Step>
</Steps>

### Smart Contract Guides

<CardGroup cols={2}>
<Card title="Deploy on Base" href="/base-chain/quickstart/deploy-on-base">
    Quickstart: Deploy on Base
</Card>

<Card title="Foundry Tutorial" href="/learn/foundry/deploy-with-foundry">
    Foundry Tutorial
</Card>

<Card title="HardHat Development" href="/learn/hardhat/setup">
    Base Learn: Development with HardHat
</Card>

<Card title="Foundry Development" href="/cookbook/smart-contract-development/foundry/setup-with-base">
    Base Learn: Development with Foundry
</Card>

<Card title="Contract Verification" href="/cookbook/smart-contract-development/foundry/verify-contract-with-basescan">
    Verify a Smart Contract using Basescan API
</Card>
</CardGroup>

## Developer Environments

Base offers multiple environments to help you iterate quickly:

<Tabs>
<Tab title="Mainnet">
**Production Environment**
Where real-value transactions occur. Use for live applications.
</Tab>

<Tab title="Testnets">
**Base testnet on Sepolia**
Great for QA or staging. Test your applications without real value.
</Tab>

<Tab title="Local Development">
**Local EVM Environment**
You can run a local EVM environment with Hardhat or similar tools to test your contracts before deploying.
</Tab>
</Tabs>
