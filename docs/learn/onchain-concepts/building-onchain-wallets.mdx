---
title: "Onchain Accounts (Wallets)"
description: "Learn about onchain accounts, from traditional EOAs using private keys to modern Smart Wallets with passkeys, and how Base's Smart Wallet SDK simplifies integration."
---

Onchain accounts, often called "wallets", are the starting point for any onchain interaction. Unlike traditional accounts that store credentials in a database, onchain accounts authenticate and authorize transactions on the public network with credentials held by the user. The original onchain accounts (EOA's) use private keys backed up by a sequence of 24 random words. Recently, with the introduction of modern accounts like Smart Wallet, the credentials are Passkeys which provide improved security and recoverability. This sub-section covers these two main wallet types, the differences in user experience and security, and how Base's Smart Wallet SDK makes them easier to integrate.

## What's Different?  

<Info>
Every onchain action requires an account (a wallet) to sign transactions. Onchain accounts can **hold funds** and also **authorize** transactions on a blockchain.
</Info>

## Types of Onchain Accounts

### Externally Owned Accounts (EOAs)

<Check>
**Traditional Model**: Private key + seed phrase model (e.g., <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)
</Check>

<Warning>
**User Experience**: Commonly used today but can be cumbersome or risky for new users
</Warning>

<Info>
**Capabilities**: Holds digital assets but has no account level logic. Required for deploying smart contracts.
</Info>

### Smart Wallets

<Tip>
**Modern Approach**: Modern accounts with their own logic enabling improved security, recoverability, and functionality.
</Tip>

<Check>
**Enhanced Features**: Built-in recovery features, passkey authentication, and improved onboarding experience for users
</Check>

<Check>
**Advanced Permissions**: Can include features like "SpendPermissions" for fine-grained security and improved user experience
</Check>

## Learn More

If you'd like a deeper dive on EOAs vs. smart wallets, check out our [Smart Wallet docs](/smart-wallet/concepts/what-is-smart-wallet) and recommended reading on [Account Abstraction](https://www.alchemy.com/learn/account-abstraction). Base provides an [SDK](/smart-wallet/technical-reference/sdk) to help you integrate Smart Wallets easily.
