---
title: "Onchain Social Networks"
description: "Discover how onchain social networks like Farcaster enable apps to tap into existing social graphs, solving the 'empty network' problem for new social applications."
---

Traditional social media apps must build their user bases from scratch and can risk losing access to platform APIs. In contrast, onchain social networks (e.g., Farcaster) allow your app to tap into existing social graphs and user relationships immediately. This part covers how open social protocols foster collaboration, prevent centralized gatekeeping, and eliminate the "empty network" problem for new social apps.

## Open Social Layers

<Tip>
**Instant Network Effect**: Rather than building siloed social graphs from scratch, you can tap into existing onchain social networks like Farcaster.
</Tip>

<Check>
**Immediate Access**: If your user is on Farcaster, your app automatically gains access to their followers, social feed, and social connections.
</Check>

<Check>
**Solves Empty Network Problem**: This provides content and user relationships from day one, eliminating the classic "empty network" challenge.
</Check>

## Mini Apps

Mini Apps are lightweight web applications that run natively within Farcaster clients, providing rich interactive experiences without requiring users to leave their social feeds.

### What Makes Mini Apps Powerful
- **Native Integration**: Launch instantly from social posts without downloads or redirects
- **Social Context**: Automatic access to user identity, connections, and social graph
- **Onchain Capabilities**: Direct wallet connections and blockchain interactions
- **Cross-Platform**: Works in Farcaster clients like Farcaster and Coinbase Wallet


### Mini App Capabilities
**User Interactions**
- Send notifications to users who have added your app
- Access user's Farcaster identity and social connections
- Enable users to share content back to their feeds

**Onchain Integration**  
- Direct wallet connections within the social context
- Execute transactions without leaving the app
- Integration with Base blockchain and DeFi protocols

**Social Features**
- Compose and share casts from within your app
- View and interact with user profiles
- Access social graph data for personalized experiences

<Info>
**Viral Distribution**: Mini Apps spread organically through social sharing, providing built-in user acquisition as users share their experiences directly in their social feeds.
</Info>

