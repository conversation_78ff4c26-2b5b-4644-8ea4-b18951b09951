---
title: "Paying for Onchain Transactions (Gas)"
description: "Learn how Paymaster services address gas fee barriers by sponsoring transactions or allowing users to pay fees in alternative tokens, improving user onboarding on Base."
---

Transactions on blockchain networks typically require "gas" fees, which can be a major hurdle for new users without pre-funded wallets. Paymaster services address this by letting developers sponsor gas or allowing users to pay fees in tokens other than ETH. In this sub-section, we explore how Paymaster works on Base, how it improves user onboarding, and how you can implement it in your own application.

## The Challenge

<Warning>
**Gas Fee Barriers**: Users must have gas tokens (ETH on Base) to pay fees. This can be a significant hurdle for newcomers who have zero crypto.
</Warning>

## Paymaster Solution

<Tip>
Paymaster is a service that sponsors gas fees on behalf of users, enabling gasless transactions. This allows your users to transact without needing ETH in their wallet, or to pay gas in alternative tokens like USDC.
</Tip>

<Check>
Base's [Paymaster solution](https://www.coinbase.com/developer-platform/products/paymaster) (via CDP) streamlines onboarding and can significantly improve user experience.
</Check>
