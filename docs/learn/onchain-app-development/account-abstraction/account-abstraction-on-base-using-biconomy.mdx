---
title: Account Abstraction on Base using Biconomy
slug: /account-abstraction-with-biconomy
description: A tutorial that teaches how to implement Account Abstraction into a Base project using Biconomy paymasters, bundlers, and smart accounts.
author: taycaldwell
---

# Account Abstraction on Base using Biconomy

This page will guide you through the process of implementing Account Abstraction in your Base projects using Biconomy paymasters, bundlers, and smart accounts.

## Objectives

By the end of this tutorial you should be able to do the following:

- Set up a smart contract project for Base using [Foundry](https://book.getfoundry.sh/)
- Set up a Next.js frontend project using `create next-app`
- Setup user login and authentication using [Particle Network](https://particle.network/)
- Setup a [Biconomy](https://biconomy.io/) paymaster and bundler
- Create a gasless transaction

## Prerequisites

### Foundry

This tutorial requires you to have Foundry installed.

- From the command-line (terminal), run: `curl -L https://foundry.paradigm.xyz | bash`
- Then run `foundryup`, to install the latest (nightly) build of Foundry

For more information, see the Foundry Book [installation guide](https://book.getfoundry.sh/getting-started/installation).

### Coinbase Wallet

This tutorial requires you to have a wallet. You can create a wallet by downloading the Coinbase Wallet browser extension:

- Download [Coinbase Wallet](https://chrome.google.com/webstore/detail/coinbase-wallet-extension/hnfanknocfeofbddgcijnmhnfnkdnaad?hl=en)

### Wallet funds

To complete this tutorial, you will need to fund a wallet with ETH on Base Goerli.

The ETH is required for covering gas fees associated with deploying smart contracts to the network.

- To fund your wallet with ETH on Base Goerli, visit a faucet listed on the [Base Faucets](https://docs.base.org/base-chain/tools/network-faucets) page.

## What is Biconomy?

Biconomy is a toolkit that offers a full-stack solution for Account Abstraction, including smart accounts, paymasters for sponsoring gas fees, and bundlers for bundling user operations into a single transaction.

### High-level concepts

#### Account Abstraction

Account Abstraction ([ERC-4337](https://eips.ethereum.org/EIPS/eip-4337)) allows users to use Smart Contract wallets instead of traditional Externally Owned Account (EOA) wallets.

#### Smart Accounts

A smart account (also known as a smart contract wallet) is a wallet that stores and manages digital assets (ERC-20 tokens, NFTs, etc.) using a smart contract.

#### User Operations

A user operation is a pseudo-transaction object sent by a smart account that describes a transaction to be sent. Multiple user operations are eventually bundled together and initiated as a single real transaction by a bundler.

#### Paymaster

A paymaster is a special smart contract that allows applications to "sponsor user operations", meaning it will pay for the gas fees associated with the resulting transaction.

#### Bundler

A special node that monitors a mempool of user operations and bundles multiple user operations into a single transaction.

<Info>
Biconomy's Paymaster is a service that allows you to sponsor gas fees for your users, enabling gasless transactions. Learn more in the [Biconomy documentation](https://docs.biconomy.io/).
</Info>

## Creating and deploying a smart contract

Before you begin, you need to set up a smart contract development environment using Foundry.

To create a new project, first create a new directory named `myproject`, and change it to your current working directory:

```bash
mkdir myproject
cd myproject
```

### Creating a Foundry project

Next, within the `myproject` directory create a new directory named `contracts`, and change it to your current working directory:

```bash
mkdir contracts
cd contracts
```

Then create a new Foundry project by running the following command:

```bash
forge init
```

This will create a Foundry project with the following basic layout:

```bash
.
├── foundry.toml
├── script
├── src
└── test
```

<Info>
The command creates a boilerplate Solidity smart contract file named `src/Counter.sol`. This is the primary contract you will use for this tutorial.
</Info>

### Compiling the smart contract

Compile the smart contract to ensure it builds without any errors.

To compile your smart contract, run:

```bash
forge build
```

### Setting up a wallet as the deployer

Before you can deploy your smart contract to various chains you will need to set up a wallet to be used as the deployer.

To do so, you can use the [`cast wallet import`](https://book.getfoundry.sh/reference/cast/cast-wallet-import) command to import the private key of the wallet into Foundry's securely encrypted keystore:

```bash
cast wallet import deployer --interactive
```

After running the command above, you will be prompted to enter your private key, as well as a password for signing transactions.

<Warning>
For instructions on how to get your private key from Coinbase Wallet, visit the [Coinbase Wallet documentation](https://docs.cloud.coinbase.com/wallet-sdk/docs/developer-settings#show-private-key). **It is critical that you do NOT commit this to a public repo.**
</Warning>

To confirm that the wallet was imported as the `deployer` account in your Foundry project, run:

```bash
cast wallet list
```

### Deploying the smart contract

To deploy the smart contract, you can use the `forge create` command. The command requires you to specify the smart contract you want to deploy, an RPC URL of the network you want to deploy to, and the account you want to deploy with.

<Info>
Your wallet must be funded with ETH on the Base Goerli testnet to cover the gas fees associated with the smart contract deployment. Otherwise, the deployment will fail.

To get testnet ETH, see the [prerequisites](#prerequisites).
</Info>

To deploy the smart contract to the Base Goerli testnet, run the following command:

```bash
forge create ./src/Counter.sol:Counter --rpc-url https://goerli.base.org --account deployer
```

When prompted, enter the password that you set earlier, when you imported your wallet's private key.

After running the command above, the contract will be deployed on the Base Goerli test network. You can view the deployment status and contract by using a [block explorer](/base-chain/tools/block-explorers).

## Setting up the Paymaster and Bundler

To setup the paymaster and bundler for your project, you will need to visit the [Biconomy Dashboard](https://dashboard.biconomy.io/) and complete the following steps.

### Registering a paymaster

Add and register a Paymaster by completing the following steps:

1. Visit the sign in to the [Biconomy Dashboard](https://dashboard.biconomy.io/)
1. From the dashboard, select the **Paymasters** tab and click **Add your first Paymaster**
1. Provide a **Name** for your paymaster
1. Select **Base Goerli** from the **Network** dropdown
1. Click **Register**

You should now have a registered Biconomy paymaster.

<Info>
The **API Key** and **Paymaster URL** for the paymaster are provided under the **Overview** tab in the [Biconomy Dashboard](https://dashboard.biconomy.io/).
</Info>

### Setting up the paymaster gas tank

Set up and fund the paymaster's gas tank by completing the following steps:

1. From the [dashboard](https://dashboard.biconomy.io/), navigate to the **Paymasters** tab
1. Click **Setup gas tank** on the paymaster
1. Navigate to **Gas-Tank > Deposit**, and click **Set up gas tank**
1. Sign the message with your connected wallet to set up the gas tank
1. Click **Go to deposit**
1. Enter the amount of ETH you wish to deposit
1. Click **Deposit**

ETH should now be deposited into the gas tank for your paymaster. You can visit the Withdraw tab at a later time if you wish to withdraw the funds.

### Setting up the paymaster policies

Set up and fund the paymaster's gas tank by completing the following steps:

1. From the [dashboard](https://dashboard.biconomy.io/), navigate to the **Paymasters** tab
1. Select the paymaster to configure
1. Navigate to **Policies > Contracts**, and click **Add your first contract**
1. Add the **Name** and the **Smart contract address** for your contract
1. Select the **increment** and **setNumber** write methods as methods to sponsor
1. Click **Add Smart Contract**

### Setting up a bundler

1. Visit the sign in to the [Biconomy Dashboard](https://dashboard.biconomy.io/)
1. From the dashboard, select the **Bundlers** tab

<Info>
At the time of writing this tutorial, the Bundler service is still under development, however a **Bundler URL** is provided for testing out UserOperations on test networks. You can specify the chain ID **84531** to use the Bundler URL on **Base Goerli** testnet.
</Info>

## Setting up the frontend

### Creating a Next.js project

After you set up your paymaster and bundler from the Biconomy Dashboard, the next step is to create a Next.js project for your app's frontend.

From the root of the `myproject` directory of your project, create a new Next.js project by running the following command:

```bash
yarn create next-app
cd my-app
```

### Installing the dependencies

To use the paymaster and bundler that were setup from the Biconomy Dashboard, you will need to add a few dependencies to your Next.js project.

To install Biconomy as a dependency to your project, run the following command:

```bash
yarn add @biconomy/account @biconomy/bundler @biconomy/common @biconomy/core-types @biconomy/paymaster ethers@5.7.2
```

Creating Biconomy smart accounts requires a signer from an [EIP-1193](https://eips.ethereum.org/EIPS/eip-1193) provider. Biconomy works with a variety of different social login and embedded wallet onboarding solutions that provide access to a signer that can be used for creating smart accounts. In this tutorial, you will use [Particle Network](https://particle.network/) for user authentication and getting a smart account signer.

To install Particle Network as a dependency to your project, run the following command:

```bash
yarn add @biconomy/particle-auth
```

### Updating the boilerplate code

The main page (`page.tsx`) of the Next.js project created when running the `yarn create next-app` command contains a `Home` component. This component comes with a lot of code that is unnecessary for this tutorial.

Replace the content of the `page.tsx` file with the following simplified code:

```javascript
'use client';

import styles from './page.module.css';

export default function Home() {
  return (
    <main className={styles.main}>
      <div></div>
    </main>
  );
}
```

## Adding social login using Particle Network

### Setting up Particle Network

To get started adding social login into the app using Particle Network, import and initialize the Biconomy Particle Auth module in the `page.tsx` file as shown below:

```javascript
'use client';

import styles from './page.module.css';
import { ParticleAuthModule, ParticleProvider } from '@biconomy/particle-auth';

const PARTICLE_PROJECT_ID = 'YOUR_PARTICLE_PROJECT_ID';
const PARTICLE_CLIENT_ID = 'YOUR_PARTICLE_CLIENT_ID';
const PARTICLE_APP_ID = 'YOUR_PARTICLE_APP_ID';

const particle = new ParticleAuthModule.ParticleNetwork({
  projectId: PARTICLE_PROJECT_ID,
  clientKey: PARTICLE_CLIENT_ID,
  appId: PARTICLE_APP_ID,
  wallet: {
    displayWalletEntry: true,
  },
});

export default function Home() {
  return (
    <main className={styles.main}>
      <div></div>
    </main>
  );
}
```

<Info>
You will need to sign up for a Particle Network account and replace the values of `PARTICLE_PROJECT_ID`, `PARTICLE_CLIENT_ID`, and `PARTICLE_APP_ID`.
</Info>

<Warning>
If you encounter issues with Paymaster integration, ensure your API keys are correct and your contract is whitelisted in the Biconomy dashboard.
</Warning>
