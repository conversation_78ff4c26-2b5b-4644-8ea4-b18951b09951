---
title: The `useAccount` Hook
description: Learn how to access information about the connected user's wallet.
hide_table_of_contents: false
---

# The `useAccount` Hook

[wagmi] is a library that provides React hooks that trade a somewhat complex setup process for a great developer experience when building a frontend around the constraints and quirks of onchain building. One of the hooks, `useAccount`, provides access to information about your users' wallet and connection information.

You can use this for connection-status-based rendering, to enable or disable controls or views based on address, and many other useful tasks.

---

## Objectives

By the end of this guide you should be able to:

- Implement the `useAccount` hook to show the user's address, connection state, network, and balance
- Implement an `isMounted` hook to prevent hydration errors

---

## Displaying Connection Information

We'll be working from an app generated by RainbowKit's [quick start]. Either open the one you created when we were exploring [Wallet Connectors], or create a new one for this project.

Either way, change the list of chains to only include `baseSepolia` as the network option. You don't want to accidentally spend real money while developing!

You can set up your providers as described in [Introduction to Providers], or use the default from RainbowKit:

```tsx
const config = getDefaultConfig({
  appName: 'RainbowKit App',
  projectId: 'YOUR APP ID',
  chains: [baseSepolia],
  ssr: true,
});
```

Either way, be sure to set `ssr` to `true`, or you will get a [hydration error] from Next.js.

### The `useAccount` Hook

The [`useAccount`] hook allows you to access account and connection data from within any of your components.

Add a folder for `components` and a file called `ConnectionWindow.tsx` in that folder. Add the below component to the file, and replace the boilerplate text in `index.tsx` with an instance of it.

```tsx
// ConnectionWindow.tsx
export function ConnectionWindow() {
  return (
    <div>
      <p>Connection Status</p>
    </div>
  );
}
```

```tsx
// index.tsx
import { ConnectButton } from '@rainbow-me/rainbowkit';
import type { NextPage } from 'next';
import Head from 'next/head';
import styles from '../styles/Home.module.css';
import { ConnectionWindow } from '../components/ConnectionWindow';

const Home: NextPage = () => {
  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <ConnectButton />
        <ConnectionWindow />
      </main>
    </div>
  );
};

export default Home;
```

For the purposes of this exercise, open `styles/Home.module.css` and **delete or comment out** `.main`. Doing so will move the content to the top of the page, which will prevent the RainbowKit modal from blocking your ability to see changes.

Return to `ConnectionWindow.tsx` and add the `useAccount` hook to the top, where you'd add any state variables. The general pattern for wagmi hooks is you decompose the properties you want to use from a function call of the name of the hook. For some, you'll add a config object to that call, but it's not needed for this one.

```tsx
import { useAccount } from 'wagmi';

export function ConnectionWindow() {
  const { address, isConnected, isConnecting, isDisconnected } = useAccount();

  return (
    <div>
      <h2>Connection Status</h2>
    </div>
  );
}
```

You can see all the deconstructable return options in the [UseAccountReturnType]:

Update your `<div>` to show the address of the connected wallet:

```tsx
<div>
  <h2>Connection Status</h2>
  <div>
    <p>{'Address: ' + address}</p>
  </div>
</div>
```

Test it out by connecting and disconnecting with your wallet. You should see your full address when you are connected, and the address will be `undefined` when you are disconnected.

### Connection Status Conditional Rendering

It isn't very nice to display a value of `undefined` to the user, so let's use the connection status values for conditional rendering depending on whether the user is disconnected, connected, or connecting.

A common pattern is to use the conditional directly in the html return of a component or render function. For example, we could add a line to show that we're connecting as demonstrated:

```
<div>
  <h2>Connection Information</h2>
  <div>
    {!isConnecting && <p>Please click Connect in your wallet...</p>}
    <p>{"Address: " + address}</p>
  </div>
</div>
```

Connect and disconnect your wallet a few times. The `isConnecting` state is true while the _Connect to website_ wallet UI is open.

Autoconnect is enabled by default, so you'll need to clear the connection from your wallet settings to see this more than once. Otherwise, it will briefly flash as the auto-connect processes.

Use the `connected` property in the same way to only render the wallet address if there is a wallet connected. Similarly, use the `isDisconnected` property to show a message asking the user to connect.

```
<div>
  <h2>Connection Information</h2>
  <div>
    {isConnecting && <p>Please click Connect in your wallet...</p>}
    {isConnected && <p>{"Address: " + address}</p>}
    {isDisconnected && <p>Please connect your wallet to use this app.</p>}
  </div>
</div>
```

---

## Conclusion

In this guide, you've learned how the `useAccount` hook gives you access to information about the user's connection status and wallet. It can be used in any part of your app that is wrapped by the wagmi context provider. You've also learned a technique for conditional rendering based on connection status. Finally, you've learned to set the `ssr` flag to prevent hydration errors due to the client and server possessing different information about the user's connection status.

---

[RainbowKit]: https://www.rainbowkit.com/
[wagmi]: https://wagmi.sh/
[quick start]: https://www.rainbowkit.com/docs/installation/
[Wallet Connectors]: ../frontend-setup/wallet-connectors/
[`useAccount`]: https://wagmi.sh/react/hooks/useAccount
[hydration error]: https://nextjs.org/docs/messages/react-hydration-error
[Introduction to Providers]: https://docs.base.org/cookbook/client-side-development/introduction-to-providers
[UseAccountReturnType]: https://wagmi.sh/react/api/hooks/useAccount#return-type

<Steps>
<Step title="Set up RainbowKit and providers">
Change the list of chains to only include `baseSepolia` as the network option. You can set up your providers as described in [Introduction to Providers], or use the default from RainbowKit:

```tsx
const config = getDefaultConfig({
  appName: 'RainbowKit App',
  projectId: 'YOUR APP ID',
  chains: [baseSepolia],
  ssr: true,
});
```
<Note>
Be sure to set `ssr` to `true`, or you will get a [hydration error] from Next.js.
</Note>
</Step>
<Step title="Add the useAccount hook to your component">
Add the [`useAccount`] hook to the top of your component, where you'd add any state variables. The general pattern for wagmi hooks is you decompose the properties you want to use from a function call of the name of the hook. For some, you'll add a config object to that call, but it's not needed for this one.

```tsx
import { useAccount } from 'wagmi';

export function ConnectionWindow() {
  const { address, isConnected, isConnecting, isDisconnected } = useAccount();

  return (
    <div>
      <h2>Connection Status</h2>
    </div>
  );
}
```
</Step>
<Step title="Display the address of the connected wallet">
Update your `<div>` to show the address of the connected wallet:

```tsx
<div>
  <h2>Connection Status</h2>
  <div>
    <p>{'Address: ' + address}</p>
  </div>
</div>
```
Test it out by connecting and disconnecting with your wallet. You should see your full address when you are connected, and the address will be `undefined` when you are disconnected.
</Step>
<Step title="Add connection status conditional rendering">
Use the connection status values for conditional rendering depending on whether the user is disconnected, connected, or connecting.

```tsx
<div>
  <h2>Connection Information</h2>
  <div>
    {isConnecting && <p>Please click Connect in your wallet...</p>}
    {isConnected && <p>{"Address: " + address}</p>}
    {isDisconnected && <p>Please connect your wallet to use this app.</p>}
  </div>
</div>
```
</Step>
</Steps>

