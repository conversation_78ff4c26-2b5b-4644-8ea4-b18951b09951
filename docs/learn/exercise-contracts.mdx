---
title: "Exercise Contracts"
description: A list of verified unit test contracts for Base Learn exercises.
---

Many of the sections in Base Learn contain an exercise to test your knowledge on the material you have just completed. We tell you **what** to do, but not **how** to do it. You have to apply your knowledge and demonstrate the new abilities you have earned.

Upon success, you'll be granted a non-transferable, or soulbound, NFT as a memento of your learning. You can track your progress on the [progress page].

Below is a list of the exercises, with links to view their code. The unit tests are written in a bespoke framework in Solidity, but the patterns should be recognizable to most engineers.

| Exercise                                                                                                   | Code                                                                                                                                      |
| :--------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------- |
| [Deploying to a Testnet](/learn/deployment-to-testnet/deployment-to-testnet-exercise) | [0x075eB9Dc52177Aa3492E1D26f0fDE3d729625d2F](https://sepolia.basescan.org/address/0x075eb9dc52177aa3492e1d26f0fde3d729625d2f#code#F16#L1) |
| [Control Structures](/learn/control-structures/control-structures-exercise)           | [0xF4D953A3976F392aA5509612DEfF395983f22a84](https://sepolia.basescan.org/address/0xf4d953a3976f392aa5509612deff395983f22a84#code#F17#L1) |
| [Storage](/learn/storage/storage-exercise)                                            | [0x567452C6638c0D2D9778C20a3D59749FDCaa7aB3](https://sepolia.basescan.org/address/0x567452c6638c0d2d9778c20a3d59749fdcaa7ab3#code#F17#L1) |
| [Arrays](/learn/arrays/arrays-exercise)                                               | [0x5B0F80cA6f5bD60Cc3b64F0377f336B2B2A56CdF](https://sepolia.basescan.org/address/0x5b0f80ca6f5bd60cc3b64f0377f336b2b2a56cdf)             |
| [Mappings](/learn/mappings/mappings-exercise)                                         | [0xD32E3ACe3272e2037003Ca54CA7E5676f9b8D06C](https://sepolia.basescan.org/address/0xd32e3ace3272e2037003ca54ca7e5676f9b8d06c#code#F17#L1) |
| [Structs](/learn/structs/structs-exercise)                                            | [0x9eB1Fa4cD9bd29ca2C8e72217a642811c1F6176d](https://sepolia.basescan.org/address/0x9eb1fa4cd9bd29ca2c8e72217a642811c1f6176d#code#F17#L1) |
| [Inheritance](/learn/inheritance/inheritance-exercise)                                | [0xF90dA05e77a33Fe6D64bc2Df84e7dd0069A2111C](https://sepolia.basescan.org/address/0xF90dA05e77a33Fe6D64bc2Df84e7dd0069A2111C#code#F17#L1) |
| [Imports](/learn/imports/imports-exercise)                                            | [0x8dD188Ec36084D59948F90213AFCd04429E33c0c](https://sepolia.basescan.org/address/0x8dd188ec36084d59948f90213afcd04429e33c0c#code#F17#L1) |
| [Errors](/learn/error-triage/error-triage-exercise)                                   | [0xC1BD0d9A8863f2318001BC5024c7f5F58a2236F7](https://sepolia.basescan.org/address/0xc1bd0d9a8863f2318001bc5024c7f5f58a2236f7#code#F17#L1) |
| [The "new" Keyword](/learn/new-keyword/new-keyword-exercise)                          | [0x4f21e69d0CDE8C21cF82a6b37Dda5444716AFA46](https://sepolia.basescan.org/address/0x4f21e69d0cde8c21cf82a6b37dda5444716afa46#code#F17#L1) |
| [Minimal Tokens](/learn/token-development/minimal-tokens/minimal-tokens-exercise)                       | [0x10Ce928030E136EcC74d4a4416Db9b533e3c694D](https://sepolia.basescan.org/address/0x10ce928030e136ecc74d4a4416db9b533e3c694d#code#F17#L1) |
| [ERC-20 Tokens](/learn/token-development/erc-20-token/erc-20-exercise)                                  | [0x4F333c49B820013e5E6Fe86634DC4Da88039CE50](https://sepolia.basescan.org/address/0x4f333c49b820013e5e6fe86634dc4da88039ce50#code#F21#L1) |
| [ERC-721 Tokens](/learn/token-development/erc-721-token/erc-721-exercise)                               | [0x15534ED3d1dBA55148695B2Ba4164F147E47a10c](https://sepolia.basescan.org/address/0x15534ed3d1dba55148695b2ba4164f147e47a10c#code#F18#L1) |

