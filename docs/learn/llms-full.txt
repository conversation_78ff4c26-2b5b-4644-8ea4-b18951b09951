# https://docs.base.org/learn/llms-full.txt

## Learn — Deep Guide for LLMs

> A structured curriculum for onchain development: Ethereum concepts, Solidity, app development, tooling, and token development, with hands‑on videos and exercises.

### What you can do here
- Learn Ethereum and Base fundamentals
- Practice Solidity with guided lessons and exercises
- Build frontends that read and write to contracts
- Use Foundry and Hardhat for testing, deployment, and verification
- Design and ship tokens (ERC‑20/721) and NFTs

## Navigation (with brief descriptions)

### Building Onchain
- [Welcome](./welcome) — Orientation and learning path

### Onchain Concepts
- [Core Concepts](./onchain-concepts/core-concepts) — Fundamental ideas
- [Understanding the Tech Stack](./onchain-concepts/understanding-the-onchain-tech-stack) — Layers and components
- [Web2 vs Onchain (Wallets, Identity, Gas, Nodes, Frontend, Onramps, Social, AI)](./onchain-concepts/understanding-the-onchain-tech-stack) — Comparisons
- [Development Flow](./onchain-concepts/development-flow) — From idea to prod

### Introduction to Ethereum
- [Introduction](./introduction-to-ethereum/introduction-to-ethereum-vid) — Video
- [Developer Overview](./introduction-to-ethereum/ethereum-dev-overview-vid) — Video
- [Applications](./introduction-to-ethereum/ethereum-applications) — Examples
- [Gas Use](./introduction-to-ethereum/gas-use-in-eth-transactions) — Fees
- [EVM Diagram](./introduction-to-ethereum/evm-diagram) — Architecture
- [Guide to Base](./introduction-to-ethereum/guide-to-base) — Base specifics

### Onchain App Development
- [Frontend Setup](./onchain-app-development/frontend-setup/overview) — App stack
- [Writing to Contracts](./onchain-app-development/writing-to-contracts/useWriteContract) — Mutations
- [Reading & Displaying Data](./onchain-app-development/reading-and-displaying-data/useReadContract) — Queries
- [Account Abstraction](./onchain-app-development/account-abstraction/gasless-transactions-with-paymaster) — Gasless tx
- [Cross‑Chain Development](./onchain-app-development/cross-chain/bridge-tokens-with-layerzero) — Interop
- [Finance](./onchain-app-development/finance/access-real-time-asset-data-pyth-price-feeds) — Data & funding
- [Deploy with Fleek](./onchain-app-development/deploy-with-fleek) — Hosting

### Smart Contract Development
- [Intro to Solidity](./introduction-to-solidity/introduction-to-solidity-overview) — Overview
- [Contracts & Basic Functions](./contracts-and-basic-functions/intro-to-contracts-vid) — Basics
- [Deploying to a Testnet](./deployment-to-testnet/overview-of-test-networks-vid) — Testnets/verify
- [Control Structures](./control-structures/standard-control-structures-vid) — Logic
- [Storage](./storage/simple-storage-video) — Layout
- [Arrays](./arrays/arrays-in-solidity-vid) — Arrays
- [Mappings](./mappings/mappings-vid) — Mappings
- [Advanced Functions](./advanced-functions/function-visibility-vid) — Visibility/modifiers
- [Structs](./structs/structs-vid) — Data types
- [Inheritance](./inheritance/inheritance-vid) — Inheritance
- [Imports](./imports/imports-vid) — Imports
- [Errors](./error-triage/error-triage-vid) — Debugging
- [The new Keyword](./new-keyword/creating-a-new-contract-vid) — new/deploy
- [Interfaces](./interfaces/intro-to-interfaces-vid) — Calls
- [Events](./events/hardhat-events-sbs) — Logging
- [Address & Payable](./address-and-payable/address-and-payable) — Transfers

### Development with Foundry
- [Deploy with Foundry](./foundry/deploy-with-foundry) — Setup/deploy
- [Setup with Base](./foundry/setup-with-base) — Base specifics
- [Testing Smart Contracts](./foundry/testing-smart-contracts) — Tests
- [Verify with Basescan](./foundry/verify-contract-with-basescan) — Verify
- [Random Numbers](./foundry/generate-random-numbers-contracts) — RNG

### Development with Hardhat
- [Setup & Overview](./hardhat/hardhat-setup-overview/hardhat-setup-overview-sbs) — Setup
- [Testing with TypeScript](./hardhat/hardhat-testing/testing-overview-vid) — Tests
- [Etherscan](./hardhat/etherscan/etherscan-sbs) — Verify
- [Hardhat Deploy](./hardhat/hardhat-deploy/hardhat-deploy-sbs) — Deploy
- [Verifying Contracts](./hardhat/hardhat-verify/hardhat-verify-vid) — Verify
- [Mainnet Forking](./hardhat/hardhat-forking/mainnet-forking-vid) — Forking
- [Tools & Testing](./hardhat/hardhat-tools-and-testing/overview) — Debug/coverage/gas/size

### Token Development
- [Intro to Tokens](./token-development/intro-to-tokens/tokens-overview) — Tokens
- [Minimal Tokens](./token-development/minimal-tokens/minimal-token-sbs) — Minimal ERCs
- [ERC‑20](./token-development/erc-20-token/erc-20-standard) — Fungible tokens
- [ERC‑721](./token-development/erc-721-token/erc-721-standard) — NFTs
- [NFT Guides](./token-development/nft-guides/signature-mint) — Minting/dynamic

### Exercise Contracts
- [Exercises](./exercise-contracts) — Practice contracts

## Minimal Critical Code
None — learning content. See product sections for runnable code.


