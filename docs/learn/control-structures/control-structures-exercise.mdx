---
title: Control Structures Exercise
sidebarTitle: Exercise
description: Exercise - Demonstrate your knowledge of control structures.
hide_table_of_contents: false
---

Create a contract that adheres to the following specifications:

---

## Contract

Create a single contract called `ControlStructures`. It should not inherit from any other contracts and does not need a constructor. It should have the following functions:

### Smart Contract FizzBuzz

Create a function called `fizzBuzz` that accepts a `uint` called `_number` and returns a `string memory`. The function should return:

- "Fizz" if the `_number` is divisible by 3
- "Buzz" if the `_number` is divisible by 5
- "FizzBuzz" if the `_number` is divisible by 3 and 5
- "Splat" if none of the above conditions are true

### Do Not Disturb

Create a function called `doNotDisturb` that accepts a `uint` called `_time`, and returns a `string memory`. It should adhere to the following properties:

- If `_time` is greater than or equal to 2400, trigger a `panic`
- If `_time` is greater than 2200 or less than 800, `revert` with a custom error of `AfterHours`, and include the time provided
- If `_time` is between `1200` and `1259`, `revert` with a string message "At lunch!"
- If `_time` is between 800 and 1199, return "Morning!"
- If `_time` is between 1300 and 1799, return "Afternoon!"
- If `_time` is between 1800 and 2200, return "Evening!"

---

### Submit your Contract and Earn an NFT Badge! (BETA)

<Note>
#### Hey, where'd my NFT go!?

[Testnets](/learn/deployment-to-testnet/test-networks) are not permanent! Base Goerli [will soon be sunset](https://base.mirror.xyz/kkz1-KFdUwl0n23PdyBRtnFewvO48_m-fZNzPMJehM4), in favor of Base Sepolia.

As these are separate networks with separate data, your NFTs **will not** transfer over.

**Don't worry!** We've captured the addresses of all NFT owners on Base Goerli and will include them when we release the mechanism to transfer these NFTs to mainnet later this year! You can also redeploy on Sepolia and resubmit if you'd like!
</Note>


{/* <CafeUnitTest nftNum={2}/> */}

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=components-cafeunittest--two&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
