---
title: 'Foundry: Setting up Foundry with Base'
slug: /intro-to-foundry-setup
description: A tutorial that teaches how to set up your development environment to work with Foundry.
author: <PERSON><PERSON>
---

# Setting up Foundry with Base

In this tutorial, you'll learn how to set up [Foundry], a toolchain for smart contract development. You'll also learn how to configure it to work with Base.

## Objectives

By the end of this tutorial, you should be able to:

- Install Foundry
- Create a Foundry project
- Compile a smart contract using Foundry
- Configure Foundry to work with Base

## Overview

Foundry is a smart contract development toolchain that is composed of multiple small command line tools:

- _[forge]_: Compile, test, and deploy your smart contracts
- _[cast]_: Interact with the Blockchain over RPC. You can make smart contract calls, send transactions, or retrieve any type of chain data
- _[chisel]_: A Solidity REPL. You can write Solidity code directly
- _[anvil]_: A local Blockchain node for testing and development

Using Foundry you can manage your dependencies, compile your project, run tests, deploy smart contracts and interact with the chain from the command-line and via Solidity scripts.

For a deep dive on the Foundry features and full capabilities, check out the [Foundry Book].

## Installing Foundry

In order to install Foundry, you can use `Foundry<PERSON>`, the Foundry's toolchain installer.

To install `Foundryup` you have to run in the terminal:

```bash
$ curl -L https://foundry.paradigm.xyz | bash
```

After `Foundryup` is installed, you can install `Foundry` by running:

```bash
$ foundryup
```

You can verify the installation by trying the following commands:

```bash
$ forge --version
$ cast --version
$ chisel --version
$ anvil --version
```

## My First Foundry Project

To create a foundry project you can simply run:

```bash filename="terminal"
$ forge init hello_foundry_in_base
```

This will create a foundry project with the following structure:

```text filename="project-structure"
├── lib          # all the libraries installed
├── script       # scripts folder, e.g., deploy scripts
├── src          # smart contracts folder
├── test         # tests folder
└── foundry.toml # foundry configuration file
```

You will also notice a `.gitmodules` file -- this is because `Foundry` handles dependencies using [Git submodules].

By default the Foundry structure stores smart contracts in the `src` folder. You can change this in the `foundry.toml` configuration file.

For instance:

```toml filename="foundry.toml"
[profile.default]
src = 'contracts'
```

In order to compile the project, simply run:

```bash filename="terminal"
forge build
```

## Setting up Foundry with Base

In order to work with Base, you need to configure a couple of settings in the configuration `foundry.toml` file.

The first thing is the Solidity version.

You need to configure your config file as follows:

```toml filename="foundry.toml"
[profile.default]
src = 'src'
out = 'out'
libs = ['lib']
solc_version = "0.8.23"
```

Be sure that you modify the pragma of your contracts and simply run `forge build` to ensure everything works well.

We also recommend setting up JSON RPC endpoints for Base and the API key for [Basescan] in the configuration file so that your environment is ready to deploy your smart contracts.

Your configuration file should look like the following:

```toml filename="foundry.toml"
[profile.default]
src = "src"
out = "out"
libs = ["lib"]
solc_version = "0.8.23"

[rpc_endpoints]
base = "https://mainnet.base.org"
baseSepolia = "https://sepolia.base.org"

[etherscan]
baseSepolia = { key = "${BASESCAN_API_KEY}", url = "https://api-sepolia.basescan.org/api" }
base = { key = "${BASESCAN_API_KEY}", url = "https://api.basescan.org/api" }
```

We included 2 JSON RPC endpoints for `Base` and `Base Sepolia` and similar for the Etherscan section, we included the configuration for `Basescan` for Sepolia and Mainnet. Both rely on the same API Key, `BASESCAN_API_KEY`.

## Conclusion

In this tutorial, you've embarked on the journey of smart contract development with Base and Foundry. You've learned the essential steps, from installing Foundry using the convenient `Foundryup` toolchain installer to creating your first project and configuring Foundry to seamlessly integrate with Base.

[Foundry]: https://github.com/foundry-rs/foundry
[Foundry Book]: https://book.getfoundry.sh/
[chisel]: https://book.getfoundry.sh/chisel/
[cast]: https://book.getfoundry.sh/cast/
[anvil]: https://book.getfoundry.sh/anvil/
[forge]: https://book.getfoundry.sh/forge/
[Git submodules]: https://git-scm.com/book/en/v2/Git-Tools-Submodules
[OP Stack]: https://stack.optimism.io/
[Differences between Ethereum and Base]: https://docs.base.org/differences/
[Basescan]: https://basescan.org/

