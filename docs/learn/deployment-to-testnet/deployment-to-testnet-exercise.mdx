---
title: 'Deployment Exercise'
sidebarTitle: 'Exercise'
description: Exercise - Deploy your basic math contract and earn an NFT.
hide_table_of_contents: false
---

You've already built and deployed your [Basic Math](/learn/contracts-and-basic-functions/basic-functions-exercise) contract for this exercise. Now it's time to submit the address and earn an NFT pin to commemorate your accomplishment!

<Warning>
We're currently in beta, so you'll only need to pay testnet funds to submit your contract, but this means you'll be getting a testnet NFT.

Stay tuned for updates!
</Warning>


### Submit your Contract and Earn an NFT Badge! (BETA)

<Note>
#### Hey, where'd my NFT go!?

[Testnets](/learn/deployment-to-testnet/test-networks) are not permanent! Base Goerli [will soon be sunset](https://base.mirror.xyz/kkz1-KFdUwl0n23PdyBRtnFewvO48_m-fZNzPMJehM4), in favor of Base Sepolia.

As these are separate networks with separate data, your NFTs **will not** transfer over.

**Don't worry!** We've captured the addresses of all NFT owners on Base Goerli and will include them when we release the mechanism to transfer these NFTs to mainnet later this year! You can also redeploy on Sepolia and resubmit if you'd like!
</Note>


{/* <CafeUnitTest nftNum={1}/> */}

<iframe
  src="https://684b5e62b1ff46bc5bf83966-aijszlfakk.chromatic.com/iframe.html?args=&id=components-cafeunittest--one&viewMode=story&dark=true&hero=true"
  width="100%"
  height="auto"
/>
