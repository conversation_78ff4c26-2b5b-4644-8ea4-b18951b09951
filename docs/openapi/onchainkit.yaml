openapi: 3.0.0
info:
  title: OnchainKit API
  version: '1.0.0'
  description: API reference for OnchainKit functionality


components:
  parameters:
    ContractAddressParam:
      name: contractAddress
      in: query
      required: true
      schema:
        type: string
      description: The NFT contract address
      example: "0x123..."
    TakerAddressParam:
      name: takerAddress
      in: query
      required: true
      schema:
        type: string
      description: The address of the NFT recipient
    TokenIdParam:
      name: tokenId
      in: query
      required: true
      schema:
        type: string
      description: The token ID of the NFT
      example: "1"
    LimitParam:
      name: limit
      in: query
      required: false
      schema:
        type: string
      description: Maximum number of tokens to return
    SearchParam:
      name: search
      in: query
      required: false
      schema:
        type: string
      description: Substring to filter token names or symbols

  schemas:
    GetTokenDetailsParams:
      type: object
      required:
        - contractAddress
        - tokenId
      properties:
        contractAddress:
          type: string
          description: The NFT contract address
          example: "0x123..."
        tokenId:
          type: string
          description: The token ID of the NFT
          example: "1"
    
    GetTokenDetailsResponse:
      type: object
      properties:
        collectionName:
          type: string
          description: NFT Collection Name
        collectionDescription:
          type: string
          description: NFT Collection Description
        name:
          type: string
          description: NFT Name
        description:
          type: string
          description: NFT Description
        imageUrl:
          type: string
          format: uri
          description: URL of the NFT image
        animationUrl:
          type: string
          format: uri
          description: URL of the NFT animation (if any)
        ownerAddress:
          type: string
          description: Current owner's wallet address
        lastSoldPrice:
          type: object
          description: Details of the last sale
          properties:
            amount:
              type: string
              description: Amount paid in native currency
            currency:
              type: string
              description: Currency symbol (e.g., ETH)
            amountUSD:
              type: string
              description: Equivalent USD value at time of sale
        mimeType:
          type: string
          description: MIME type of the NFT asset
        contractType:
          type: string
          description: Token standard (e.g., ERC721)

    GetMintDetailsParams:
      type: object
      required:
        - contractAddress
        - takerAddress
        - tokenId
      properties:
        contractAddress:
          type: string
          description: The NFT contract address
          example: "0x123..."
        takerAddress:
          type: string
          description: The address of the NFT recipient
          example: "0x456..."
        tokenId:
          type: string
          description: The token ID of the NFT to be minted
          example: "1"
    
    GetMintDetailsResponse:
      type: object
      properties:
        name:
          type: string
          description: NFT Name
        description:
          type: string
          description: NFT description
        imageUrl:
          type: string
          format: uri
          description: URL of the NFT image
        animationUrl:
          type: string
          format: uri
          description: URL of the NFT animation (if any)
        mimeType:
          type: string
          description: MIME type of the NFT asset
        contractType:
          type: string
          description: Token standard (e.g., ERC721)
        price:
          type: object
          description: Current price details
          properties:
            amount:
              type: string
              description: Amount paid in native currency
            currency:
              type: string
              description: Currency symbol (e.g., ETH)
            amountUSD:
              type: string
              description: Equivalent USD value
        mintFee:
          type: object
          description: Fee required to mint this NFT
          properties:
            amount:
              type: string
              description: Fee amount in native currency
            currency:
              type: string
              description: Currency symbol for the fee
            amountUSD:
              type: string
              description: Equivalent USD value of the fee
        maxMintsPerWallet:
          type: integer
          description: Maximum number of mints allowed per wallet
        isEligibleToMint:
          type: boolean
          description: Indicates whether the caller is eligible to mint
        creatorAddress:
          type: string
          description: Address of the NFT creator
        network:
          type: string
          description: Network on which the NFT exists
        totalTokens:
          type: string
          description: Total number of tokens in the collection
        totalOwners:
          type: string
          description: Total number of unique owners of tokens in the collection

    BuildMintTransactionParams:
      type: object
      required:
        - mintAddress
        - takerAddress
        - tokenId
        - quantity
        - network
      properties:
        mintAddress:
          type: string
          description: The NFT contract address to mint from
          example: "0x123..."
        takerAddress:
          type: string
          description: The address of the NFT recipient
          example: "0x456..."
        tokenId:
          type: string
          description: The token ID of the NFT to be minted
          example: "1"
        quantity:
          type: integer
          description: The number of NFTs to mint
          example: 1
        network:
          type: string
          description: The network to mint on
          example: "networks/base-mainnet"
    
    BuildMintTransactionResponse:
      type: object
      properties:
        call_data:
          type: object
          properties:
            from:
              type: string
              description: Address sending the mint transaction
            to:
              type: string
              description: Contract address to receive the mint transaction
            data:
              type: string
              description: ABI‐encoded calldata for the mint transaction
            value:
              type: string
              description: Amount of native currency (in hexadecimal) to include

    Token:
      type: object
      properties:
        name:
          type: string
          description: Token name (e.g., "ETH")
        address:
          type: string
          description: Contract address of the token
        symbol:
          type: string
          description: Token symbol (e.g., "ETH")
        decimals:
          type: integer
          description: Number of decimals for the token
        image:
          type: string
          format: uri
          description: URL to the token’s image
        chainId:
          type: integer
          description: Chain ID where the token resides
      required:
        - name
        - address
        - symbol
        - decimals
        - image
        - chainId

    BuildSwapTransactionRequest:
      type: object
      properties:
        fromAddress:
          type: string
          description: Wallet address initiating the swap
        from:
          $ref: '#/components/schemas/Token'
        to:
          $ref: '#/components/schemas/Token'
        amount:
          type: string
          description: Amount of the “from” token to swap (in smallest unit or decimal string)
        useAggregator:
          type: boolean
          description: Whether to use aggregator routing
      required:
        - fromAddress
        - from
        - to
        - amount
        - useAggregator
        
    BuildSwapTransactionResponse:
      type: object
      properties:
        approveTransaction:
          type: object
          properties:
            chainId:
              type: integer
              description: Chain ID for the approval transaction
            data:
              type: string
              description: ABI‐encoded calldata for the approval
            gas:
              type: integer
              description: Gas limit for the approval transaction
            to:
              type: string
              description: Contract address to which the approval is sent
            value:
              type: integer
              description: Amount of native currency to send (in wei)
        fee:
          type: object
          properties:
            baseAsset:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the fee asset
                address:
                  type: string
                  description: Contract address of the fee asset
                currencyCode:
                  type: string
                  description: Symbol of the fee asset
                decimals:
                  type: integer
                  description: Number of decimals for the fee asset
                imageURL:
                  type: string
                  format: uri
                  description: URL for the fee asset’s image
                blockchain:
                  type: string
                  description: Underlying blockchain identifier (e.g., “eth”)
                aggregators:
                  type: array
                  description: List of aggregator identifiers or objects
                  items:
                    type: object
                    description: Aggregator details (varies by integration)
                    additionalProperties: true
                swappable:
                  type: boolean
                  description: Whether this asset can be swapped
                unverified:
                  type: boolean
                  description: Indicates if the asset is unverified
                chainId:
                  type: integer
                  description: Chain ID where the fee asset resides
            percentage:
              type: string
              description: Fee percentage (as a string, e.g., “1” for 1%)
            amount:
              type: string
              description: Fee amount in native currency (in smallest unit)
        quote:
          type: object
          properties:
            from:
              type: object
              properties:
                address:
                  type: string
                  description: Contract address of the “from” token
                chainId:
                  type: integer
                  description: Chain ID for the “from” token
                decimals:
                  type: integer
                  description: Decimal precision of the “from” token
                image:
                  type: string
                  format: uri
                  description: Image URL of the “from” token
                name:
                  type: string
                  description: Name of the “from” token
                symbol:
                  type: string
                  description: Symbol of the “from” token
            to:
              type: object
              properties:
                address:
                  type: string
                  description: Contract address of the “to” token
                chainId:
                  type: integer
                  description: Chain ID for the “to” token
                decimals:
                  type: integer
                  description: Decimal precision of the “to” token
                image:
                  type: string
                  format: uri
                  description: Image URL of the “to” token
                name:
                  type: string
                  description: Name of the “to” token
                symbol:
                  type: string
                  description: Symbol of the “to” token
            fromAmount:
              type: string
              description: Amount of “from” token (in smallest unit)
            toAmount:
              type: string
              description: Amount of “to” token (in smallest unit)
            amountReference:
              type: string
              description: Indicates which amount is the reference (e.g., “from”)
            priceImpact:
              type: string
              description: Estimated price impact (as a string)
            chainId:
              type: integer
              description: Chain ID for this quote
            highPriceImpact:
              type: boolean
              description: Whether the price impact is considered high
            slippage:
              type: string
              description: Allowed slippage percentage (as a string)
            warning:
              type: object
              properties:
                type:
                  type: string
                  description: Type of warning (e.g., “warning”)
                message:
                  type: string
                  description: Short warning message
                description:
                  type: string
                  description: Detailed warning description
        transaction:
          type: object
          properties:
            chainId:
              type: integer
              description: Chain ID for the swap transaction
            data:
              type: string
              description: ABI‐encoded calldata for the swap
            gas:
              type: integer
              description: Gas limit for the swap transaction
            to:
              type: string
              description: Contract address to which the swap is sent
            value:
              type: integer
              description: Amount of native currency to send (in wei)
        warning:
          type: object
          properties:
            type:
              type: string
              description: Type of warning (e.g., “warning”)
            message:
              type: string
              description: Short warning message
            description:
              type: string
              description: Detailed warning description

    GetSwapQuoteRequest:
      type: object
      properties:
        from:
          $ref: '#/components/schemas/Token'
        to:
          $ref: '#/components/schemas/Token'
        amount:
          type: string
          description: Amount of the “from” token to quote (in smallest unit or decimal string)
        useAggregator:
          type: boolean
          description: Whether to use aggregator routing
      required:
        - from
        - to
        - amount
        - useAggregator

    GetSwapQuoteResponse:
      type: object
      properties:
        amountReference:
          type: string
          description: Indicates which side is the reference for amounts (e.g., “from”)
        chainId:
          type: integer
          description: Chain ID for this quote
        from:
          $ref: '#/components/schemas/Token'
        to:
          $ref: '#/components/schemas/Token'
        fromAmount:
          type: string
          description: Amount of “from” token (in smallest unit)
        fromAmountUSD:
          type: string
          description: USD equivalent of the “from” amount
        toAmount:
          type: string
          description: Amount of “to” token (in smallest unit)
        toAmountUSD:
          type: string
          description: USD equivalent of the “to” amount
        priceImpact:
          type: string
          description: Estimated price impact (as a string)
        highPriceImpact:
          type: boolean
          description: Whether the price impact is considered high
        slippage:
          type: string
          description: Allowed slippage percentage (as a string)

    GetPortfoliosRequest:
      type: object
      properties:
        addresses:
          type: array
          description: List of wallet addresses to retrieve portfolios for
          items:
            type: string
      required:
        - addresses
    
    TokenBalance:
      type: object
      properties:
        address:
          type: string
          description: Contract address of the token
        chainId:
          type: integer
          description: Chain ID where the token resides
        decimals:
          type: integer
          description: Number of decimals for the token
        image:
          type: string
          format: uri
          description: URL to the token’s image
        name:
          type: string
          description: Token name
        symbol:
          type: string
          description: Token symbol
        cryptoBalance:
          type: number
          description: Token balance in its native units
        fiatBalance:
          type: number
          description: Token balance converted to USD

    Portfolio:
      type: object
      properties:
        address:
          type: string
          description: Wallet address for this portfolio
        portfolioBalanceInUsd:
          type: number
          description: Total portfolio value in USD
        tokenBalances:
          type: array
          description: List of token balances in this portfolio
          items:
            $ref: '#/components/schemas/TokenBalance'

    GetPortfoliosResponse:
      type: object
      properties:
        portfolios:
          type: array
          description: Array of portfolio objects
          items:
            $ref: '#/components/schemas/Portfolio'

paths:
  /getTokenDetails:
    get:
      summary: GetTokenDetails
      x-code-samples:
        - lang: JavaScript
          label: JavaScript
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { getTokenDetails } from '@coinbase/onchainkit/api';

            const response = await getTokenDetails({
              contractAddress: '0x...',
              tokenId: '1',
            });
      operationId: get-token-details
      tags:
        - Mint
      parameters:
        - $ref: '#/components/parameters/ContractAddressParam'
        - $ref: '#/components/parameters/TokenIdParam'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTokenDetailsResponse'
              example:
                collectionName: "NFT Collection Name"
                collectionDescription: "NFT Collection Description"
                name: "NFT Name"
                description: "NFT Description"
                imageUrl: "https://example.com/image.png"
                animationUrl: ""
                ownerAddress: "0x..."
                lastSoldPrice:
                  amount: "0.0001"
                  currency: "ETH"
                  amountUSD: "0.242271"
                mimeType: "image/png"
                contractType: "ERC721"

  /getMintDetails:
    get:
      summary: GetMintDetails
      x-code-samples:
        - lang: JavaScript
          label: JavaScript
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { getMintDetails } from '@coinbase/onchainkit/api';

            const response = await getMintDetails({
              contractAddress: '0x...',
              takerAddress: '0x...',
              tokenId: '1',
            });
      operationId: get-mint-details
      tags:
        - Mint
      parameters:
        - $ref: '#/components/parameters/ContractAddressParam'
        - $ref: '#/components/parameters/TakerAddressParam'
        - $ref: '#/components/parameters/TokenIdParam'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMintDetailsResponse'
              example:
                name: "NFT Name"
                description: "NFT description"
                imageUrl: "https://example.com/image.png"
                animationUrl: ""
                mimeType: "image/png"
                contractType: "ERC721"
                price:
                  amount: "0.0001"
                  currency: "ETH"
                  amountUSD: "0.242271"
                mintFee:
                  amount: "0"
                  currency: "ETH"
                  amountUSD: "0"
                maxMintsPerWallet: 100
                isEligibleToMint: true
                creatorAddress: "0x..."
                network: ""
                totalTokens: "300"
                totalOwners: "200"

  /BuildMintTransaction:
    post:
      summary: BuildMintTransaction
      x-code-samples:
        - lang: JavaScript
          label: JavaScript
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { buildMintTransaction } from '@coinbase/onchainkit/api';
            
            const response = await buildMintTransaction({
              mintAddress: '0x...',
              takerAddress: '0x...',
              tokenId: '1',
              quantity: 1,
              network: 'networks/base-mainnet',
            });
      operationId: build-mint-transaction
      tags:
        - Mint
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BuildMintTransactionParams'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BuildMintTransactionResponse'
              example:
                call_data:
                  from: "0x..."
                  to: "0x..."
                  data: "0x..."
                  value: "0x000000000001"

  /buildSwapTransaction:
    post:
      summary: BuildSwapTransaction
      x-code-samples:
        - lang: JavaScript
          label: JavaScript
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { buildSwapTransaction } from '@coinbase/onchainkit/api';
            import type { Token } from '@coinbase/onchainkit/token';
            
            setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });
            
            const fromToken: Token = {
              name: 'ETH',
              address: '',
              symbol: 'ETH',
              decimals: 18,
              image: 'https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png',
              chainId: 8453,
            };
            
            const toToken: Token = {
              name: 'USDC',
              address: '******************************************',
              symbol: 'USDC',
              decimals: 6,
              image:
                'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2',
              chainId: 8453,
            };
            
            const response = await buildSwapTransaction({
              fromAddress: '0x...',
              from: fromToken,
              to: toToken,
              amount: '0.1',
              useAggregator: false,
            });
      operationId: build-swap-transaction
      tags:
        - Swap
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BuildSwapTransactionRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BuildSwapTransactionResponse'
              example:
                approveTransaction:
                  chainId: 8453
                  data: ""
                  gas: 0
                  to: ""
                  value: 0
                fee:
                  baseAsset:
                    name: "USDC"
                    address: "******************************************"
                    currencyCode: "USDC"
                    decimals: 6
                    imageURL: "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2"
                    blockchain: "eth"
                    aggregators: []
                    swappable: true
                    unverified: false
                    chainId: 8453
                  percentage: "1"
                  amount: "3517825"
                quote:
                  from:
                    address: ""
                    chainId: 8453
                    decimals: 18
                    image: "https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png"
                    name: "ETH"
                    symbol: "ETH"
                  to:
                    address: "******************************************"
                    chainId: 8453
                    decimals: 6
                    image: "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2"
                    name: "USDC"
                    symbol: "USDC"
                  fromAmount: "100000000000000000"
                  toAmount: "348264739"
                  amountReference: "from"
                  priceImpact: ""
                  chainId: 8453
                  highPriceImpact: false
                  slippage: "3"
                  warning:
                    type: "warning"
                    message: "This transaction has a very high likelihood of failing if submitted"
                    description: "failed with 500000000 gas: insufficient funds for gas * price + value: address ****************************************** have 0 want 100000000000000000"
                transaction:
                  chainId: 8453
                  data: "0x..."
                  gas: 419661
                  to: "0xdef1c0ded9bec7f1a1670819833240f027b25eff"
                  value: 100000000000000000
                warning:
                  type: "warning"
                  message: "This transaction has a very high likelihood of failing if submitted"
                  description: "failed with 500000000 gas: insufficient funds for gas * price + value: address ****************************************** have 0 want 100000000000000000"

  /getSwapQuote:
    get:
      summary: GetSwapQuote
      x-code-samples:
        - lang: JavaScript
          label: JavaScript
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { getSwapQuote } from '@coinbase/onchainkit/api';
            import type { Token } from '@coinbase/onchainkit/token';
            
            setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });
            
            const fromToken: Token = {
              name: 'ETH',
              address: '',
              symbol: 'ETH',
              decimals: 18,
              image: 'https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png',
              chainId: 8453,
            };
            
            const toToken: Token = {
              name: 'USDC',
              address: '******************************************',
              symbol: 'USDC',
              decimals: 6,
              image:
                'https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2',
              chainId: 8453,
            };
            
            const quote = await getSwapQuote({
              from: fromToken,
              to: toToken,
              amount: '0.001',
              useAggregator: false,
            });
      operationId: get-swap-quote
      tags:
        - Swap
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSwapQuoteRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSwapQuoteResponse'
              example:
                amountReference: "from"
                chainId: 8453
                from:
                  address: ""
                  chainId: 8453
                  decimals: 18
                  image: "https://wallet-api-production.s3.amazonaws.com/uploads/tokens/eth_288.png"
                  name: "ETH"
                  symbol: "ETH"
                to:
                  address: "******************************************"
                  chainId: 8453
                  decimals: 6
                  image: "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/…-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2"
                  name: "USDC"
                  symbol: "USDC"
                fromAmount: "1000000000000000"
                fromAmountUSD: "2.6519265340000002"
                toAmount: "2650405"
                toAmountUSD: "2.64980125"
                priceImpact: "0"
                highPriceImpact: false
                slippage: "3"

  /getTokens:
    get:
      summary: GetTokens
      x-code-samples:
        - lang: JavaScript
          label: Search by symbol
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { getTokens } from '@coinbase/onchainkit/api';

            setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

            const tokens = await getTokens({ limit: '1', search: 'degen' });
        - lang: JavaScript
          label: Search by name
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { getTokens } from '@coinbase/onchainkit/api';

            setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

            const tokens = await getTokens({ limit: '1', search: 'Wrapped Ether' });
        - lang: JavaScript
          label: Search by address
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { getTokens } from '@coinbase/onchainkit/api';

            setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

            const tokens = await getTokens({
              limit: '1',
              search: '******************************************',
            });
      operationId: get-tokens
      tags:
        - Token
      parameters:
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successful response – Array of token objects
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Token'
              examples:
                Search by symbol:
                  summary: Search by symbol “DEGEN”
                  value:
                    - address: "******************************************"
                      chainId: 8453
                      decimals: 18
                      image: "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/3b/bf/3bbf118b5e6dc2f9e7fc607a6e7526647b4ba8f0bea87125f971446d57b296d2-MDNmNjY0MmEtNGFiZi00N2I0LWIwMTItMDUyMzg2ZDZhMWNm"
                      name: "DEGEN"
                      symbol: "DEGEN"
                Search by name:
                  summary: Search by name “Wrapped Ether”
                  value:
                    - address: "******************************************"
                      chainId: 8453
                      decimals: 18
                      image: "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/47/bc/47bc3593c2dec7c846b66b7ba5f6fa6bd69ec34f8ebb931f2a43072e5aaac7a8-YmUwNmRjZDUtMjczYy00NDFiLWJhZDUtMzgwNjFmYWM0Njkx"
                      name: "Wrapped Ether"
                      symbol: "WETH"
                Search by address:
                  summary: Search by address “******************************************”
                  value:
                    - address: "******************************************"
                      chainId: 8453
                      decimals: 6
                      image: "https://d3r81g40ycuhqg.cloudfront.net/wallet/wais/44/2b/442b80bd16af0c0d9b22e03a16753823fe826e5bfd457292b55fa0ba8c1ba213-ZWUzYjJmZGUtMDYxNy00NDcyLTg0NjQtMWI4OGEwYjBiODE2"
                      name: "USDC"
                      symbol: "USDC"

  /getPortfolios:
    get:
      summary: GetPortfolios
      operationId: get-portfolios
      tags:
        - Wallet
      x-code-samples:
        - lang: JavaScript
          label: JavaScript
          source: |
            import { setOnchainKitConfig } from '@coinbase/onchainkit';
            import { getPortfolios } from '@coinbase/onchainkit/api';

            setOnchainKitConfig({ apiKey: 'YOUR_API_KEY' });

            const response = await getPortfolios({
              addresses: ['0x...'],
            });
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPortfoliosRequest'
      responses:
        '200':
          description: Successful response – Array of portfolio objects
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPortfoliosResponse'
              example:
                portfolios:
                  - address: "0x..."
                    portfolioBalanceInUsd: 100
                    tokenBalances:
                      - address: "0x..."
                        chainId: 1
                        decimals: 18
                        image: "https://example.com/image.png"
                        name: "Token Name"
                        symbol: "TKN"
                        cryptoBalance: 10
                        fiatBalance: 100