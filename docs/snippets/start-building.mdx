## Start building!

Explore our ready-to-use onchain components:

- [**`Identity`**](/onchainkit/identity/identity) – Show [Basenames](/onchainkit/identity/identity), [avatars](/onchainkit/identity/avatar), [badges](/onchainkit/identity/badge), and [addresses](/onchainkit/identity/address).
- [**`Wallet`**](/onchainkit/wallet/wallet) – Create or connect wallets with [Connect Wallet](/onchainkit/wallet/wallet).
- [**`Transaction`**](/onchainkit/transaction/transaction) – Handle [transactions](/onchainkit/transaction/transaction) using EOAs or Smart Wallets.
- [**`Checkout`**](/onchainkit/checkout/checkout) – Integrate USDC [checkout](/onchainkit/checkout/checkout) flows with ease.
- [**`Fund`**](/onchainkit/fund/fund-button) – Create a [funding](/onchainkit/fund/fund-button) flow to onboard users.
- [**`Tokens`**](/onchainkit/token/token-chip) – Search and display [tokens](/onchainkit/token/token-chip) with various components.
- [**`Swap`**](/onchainkit/swap/swap) – Enable [token swaps](/onchainkit/swap/swap) in your app.
- [**`Mint`**](/onchainkit/mint/nft-mint-card) – [View](/onchainkit/mint/nft-mint-card) and [Mint](/onchainkit/mint/nft-mint-card) NFTs in your app.
