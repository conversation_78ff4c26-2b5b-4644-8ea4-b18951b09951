## Use AI-powered IDEs

You can also directly download the [context](https://github.com/fakepixels/md-generator/blob/master/combined-ock-docs-0.35.8.mdx) and import it into AI-powered IDE such as Cursor or Replit.

In addition, you can import a `.cursorrules` file in the root of your project via [Cursor Directory](https://cursor.directory/onchainkit). Cursor also has an array of resources [here](https://cursor.directory/learn) on how to use AI-powered IDEs.

<Frame>
<img alt="Cursor"
  src="/images/onchainkit/cursor-dir.gif"
  height="364"/>
</Frame>

## AI Tooling

### Replit

[Replit](https://replit.com) is a cloud-based coding platform that streamlines the process of setting up, building, sharing, and deploying projects. It allows developers to code in a Google Docs-like environment, and pre-built templates provide a great starting point for building a website, app, or game. Its new AI Agent can assist with the code development process and work with several files at once, making the programming process feel like a one-on-one conversation.

### <PERSON>ursor

[<PERSON><PERSON><PERSON>](https://cursor.com) is an AI-powered code editor that makes the programming experience feel like magic. Built as a fork of VS Code, it boasts powerful features like AI code completion, natural language editing, and codebase understanding. Cursor Pro is free for the first two weeks after signup, and offers more powerful models.

### Using OnchainKit with CDP SDK

You can use OnchainKit with [CDP SDK](https://docs.cdp.coinbase.com/get-started/docs/overview) to access additional capabilities such as [AgentKit](https://docs.cdp.coinbase.com/agentkit/docs/welcome).
