import { Icon } from '/snippets/base-org/Icon/Icon.mdx';



export const Button = ({
  children,
  disabled,
  variant = "primary",
  size = "medium",
  iconName,
  roundedFull = false,
  className = '',
  fullWidth = false,
  onClick = undefined,
}) => {

  const variantStyles = {
  primary:
    'bg-blue text-black border border-blue hover:bg-blue-80 active:bg-[#06318E] dark:text-white',
  secondary:
    'bg-white border border-white text-palette-foreground hover:bg-zinc-15 active:bg-zinc-30',
  outlined:
    'bg-transparent text-white border border-white hover:bg-white hover:text-black active:bg-[#E3E7E9]',
}

  const sizeStyles = {
    medium: 'text-md px-4 py-2 gap-3',
    large: 'text-lg px-6 py-4 gap-5',
  }

  const sizeIconRatio = {
    medium: '0.75rem',
    large: '1rem',
  }
  
  const classes = [
    'text-md px-4 py-2 whitespace-nowrap',
    'flex items-center justify-center',
    'disabled:opacity-40 disabled:pointer-events-none',
    'transition-all',
    variantStyles[variant],
    sizeStyles[size],
    roundedFull ? 'rounded-full' : 'rounded-lg',
    fullWidth ? 'w-full' : 'w-auto',
    className,
  ]

  const buttonClasses = classes.filter(Boolean).join(' ')
  const iconSize = sizeIconRatio[size]

  return (
    <button
      type="button"
      disabled={disabled}
      className={buttonClasses}
      onClick={onClick}
    >
      <span>{children}</span>
      {iconName && (
        <Icon
          name={iconName}
          width={iconSize}
          height={iconSize}
          color="currentColor"
        />
      )}
    </button>
  )
}