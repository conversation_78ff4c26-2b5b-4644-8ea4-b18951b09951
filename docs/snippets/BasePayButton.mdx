export const BasePayButton = ({ colorScheme = 'light' }) => {
  const isLight = colorScheme === 'light';
  
  return (
    <button
      type="button"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '12px 16px',
        backgroundColor: isLight ? '#ffffff' : '#0000FF',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        minWidth: '180px',
        height: '44px'
      }}
    >
      <img
        src={isLight ? '/images/base-account/BasePayBlueLogo.png' : '/images/base-account/BasePayWhiteLogo.png'}
        alt="Base Pay"
        style={{
          height: '20px',
          width: 'auto'
        }}
      />
    </button>
  );
};