## LangChain Local Environment Setup

### TypeScript

<Steps>
  <Step title="Set Up Your Development Environment">
  ```bash Terminal
  node --version  # Should be 18+
  npm --version   # Should be 9.7.2+
  ```
  </Step>
  <Step title="Clone and Set Up the Repository">
  ```bash Terminal
  git clone https://github.com/coinbase/agentkit.git
  cd agentkit
  npm install
  npm run build
  cd typescript/examples/langchain-cdp-chatbot
  ```
  </Step>
  <Step title="Configure Environment Variables">
  ```bash Terminal
  cp .env.local .env
  ```
  </Step>
  <Step title="Run the Agent">
  ```bash Terminal
  npm run start
  ```
  </Step>
</Steps>

### Python

<Steps>
  <Step title="Set Up Your Development Environment">
  ```bash Terminal
  python --version  # Should be 3.10+
  poetry --version # Verify Poetry installation
  ```
  </Step>
  <Step title="Clone and Set Up the Repository">
  ```bash Terminal
  git clone https://github.com/coinbase/agentkit.git
  cd agentkit/python/examples/cdp-langchain-chatbot
  ```
  </Step>
  <Step title="Configure Environment Variables">
  ```bash Terminal
  cp .env.local .env
  # Edit .env with your credentials
  ```
  </Step>
  <Step title="Run the Agent">
  ```bash Terminal
  poetry install
  poetry run python main.py
  ```
  </Step>
</Steps>
