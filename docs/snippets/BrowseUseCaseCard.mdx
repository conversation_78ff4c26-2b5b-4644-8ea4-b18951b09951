export const BrowseUseCaseCard = ({ title, description, icon, href }) => {
  const CardWrapper = href ? 'a' : 'div';

  return (
    <CardWrapper href={href} className="block h-[420px]">
      <div className="flex h-full flex-col">
        <div className="mb-4 flex h-[280px] w-full items-center justify-center rounded-lg bg-neutral-400 dark:bg-zinc-900">
          <div className="mx-auto flex w-[60%] items-center justify-center">{icon}</div>
        </div>

        <div className="flex flex-1 flex-col">
          <h3 className="mb-1 text-lg font-bold text-black dark:text-white">{title}</h3>
          <p className="text-black dark:text-zinc-400">{description}</p>
        </div>
      </div>
    </CardWrapper>
  );
}
