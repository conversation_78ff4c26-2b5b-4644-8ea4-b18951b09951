### [Ethereum Applications](/learn/introduction-to-ethereum/ethereum-applications)

- Describe the origin and goals of the Ethereum blockchain
- List common types of applications that can be developed with the Ethereum blockchain
- Compare and contrast Web2 vs. Web3 development
- Compare and contrast the concept of "ownership" in Web2 vs. Web3

### [Gas Use in Ethereum Transactions](/learn/introduction-to-ethereum/gas-use-in-eth-transactions)

- Explain what gas is in Ethereum
- Explain why gas is necessary in Ethereum
- Understand how gas works in Ethereum transactions

### [EVM Diagram](/learn/introduction-to-ethereum/evm-diagram)

- Diagram the EVM

### [Setup and Overview](/learn/hardhat/hardhat-setup-overview/hardhat-setup-overview-sbs)

- Install and create a new Hardhat project with Typescript support
- Describe the organization and folder structure of a Hardhat project
- List the use and properties of hardhat.config.ts

### [Testing with Hardhat and Typechain](/learn/hardhat/hardhat-testing/hardhat-testing-sbs)

- Set up TypeChain to enable testing
- Write unit tests for smart contracts using <PERSON><PERSON>, <PERSON><PERSON>, and the Hardhat Toolkit
- Set up multiple signers and call smart contract functions with different signers

### [Etherscan](/learn/hardhat/etherscan/etherscan-sbs)

- List some of the features of Etherscan
- Read data from the Bored Ape Yacht Club contract on Etherscan
- Write data to a contract using Etherscan.

### [Deploying Smart Contracts](/learn/hardhat/hardhat-deploy/hardhat-deploy-sbs)

- Deploy a smart contract to the Base Sepolia Testnet with hardhat-deploy
- Deploy a smart contract to the Sepolia Testnet with hardhat-deploy
- Use BaseScan to view a deployed smart contract

### [Verifying Smart Contracts](/learn/hardhat/hardhat-verify/hardhat-verify-sbs)

- Verify a deployed smart contract on Etherscan
- Connect a wallet to a contract in Etherscan
- Use etherscan to interact with your own deployed contract

### [Hardhat Forking](/learn/hardhat/hardhat-forking/hardhat-forking)

- Use Hardhat Network to create a local fork of mainnet and deploy a contract to it
- Utilize Hardhat forking features to configure the fork for several use cases

### ['Introduction to Remix'](/learn/introduction-to-solidity/introduction-to-remix)

- List the features, pros, and cons of using Remix as an IDE
- Deploy and test the Storage.sol demo contract in Remix

### [Deployment in Remix](/learn/introduction-to-solidity/deployment-in-remix)

- Deploy and test the Storage.sol demo contract in Remix

### [Hello World](/learn/contracts-and-basic-functions/hello-world-step-by-step)

- Construct a simple "Hello World" contract
- List the major differences between data types in Solidity as compared to other languages
- Select the appropriate visibility for a function

### [Basic Types](/learn/contracts-and-basic-functions/basic-types)

- Categorize basic data types
- List the major differences between data types in Solidity as compared to other languages
- Compare and contrast signed and unsigned integers

### [Test Networks](/learn/deployment-to-testnet/test-networks)

- Describe the uses and properties of the Base testnet
- Compare and contrast Ropsten, Rinkeby, Goerli, and Sepolia

### [Deployment to Base Sepolia](/learn/deployment-to-testnet/deployment-to-base-sepolia-sbs)

- Deploy a contract to the Base Sepolia testnet and interact with it in [BaseScan]

### [Contract Verification](/learn/deployment-to-testnet/contract-verification-sbs)

- Verify a contract on the Base Sepolia testnet and interact with it in [BaseScan]

### [Control Structures](/learn/control-structures/control-structures)

- Control code flow with `if`, `else`, `while`, and `for`
- List the unique constraints for control flow in Solidity
- Utilize `require` to write a function that can only be used when a variable is set to `true`
- Write a `revert` statement to abort execution of a function in a specific state
- Utilize `error` to control flow more efficiently than with `require`

### [Storing Data](/learn/storage/simple-storage-sbs)

- Use the constructor to initialize a variable
- Access the data in a public variable with the automatically generated getter
- Order variable declarations to use storage efficiently

### [How Storage Works](/learn/storage/how-storage-works)

- Diagram how a contract's data is stored on the blockchain (Contract -> Blockchain)
- Order variable declarations to use storage efficiently
- Diagram how variables in a contract are stored (Variable -> Contract)

### [Arrays](/learn/arrays/arrays-in-solidity)

- Describe the difference between storage, memory, and calldata arrays

### [Filtering an Array](/learn/arrays/filtering-an-array-sbs)

- Write a function that can return a filtered subset of an array

### [Mappings](/learn/mappings/mappings-sbs)

- Construct a Map (dictionary) data type
- Recall that assignment of the Map data type is not as flexible as for other data types/in other languages
- Restrict function calls with the `msg.sender` global variable
- Recall that there is no collision protection in the EVM and why this is (probably) ok

### [Function Visibility and State Mutability](/learn/advanced-functions/function-visibility)

- Categorize functions as public, private, internal, or external based on their usage
- Describe how pure and view functions are different than functions that modify storage

### [Function Modifiers](/learn/advanced-functions/function-modifiers)

- Use modifiers to efficiently add functionality to multiple functions

### [Structs](/learn/structs/structs-sbs)

- Construct a `struct` (user-defined type) that contains several different data types
- Declare members of the `struct` to maximize storage efficiency
- Describe constraints related to the assignment of `struct`s depending on the types they contain

### [Inheritance](/learn/inheritance/inheritance-sbs)

- Write a smart contract that inherits from another contract
- Describe the impact inheritance has on the byte code size limit

### [Multiple Inheritance](/learn/inheritance/multiple-inheritance)

- Write a smart contract that inherits from multiple contracts

### [Abstract Contracts](/learn/inheritance/abstract-contracts-sbs)

- Use the virtual, override, and abstract keywords to create and use an abstract contract

### [Imports](/learn/imports/imports-sbs)

- Import and use code from another file
- Utilize OpenZeppelin contracts within Remix

### [Error Triage](/learn/error-triage/error-triage)

- Debug common solidity errors including transaction reverted, out of gas, stack overflow, value overflow/underflow, index out of range, etc.

### [The New Keyword](/learn/new-keyword/new-keyword-sbs)

- Write a contract that creates a new contract with the new keyword

### ['Contract to Contract Interaction'](/learn/interfaces/contract-to-contract-interaction)

- Use interfaces to allow a smart contract to call functions in another smart contract
- Use the `call()` function to interact with another contract without using an interface

### [Events](/learn/events/hardhat-events-sbs)

- Write and trigger an event
- List common uses of events
- Understand events vs. smart contract storage

### [Address and Payable in Solidity](/learn/address-and-payable/address-and-payable)

- Differentiate between address and address payable types in Solidity
- Determine when to use each type appropriately in contract development
- Employ address payable to send Ether and interact with payable functions

### [Minimal Token](/learn/token-development/minimal-tokens/minimal-token-sbs)

- Construct a minimal token and deploy to testnet
- Identify the properties that make a token a token

### [The ERC-20 Token Standard](/learn/token-development/erc-20-token/erc-20-standard)

- Analyze the anatomy of an ERC-20 token
- Review the formal specification for ERC-20

### [ERC-20 Implementation](/learn/token-development/erc-20-token/erc-20-token-sbs)

- Describe OpenZeppelin
- Import the OpenZeppelin ERC-20 implementation
- Describe the difference between the ERC-20 standard and OpenZeppelin's ERC20.sol
- Build and deploy an ERC-20 compliant token

### [The ERC-721 Token Standard](/learn/token-development/erc-721-token/erc-721-standard)

- Analyze the anatomy of an ERC-721 token
- Compare and contrast the technical specifications of ERC-20 and ERC-721
- Review the formal specification for ERC-721

### [ERC-721 Token](/learn/token-development/erc-721-token/erc-721-sbs)

- Analyze the anatomy of an ERC-721 token
- Compare and contrast the technical specifications of ERC-20 and ERC-721
- Review the formal specification for ERC-721
- Build and deploy an ERC-721 compliant token
- Use an ERC-721 token to control ownership of another data structure

### [Wallet Connectors](/learn/onchain-app-development/frontend-setup/wallet-connectors)

- Identify the role of a wallet aggregator in an onchain app
- Debate the pros and cons of using a template
- Scaffold a new onchain app with RainbowKit
- Support users of EOAs and the Coinbase Smart Wallet with the same app

### [Building an Onchain App](/learn/onchain-app-development/frontend-setup/building-an-onchain-app)

- Identify the role of a wallet aggregator in an onchain app
- Debate the pros and cons of using a template
- Add a wallet connection to a standard template app

### [The `useAccount` Hook](/learn/onchain-app-development/reading-and-displaying-data/useAccount)

- Implement the `useAccount` hook to show the user's address, connection state, network, and balance
- Implement an `isMounted` hook to prevent hydration errors

### [The `useReadContract` Hook](/learn/onchain-app-development/reading-and-displaying-data/useReadContract)

- Implement wagmi's `useReadContract` hook to fetch data from a smart contract
- Convert data fetched from a smart contract to information displayed to the user
- Identify the caveats of reading data from automatically-generated getters

### [Configuring `useReadContract`](/learn/onchain-app-development/reading-and-displaying-data/configuring-useReadContract)

- Use `useBlockNumber` and the `queryClient` to automatically fetch updates from the blockchain
- Describe the costs of using the above, and methods to reduce those costs
- Configure arguments to be passed with a call to a `pure` or `view` smart contract function
- Call an instance of `useReadContract` on demand
- Utilize `isLoading` and `isFetching` to improve user experience

### [The `useWriteContract` hook](/learn/onchain-app-development/writing-to-contracts/useWriteContract)

- Implement wagmi's `useWriteContract` hook to send transactions to a smart contract
- Configure the options in `useWriteContract`
- Display the execution, success, or failure of a function with button state changes, and data display

### [The `useSimulateContract` hook](/learn/onchain-app-development/writing-to-contracts/useSimulateContract)

- Implement wagmi's `useSimulateContract` and `useWriteContract` to send transactions to a smart contract
- Configure the options in `useSimulateContract` and `useWriteContract`
- Call a smart contract function on-demand using the write function from `useWriteContract`, with arguments and a value