import { BaseBanner } from "/snippets/banner/BaseBanner.mdx";
import { Button } from "/snippets/Button/index.mdx";

<BaseBanner
  id="privacy-policy"
  dismissable={false}
  content={({ onDismiss }) => (
    <div className="flex items-center">
      <div className="mr-2">
        We're updating the Base Privacy Policy, effective July 25, 2025, to reflect an expansion of Base services. Please review the updated policy here:{" "}
        <a
          href="https://docs.base.org/privacy-policy-2025"
          target="_blank"
          className="whitespace-nowrap"
        >
          Base Privacy Policy
        </a>. By continuing to use Base services, you confirm that you have read and understand the updated policy.
      </div>
      <Button onClick={onDismiss}>I Acknowledge</Button>
    </div>
  )}
/>
