name: "Chromatic Publish"

on:
  push:
    paths:
      - "storybook/**"

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Harden the runner (Audit all outbound calls)
        uses: step-security/harden-runner@002fdce3c6a235733a90a27c80493a3241e56863 # v2.12.1
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install dependencies
        run: cd storybook && yarn install
      - uses: chromaui/action@3378c92d26c32353e53166beac732e2edc14213c # latest
        with:
          workingDir: storybook
          buildScriptName: build-storybook
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          onlyChanged: true
