{"address": "0xF4D953A3976F392aA5509612DEfF395983f22a84", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "NotActive", "type": "error"}, {"inputs": [], "name": "SoulboundToken", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "approved", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "submission", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "passed", "type": "bool"}, {"components": [{"internalType": "string", "name": "message", "type": "string"}, {"components": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult[]", "name": "elements", "type": "tuple[]"}, {"internalType": "uint256", "name": "num", "type": "uint256"}], "internalType": "struct List.ARList", "name": "assertResults", "type": "tuple"}], "indexed": false, "internalType": "struct Cafe.TestResult[]", "name": "testResults", "type": "tuple[]"}], "name": "TestSuiteResult", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "string", "name": "message", "type": "string"}, {"components": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult[]", "name": "elements", "type": "tuple[]"}, {"internalType": "uint256", "name": "num", "type": "uint256"}], "internalType": "struct List.ARList", "name": "assertResults", "type": "tuple"}], "internalType": "struct Cafe.TestResult[]", "name": "_testResults", "type": "tuple[]"}], "name": "checkIfAllPassed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "a", "type": "bytes"}, {"internalType": "bytes", "name": "b", "type": "bytes"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "address", "name": "b", "type": "address"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "string", "name": "a", "type": "string"}, {"internalType": "string", "name": "b", "type": "string"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "a", "type": "string[]"}, {"internalType": "string[]", "name": "b", "type": "string[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "a", "type": "address[]"}, {"internalType": "address[]", "name": "b", "type": "address[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "a", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "b", "type": "uint256[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "a", "type": "bool"}], "name": "isFalse", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "a", "type": "bool"}], "name": "isTrue", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "address", "name": "b", "type": "address"}], "name": "notEqual", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "owners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_setActiveTo", "type": "bool"}], "name": "setActive", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_tokenURI", "type": "string"}], "name": "setTokenURI", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_submissionAddress", "type": "address"}], "name": "testContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "tokenIds", "outputs": [{"internalType": "uint256", "name": "_value", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0x4698be099de53af56900a853a60416929ccb38b0b310aa42669cab436a98afc2", "receipt": {"to": null, "from": "0x0919C594E549545374772246B0D433a4988A0eC9", "contractAddress": "0xF4D953A3976F392aA5509612DEfF395983f22a84", "transactionIndex": 3, "gasUsed": "7197650", "logsBloom": "0x00000000000000000000000000000040000000000000000000800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001800000000000000000000000000000000000020000200000000000000840000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000040000000000000000000000000000000000000000000000000000", "blockHash": "0x3f2b88eac65ca6cb45e5afd70b2a42e442176e4dba2fc4fd90971b90aa9fb36f", "transactionHash": "0x4698be099de53af56900a853a60416929ccb38b0b310aa42669cab436a98afc2", "logs": [{"transactionIndex": 3, "blockNumber": 4564455, "transactionHash": "0x4698be099de53af56900a853a60416929ccb38b0b310aa42669cab436a98afc2", "address": "0xF4D953A3976F392aA5509612DEfF395983f22a84", "topics": ["0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x0000000000000000000000000919c594e549545374772246b0d433a4988a0ec9"], "data": "0x", "logIndex": 0, "blockHash": "0x3f2b88eac65ca6cb45e5afd70b2a42e442176e4dba2fc4fd90971b90aa9fb36f"}], "blockNumber": 4564455, "cumulativeGasUsed": "7287063", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "6a581879d712a9c25e4fb247f22274cf", "metadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"NotActive\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SoulboundToken\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"submission\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"string\",\"name\":\"message\",\"type\":\"string\"},{\"components\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult[]\",\"name\":\"elements\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"internalType\":\"struct List.ARList\",\"name\":\"assertResults\",\"type\":\"tuple\"}],\"indexed\":false,\"internalType\":\"struct Cafe.TestResult[]\",\"name\":\"testResults\",\"type\":\"tuple[]\"}],\"name\":\"TestSuiteResult\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"message\",\"type\":\"string\"},{\"components\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult[]\",\"name\":\"elements\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"internalType\":\"struct List.ARList\",\"name\":\"assertResults\",\"type\":\"tuple\"}],\"internalType\":\"struct Cafe.TestResult[]\",\"name\":\"_testResults\",\"type\":\"tuple[]\"}],\"name\":\"checkIfAllPassed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"a\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"b\",\"type\":\"bytes\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"b\",\"type\":\"uint256\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"a\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"b\",\"type\":\"address\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"a\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"b\",\"type\":\"string\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"a\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"b\",\"type\":\"string[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"a\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"b\",\"type\":\"address[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"a\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"b\",\"type\":\"uint256[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"a\",\"type\":\"bool\"}],\"name\":\"isFalse\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"a\",\"type\":\"bool\"}],\"name\":\"isTrue\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"a\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"b\",\"type\":\"address\"}],\"name\":\"notEqual\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"owners\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_setActiveTo\",\"type\":\"bool\"}],\"name\":\"setActive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_tokenURI\",\"type\":\"string\"}],\"name\":\"setTokenURI\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_submissionAddress\",\"type\":\"address\"}],\"name\":\"testContract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenIds\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when `owner` enables `approved` to manage the `tokenId` token.\"},\"ApprovalForAll(address,address,bool)\":{\"details\":\"Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `tokenId` token is transferred from `from` to `to`.\"}},\"kind\":\"dev\",\"methods\":{\"approve(address,uint256)\":{\"details\":\"See {IERC721-approve}.\"},\"balanceOf(address)\":{\"details\":\"See {IERC721-balanceOf}.\"},\"getApproved(uint256)\":{\"details\":\"See {IERC721-getApproved}.\"},\"isApprovedForAll(address,address)\":{\"details\":\"See {IERC721-isApprovedForAll}.\"},\"name()\":{\"details\":\"See {IERC721Metadata-name}.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"ownerOf(uint256)\":{\"details\":\"See {IERC721-ownerOf}.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"safeTransferFrom(address,address,uint256)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"safeTransferFrom(address,address,uint256,bytes)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"setApprovalForAll(address,bool)\":{\"details\":\"See {IERC721-setApprovalForAll}.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"},\"symbol()\":{\"details\":\"See {IERC721Metadata-symbol}.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC721-transferFrom}.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"checkIfAllPassed((string,((bool,string,string,string,string)[],uint256))[])\":{\"notice\":\"Check each assert in each test to see if any failed. Note:  The check is here instead of setting a `passed` bool in `TestResult` to reduce the amount of code in each unit test.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/ControlStructuresUT.sol\":\"ControlStructuresUT\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":1000},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../utils/Context.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * By default, the owner account will be the one that deploys the contract. This\\n * can later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract Ownable is Context {\\n    address private _owner;\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial owner.\\n     */\\n    constructor() {\\n        _transferOwnership(_msgSender());\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        require(owner() == _msgSender(), \\\"Ownable: caller is not the owner\\\");\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        require(newOwner != address(0), \\\"Ownable: new owner is the zero address\\\");\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xba43b97fba0d32eb4254f6a5a297b39a19a247082a02d6e69349e071e2946218\",\"license\":\"MIT\"},\"@openzeppelin/contracts/security/ReentrancyGuard.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (security/ReentrancyGuard.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Contract module that helps prevent reentrant calls to a function.\\n *\\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\\n * available, which can be applied to functions to make sure there are no nested\\n * (reentrant) calls to them.\\n *\\n * Note that because there is a single `nonReentrant` guard, functions marked as\\n * `nonReentrant` may not call one another. This can be worked around by making\\n * those functions `private`, and then adding `external` `nonReentrant` entry\\n * points to them.\\n *\\n * TIP: If you would like to learn more about reentrancy and alternative ways\\n * to protect against it, check out our blog post\\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\\n */\\nabstract contract ReentrancyGuard {\\n    // Booleans are more expensive than uint256 or any type that takes up a full\\n    // word because each write operation emits an extra SLOAD to first read the\\n    // slot's contents, replace the bits taken up by the boolean, and then write\\n    // back. This is the compiler's defense against contract upgrades and\\n    // pointer aliasing, and it cannot be disabled.\\n\\n    // The values being non-zero value makes deployment a bit more expensive,\\n    // but in exchange the refund on every call to nonReentrant will be lower in\\n    // amount. Since refunds are capped to a percentage of the total\\n    // transaction's gas, it is best to keep them low in cases like this one, to\\n    // increase the likelihood of the full refund coming into effect.\\n    uint256 private constant _NOT_ENTERED = 1;\\n    uint256 private constant _ENTERED = 2;\\n\\n    uint256 private _status;\\n\\n    constructor() {\\n        _status = _NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and making it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        _nonReentrantBefore();\\n        _;\\n        _nonReentrantAfter();\\n    }\\n\\n    function _nonReentrantBefore() private {\\n        // On the first call to nonReentrant, _status will be _NOT_ENTERED\\n        require(_status != _ENTERED, \\\"ReentrancyGuard: reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        _status = _ENTERED;\\n    }\\n\\n    function _nonReentrantAfter() private {\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        _status = _NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Returns true if the reentrancy guard is currently set to \\\"entered\\\", which indicates there is a\\n     * `nonReentrant` function in the call stack.\\n     */\\n    function _reentrancyGuardEntered() internal view returns (bool) {\\n        return _status == _ENTERED;\\n    }\\n}\\n\",\"keccak256\":\"0xa535a5df777d44e945dd24aa43a11e44b024140fc340ad0dfe42acf4002aade1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/ERC721.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC721/ERC721.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC721.sol\\\";\\nimport \\\"./IERC721Receiver.sol\\\";\\nimport \\\"./extensions/IERC721Metadata.sol\\\";\\nimport \\\"../../utils/Address.sol\\\";\\nimport \\\"../../utils/Context.sol\\\";\\nimport \\\"../../utils/Strings.sol\\\";\\nimport \\\"../../utils/introspection/ERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of https://eips.ethereum.org/EIPS/eip-721[ERC721] Non-Fungible Token Standard, including\\n * the Metadata extension, but not including the Enumerable extension, which is available separately as\\n * {ERC721Enumerable}.\\n */\\ncontract ERC721 is Context, ERC165, IERC721, IERC721Metadata {\\n    using Address for address;\\n    using Strings for uint256;\\n\\n    // Token name\\n    string private _name;\\n\\n    // Token symbol\\n    string private _symbol;\\n\\n    // Mapping from token ID to owner address\\n    mapping(uint256 => address) private _owners;\\n\\n    // Mapping owner address to token count\\n    mapping(address => uint256) private _balances;\\n\\n    // Mapping from token ID to approved address\\n    mapping(uint256 => address) private _tokenApprovals;\\n\\n    // Mapping from owner to operator approvals\\n    mapping(address => mapping(address => bool)) private _operatorApprovals;\\n\\n    /**\\n     * @dev Initializes the contract by setting a `name` and a `symbol` to the token collection.\\n     */\\n    constructor(string memory name_, string memory symbol_) {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override(ERC165, IERC165) returns (bool) {\\n        return\\n            interfaceId == type(IERC721).interfaceId ||\\n            interfaceId == type(IERC721Metadata).interfaceId ||\\n            super.supportsInterface(interfaceId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-balanceOf}.\\n     */\\n    function balanceOf(address owner) public view virtual override returns (uint256) {\\n        require(owner != address(0), \\\"ERC721: address zero is not a valid owner\\\");\\n        return _balances[owner];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-ownerOf}.\\n     */\\n    function ownerOf(uint256 tokenId) public view virtual override returns (address) {\\n        address owner = _ownerOf(tokenId);\\n        require(owner != address(0), \\\"ERC721: invalid token ID\\\");\\n        return owner;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-name}.\\n     */\\n    function name() public view virtual override returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-symbol}.\\n     */\\n    function symbol() public view virtual override returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-tokenURI}.\\n     */\\n    function tokenURI(uint256 tokenId) public view virtual override returns (string memory) {\\n        _requireMinted(tokenId);\\n\\n        string memory baseURI = _baseURI();\\n        return bytes(baseURI).length > 0 ? string(abi.encodePacked(baseURI, tokenId.toString())) : \\\"\\\";\\n    }\\n\\n    /**\\n     * @dev Base URI for computing {tokenURI}. If set, the resulting URI for each\\n     * token will be the concatenation of the `baseURI` and the `tokenId`. Empty\\n     * by default, can be overridden in child contracts.\\n     */\\n    function _baseURI() internal view virtual returns (string memory) {\\n        return \\\"\\\";\\n    }\\n\\n    /**\\n     * @dev See {IERC721-approve}.\\n     */\\n    function approve(address to, uint256 tokenId) public virtual override {\\n        address owner = ERC721.ownerOf(tokenId);\\n        require(to != owner, \\\"ERC721: approval to current owner\\\");\\n\\n        require(\\n            _msgSender() == owner || isApprovedForAll(owner, _msgSender()),\\n            \\\"ERC721: approve caller is not token owner or approved for all\\\"\\n        );\\n\\n        _approve(to, tokenId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-getApproved}.\\n     */\\n    function getApproved(uint256 tokenId) public view virtual override returns (address) {\\n        _requireMinted(tokenId);\\n\\n        return _tokenApprovals[tokenId];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-setApprovalForAll}.\\n     */\\n    function setApprovalForAll(address operator, bool approved) public virtual override {\\n        _setApprovalForAll(_msgSender(), operator, approved);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-isApprovedForAll}.\\n     */\\n    function isApprovedForAll(address owner, address operator) public view virtual override returns (bool) {\\n        return _operatorApprovals[owner][operator];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-transferFrom}.\\n     */\\n    function transferFrom(address from, address to, uint256 tokenId) public virtual override {\\n        //solhint-disable-next-line max-line-length\\n        require(_isApprovedOrOwner(_msgSender(), tokenId), \\\"ERC721: caller is not token owner or approved\\\");\\n\\n        _transfer(from, to, tokenId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-safeTransferFrom}.\\n     */\\n    function safeTransferFrom(address from, address to, uint256 tokenId) public virtual override {\\n        safeTransferFrom(from, to, tokenId, \\\"\\\");\\n    }\\n\\n    /**\\n     * @dev See {IERC721-safeTransferFrom}.\\n     */\\n    function safeTransferFrom(address from, address to, uint256 tokenId, bytes memory data) public virtual override {\\n        require(_isApprovedOrOwner(_msgSender(), tokenId), \\\"ERC721: caller is not token owner or approved\\\");\\n        _safeTransfer(from, to, tokenId, data);\\n    }\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients\\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\\n     *\\n     * `data` is additional data, it has no specified format and it is sent in call to `to`.\\n     *\\n     * This internal function is equivalent to {safeTransferFrom}, and can be used to e.g.\\n     * implement alternative mechanisms to perform token transfer, such as signature-based.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _safeTransfer(address from, address to, uint256 tokenId, bytes memory data) internal virtual {\\n        _transfer(from, to, tokenId);\\n        require(_checkOnERC721Received(from, to, tokenId, data), \\\"ERC721: transfer to non ERC721Receiver implementer\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the owner of the `tokenId`. Does NOT revert if token doesn't exist\\n     */\\n    function _ownerOf(uint256 tokenId) internal view virtual returns (address) {\\n        return _owners[tokenId];\\n    }\\n\\n    /**\\n     * @dev Returns whether `tokenId` exists.\\n     *\\n     * Tokens can be managed by their owner or approved accounts via {approve} or {setApprovalForAll}.\\n     *\\n     * Tokens start existing when they are minted (`_mint`),\\n     * and stop existing when they are burned (`_burn`).\\n     */\\n    function _exists(uint256 tokenId) internal view virtual returns (bool) {\\n        return _ownerOf(tokenId) != address(0);\\n    }\\n\\n    /**\\n     * @dev Returns whether `spender` is allowed to manage `tokenId`.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function _isApprovedOrOwner(address spender, uint256 tokenId) internal view virtual returns (bool) {\\n        address owner = ERC721.ownerOf(tokenId);\\n        return (spender == owner || isApprovedForAll(owner, spender) || getApproved(tokenId) == spender);\\n    }\\n\\n    /**\\n     * @dev Safely mints `tokenId` and transfers it to `to`.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must not exist.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _safeMint(address to, uint256 tokenId) internal virtual {\\n        _safeMint(to, tokenId, \\\"\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-ERC721-_safeMint-address-uint256-}[`_safeMint`], with an additional `data` parameter which is\\n     * forwarded in {IERC721Receiver-onERC721Received} to contract recipients.\\n     */\\n    function _safeMint(address to, uint256 tokenId, bytes memory data) internal virtual {\\n        _mint(to, tokenId);\\n        require(\\n            _checkOnERC721Received(address(0), to, tokenId, data),\\n            \\\"ERC721: transfer to non ERC721Receiver implementer\\\"\\n        );\\n    }\\n\\n    /**\\n     * @dev Mints `tokenId` and transfers it to `to`.\\n     *\\n     * WARNING: Usage of this method is discouraged, use {_safeMint} whenever possible\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must not exist.\\n     * - `to` cannot be the zero address.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _mint(address to, uint256 tokenId) internal virtual {\\n        require(to != address(0), \\\"ERC721: mint to the zero address\\\");\\n        require(!_exists(tokenId), \\\"ERC721: token already minted\\\");\\n\\n        _beforeTokenTransfer(address(0), to, tokenId, 1);\\n\\n        // Check that tokenId was not minted by `_beforeTokenTransfer` hook\\n        require(!_exists(tokenId), \\\"ERC721: token already minted\\\");\\n\\n        unchecked {\\n            // Will not overflow unless all 2**256 token ids are minted to the same owner.\\n            // Given that tokens are minted one by one, it is impossible in practice that\\n            // this ever happens. Might change if we allow batch minting.\\n            // The ERC fails to describe this case.\\n            _balances[to] += 1;\\n        }\\n\\n        _owners[tokenId] = to;\\n\\n        emit Transfer(address(0), to, tokenId);\\n\\n        _afterTokenTransfer(address(0), to, tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Destroys `tokenId`.\\n     * The approval is cleared when the token is burned.\\n     * This is an internal function that does not check if the sender is authorized to operate on the token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _burn(uint256 tokenId) internal virtual {\\n        address owner = ERC721.ownerOf(tokenId);\\n\\n        _beforeTokenTransfer(owner, address(0), tokenId, 1);\\n\\n        // Update ownership in case tokenId was transferred by `_beforeTokenTransfer` hook\\n        owner = ERC721.ownerOf(tokenId);\\n\\n        // Clear approvals\\n        delete _tokenApprovals[tokenId];\\n\\n        unchecked {\\n            // Cannot overflow, as that would require more tokens to be burned/transferred\\n            // out than the owner initially received through minting and transferring in.\\n            _balances[owner] -= 1;\\n        }\\n        delete _owners[tokenId];\\n\\n        emit Transfer(owner, address(0), tokenId);\\n\\n        _afterTokenTransfer(owner, address(0), tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Transfers `tokenId` from `from` to `to`.\\n     *  As opposed to {transferFrom}, this imposes no restrictions on msg.sender.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must be owned by `from`.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _transfer(address from, address to, uint256 tokenId) internal virtual {\\n        require(ERC721.ownerOf(tokenId) == from, \\\"ERC721: transfer from incorrect owner\\\");\\n        require(to != address(0), \\\"ERC721: transfer to the zero address\\\");\\n\\n        _beforeTokenTransfer(from, to, tokenId, 1);\\n\\n        // Check that tokenId was not transferred by `_beforeTokenTransfer` hook\\n        require(ERC721.ownerOf(tokenId) == from, \\\"ERC721: transfer from incorrect owner\\\");\\n\\n        // Clear approvals from the previous owner\\n        delete _tokenApprovals[tokenId];\\n\\n        unchecked {\\n            // `_balances[from]` cannot overflow for the same reason as described in `_burn`:\\n            // `from`'s balance is the number of token held, which is at least one before the current\\n            // transfer.\\n            // `_balances[to]` could overflow in the conditions described in `_mint`. That would require\\n            // all 2**256 token ids to be minted, which in practice is impossible.\\n            _balances[from] -= 1;\\n            _balances[to] += 1;\\n        }\\n        _owners[tokenId] = to;\\n\\n        emit Transfer(from, to, tokenId);\\n\\n        _afterTokenTransfer(from, to, tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Approve `to` to operate on `tokenId`\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function _approve(address to, uint256 tokenId) internal virtual {\\n        _tokenApprovals[tokenId] = to;\\n        emit Approval(ERC721.ownerOf(tokenId), to, tokenId);\\n    }\\n\\n    /**\\n     * @dev Approve `operator` to operate on all of `owner` tokens\\n     *\\n     * Emits an {ApprovalForAll} event.\\n     */\\n    function _setApprovalForAll(address owner, address operator, bool approved) internal virtual {\\n        require(owner != operator, \\\"ERC721: approve to caller\\\");\\n        _operatorApprovals[owner][operator] = approved;\\n        emit ApprovalForAll(owner, operator, approved);\\n    }\\n\\n    /**\\n     * @dev Reverts if the `tokenId` has not been minted yet.\\n     */\\n    function _requireMinted(uint256 tokenId) internal view virtual {\\n        require(_exists(tokenId), \\\"ERC721: invalid token ID\\\");\\n    }\\n\\n    /**\\n     * @dev Internal function to invoke {IERC721Receiver-onERC721Received} on a target address.\\n     * The call is not executed if the target address is not a contract.\\n     *\\n     * @param from address representing the previous owner of the given token ID\\n     * @param to target address that will receive the tokens\\n     * @param tokenId uint256 ID of the token to be transferred\\n     * @param data bytes optional data to send along with the call\\n     * @return bool whether the call correctly returned the expected magic value\\n     */\\n    function _checkOnERC721Received(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) private returns (bool) {\\n        if (to.isContract()) {\\n            try IERC721Receiver(to).onERC721Received(_msgSender(), from, tokenId, data) returns (bytes4 retval) {\\n                return retval == IERC721Receiver.onERC721Received.selector;\\n            } catch (bytes memory reason) {\\n                if (reason.length == 0) {\\n                    revert(\\\"ERC721: transfer to non ERC721Receiver implementer\\\");\\n                } else {\\n                    /// @solidity memory-safe-assembly\\n                    assembly {\\n                        revert(add(32, reason), mload(reason))\\n                    }\\n                }\\n            }\\n        } else {\\n            return true;\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called before any token transfer. This includes minting and burning. If {ERC721Consecutive} is\\n     * used, the hook may be called as part of a consecutive (batch) mint, as indicated by `batchSize` greater than 1.\\n     *\\n     * Calling conditions:\\n     *\\n     * - When `from` and `to` are both non-zero, ``from``'s tokens will be transferred to `to`.\\n     * - When `from` is zero, the tokens will be minted for `to`.\\n     * - When `to` is zero, ``from``'s tokens will be burned.\\n     * - `from` and `to` are never both zero.\\n     * - `batchSize` is non-zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _beforeTokenTransfer(address from, address to, uint256 firstTokenId, uint256 batchSize) internal virtual {}\\n\\n    /**\\n     * @dev Hook that is called after any token transfer. This includes minting and burning. If {ERC721Consecutive} is\\n     * used, the hook may be called as part of a consecutive (batch) mint, as indicated by `batchSize` greater than 1.\\n     *\\n     * Calling conditions:\\n     *\\n     * - When `from` and `to` are both non-zero, ``from``'s tokens were transferred to `to`.\\n     * - When `from` is zero, the tokens were minted for `to`.\\n     * - When `to` is zero, ``from``'s tokens were burned.\\n     * - `from` and `to` are never both zero.\\n     * - `batchSize` is non-zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _afterTokenTransfer(address from, address to, uint256 firstTokenId, uint256 batchSize) internal virtual {}\\n\\n    /**\\n     * @dev Unsafe write access to the balances, used by extensions that \\\"mint\\\" tokens using an {ownerOf} override.\\n     *\\n     * WARNING: Anyone calling this MUST ensure that the balances remain consistent with the ownership. The invariant\\n     * being that for any address `a` the value returned by `balanceOf(a)` must be equal to the number of tokens such\\n     * that `ownerOf(tokenId)` is `a`.\\n     */\\n    // solhint-disable-next-line func-name-mixedcase\\n    function __unsafe_increaseBalance(address account, uint256 amount) internal {\\n        _balances[account] += amount;\\n    }\\n}\\n\",\"keccak256\":\"0x2c309e7df9e05e6ce15bedfe74f3c61b467fc37e0fae9eab496acf5ea0bbd7ff\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/IERC721.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC721/IERC721.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../../utils/introspection/IERC165.sol\\\";\\n\\n/**\\n * @dev Required interface of an ERC721 compliant contract.\\n */\\ninterface IERC721 is IERC165 {\\n    /**\\n     * @dev Emitted when `tokenId` token is transferred from `from` to `to`.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables `approved` to manage the `tokenId` token.\\n     */\\n    event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets.\\n     */\\n    event ApprovalForAll(address indexed owner, address indexed operator, bool approved);\\n\\n    /**\\n     * @dev Returns the number of tokens in ``owner``'s account.\\n     */\\n    function balanceOf(address owner) external view returns (uint256 balance);\\n\\n    /**\\n     * @dev Returns the owner of the `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function ownerOf(uint256 tokenId) external view returns (address owner);\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(address from, address to, uint256 tokenId, bytes calldata data) external;\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients\\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must have been allowed to move this token by either {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(address from, address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Transfers `tokenId` token from `from` to `to`.\\n     *\\n     * WARNING: Note that the caller is responsible to confirm that the recipient is capable of receiving ERC721\\n     * or else they may be permanently lost. Usage of {safeTransferFrom} prevents loss, though the caller must\\n     * understand this adds an external call which potentially creates a reentrancy vulnerability.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must be owned by `from`.\\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Gives permission to `to` to transfer `tokenId` token to another account.\\n     * The approval is cleared when the token is transferred.\\n     *\\n     * Only a single account can be approved at a time, so approving the zero address clears previous approvals.\\n     *\\n     * Requirements:\\n     *\\n     * - The caller must own the token or be an approved operator.\\n     * - `tokenId` must exist.\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Approve or remove `operator` as an operator for the caller.\\n     * Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller.\\n     *\\n     * Requirements:\\n     *\\n     * - The `operator` cannot be the caller.\\n     *\\n     * Emits an {ApprovalForAll} event.\\n     */\\n    function setApprovalForAll(address operator, bool approved) external;\\n\\n    /**\\n     * @dev Returns the account approved for `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function getApproved(uint256 tokenId) external view returns (address operator);\\n\\n    /**\\n     * @dev Returns if the `operator` is allowed to manage all of the assets of `owner`.\\n     *\\n     * See {setApprovalForAll}\\n     */\\n    function isApprovedForAll(address owner, address operator) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x5bce51e11f7d194b79ea59fe00c9e8de9fa2c5530124960f29a24d4c740a3266\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC721/IERC721Receiver.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @title ERC721 token receiver interface\\n * @dev Interface for any contract that wants to support safeTransfers\\n * from ERC721 asset contracts.\\n */\\ninterface IERC721Receiver {\\n    /**\\n     * @dev Whenever an {IERC721} `tokenId` token is transferred to this contract via {IERC721-safeTransferFrom}\\n     * by `operator` from `from`, this function is called.\\n     *\\n     * It must return its Solidity selector to confirm the token transfer.\\n     * If any other value is returned or the interface is not implemented by the recipient, the transfer will be reverted.\\n     *\\n     * The selector can be obtained in Solidity with `IERC721Receiver.onERC721Received.selector`.\\n     */\\n    function onERC721Received(\\n        address operator,\\n        address from,\\n        uint256 tokenId,\\n        bytes calldata data\\n    ) external returns (bytes4);\\n}\\n\",\"keccak256\":\"0xa82b58eca1ee256be466e536706850163d2ec7821945abd6b4778cfb3bee37da\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC721/extensions/IERC721Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC721.sol\\\";\\n\\n/**\\n * @title ERC-721 Non-Fungible Token Standard, optional metadata extension\\n * @dev See https://eips.ethereum.org/EIPS/eip-721\\n */\\ninterface IERC721Metadata is IERC721 {\\n    /**\\n     * @dev Returns the token collection name.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the token collection symbol.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the Uniform Resource Identifier (URI) for `tokenId` token.\\n     */\\n    function tokenURI(uint256 tokenId) external view returns (string memory);\\n}\\n\",\"keccak256\":\"0x75b829ff2f26c14355d1cba20e16fe7b29ca58eb5fef665ede48bc0f9c6c74b9\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Address.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following\\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     *\\n     * Furthermore, `isContract` will also return true if the target contract within\\n     * the same transaction is already scheduled for destruction by `SELFDESTRUCT`,\\n     * which only has an effect at the end of a transaction.\\n     * ====\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n     *\\n     * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n     * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n     * constructor.\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // This method relies on extcodesize/address.code.length, which returns 0\\n        // for contracts in construction, since the code is only stored at the end\\n        // of the constructor execution.\\n\\n        return account.code.length > 0;\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://consensys.net/diligence/blog/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.8.0/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n\\n    /**\\n     * @dev Performs a Solidity function call using a low level `call`. A\\n     * plain `call` is an unsafe replacement for a function call: use this\\n     * function instead.\\n     *\\n     * If `target` reverts with a revert reason, it is bubbled up by this\\n     * function (like regular Solidity function calls).\\n     *\\n     * Returns the raw returned data. To convert to the expected return value,\\n     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n     *\\n     * Requirements:\\n     *\\n     * - `target` must be a contract.\\n     * - calling `target` with `data` must not revert.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n     * `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but also transferring `value` wei to `target`.\\n     *\\n     * Requirements:\\n     *\\n     * - the calling contract must have an ETH balance of at least `value`.\\n     * - the called Solidity function must be `payable`.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n     * with `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        require(address(this).balance >= value, \\\"Address: insufficient balance for call\\\");\\n        (bool success, bytes memory returndata) = target.call{value: value}(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n        return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.staticcall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.delegatecall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n     * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n     *\\n     * _Available since v4.8._\\n     */\\n    function verifyCallResultFromTarget(\\n        address target,\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        if (success) {\\n            if (returndata.length == 0) {\\n                // only check isContract if the call was successful and the return data is empty\\n                // otherwise we already know that it was a contract\\n                require(isContract(target), \\\"Address: call to non-contract\\\");\\n            }\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n     * revert reason or using the provided one.\\n     *\\n     * _Available since v4.3._\\n     */\\n    function verifyCallResult(\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal pure returns (bytes memory) {\\n        if (success) {\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n        // Look for revert reason and bubble it up if present\\n        if (returndata.length > 0) {\\n            // The easiest way to bubble the revert reason is using memory via assembly\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                let returndata_size := mload(returndata)\\n                revert(add(32, returndata), returndata_size)\\n            }\\n        } else {\\n            revert(errorMessage);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x006dd67219697fe68d7fbfdea512e7c4cb64a43565ed86171d67e844982da6fa\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.4) (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    function _contextSuffixLength() internal view virtual returns (uint256) {\\n        return 0;\\n    }\\n}\\n\",\"keccak256\":\"0xa92e4fa126feb6907daa0513ddd816b2eb91f30a808de54f63c17d0e162c3439\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Counters.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Counters.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @title Counters\\n * <AUTHOR> Condon (@shrugs)\\n * @dev Provides counters that can only be incremented, decremented or reset. This can be used e.g. to track the number\\n * of elements in a mapping, issuing ERC721 ids, or counting request ids.\\n *\\n * Include with `using Counters for Counters.Counter;`\\n */\\nlibrary Counters {\\n    struct Counter {\\n        // This variable should never be directly accessed by users of the library: interactions must be restricted to\\n        // the library's function. As of Solidity v0.5.2, this cannot be enforced, though there is a proposal to add\\n        // this feature: see https://github.com/ethereum/solidity/issues/4637\\n        uint256 _value; // default: 0\\n    }\\n\\n    function current(Counter storage counter) internal view returns (uint256) {\\n        return counter._value;\\n    }\\n\\n    function increment(Counter storage counter) internal {\\n        unchecked {\\n            counter._value += 1;\\n        }\\n    }\\n\\n    function decrement(Counter storage counter) internal {\\n        uint256 value = counter._value;\\n        require(value > 0, \\\"Counter: decrement overflow\\\");\\n        unchecked {\\n            counter._value = value - 1;\\n        }\\n    }\\n\\n    function reset(Counter storage counter) internal {\\n        counter._value = 0;\\n    }\\n}\\n\",\"keccak256\":\"0xf0018c2440fbe238dd3a8732fa8e17a0f9dce84d31451dc8a32f6d62b349c9f1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Strings.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (utils/Strings.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./math/Math.sol\\\";\\nimport \\\"./math/SignedMath.sol\\\";\\n\\n/**\\n * @dev String operations.\\n */\\nlibrary Strings {\\n    bytes16 private constant _SYMBOLS = \\\"0123456789abcdef\\\";\\n    uint8 private constant _ADDRESS_LENGTH = 20;\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` decimal representation.\\n     */\\n    function toString(uint256 value) internal pure returns (string memory) {\\n        unchecked {\\n            uint256 length = Math.log10(value) + 1;\\n            string memory buffer = new string(length);\\n            uint256 ptr;\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                ptr := add(buffer, add(32, length))\\n            }\\n            while (true) {\\n                ptr--;\\n                /// @solidity memory-safe-assembly\\n                assembly {\\n                    mstore8(ptr, byte(mod(value, 10), _SYMBOLS))\\n                }\\n                value /= 10;\\n                if (value == 0) break;\\n            }\\n            return buffer;\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts a `int256` to its ASCII `string` decimal representation.\\n     */\\n    function toString(int256 value) internal pure returns (string memory) {\\n        return string(abi.encodePacked(value < 0 ? \\\"-\\\" : \\\"\\\", toString(SignedMath.abs(value))));\\n    }\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation.\\n     */\\n    function toHexString(uint256 value) internal pure returns (string memory) {\\n        unchecked {\\n            return toHexString(value, Math.log256(value) + 1);\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation with fixed length.\\n     */\\n    function toHexString(uint256 value, uint256 length) internal pure returns (string memory) {\\n        bytes memory buffer = new bytes(2 * length + 2);\\n        buffer[0] = \\\"0\\\";\\n        buffer[1] = \\\"x\\\";\\n        for (uint256 i = 2 * length + 1; i > 1; --i) {\\n            buffer[i] = _SYMBOLS[value & 0xf];\\n            value >>= 4;\\n        }\\n        require(value == 0, \\\"Strings: hex length insufficient\\\");\\n        return string(buffer);\\n    }\\n\\n    /**\\n     * @dev Converts an `address` with fixed length of 20 bytes to its not checksummed ASCII `string` hexadecimal representation.\\n     */\\n    function toHexString(address addr) internal pure returns (string memory) {\\n        return toHexString(uint256(uint160(addr)), _ADDRESS_LENGTH);\\n    }\\n\\n    /**\\n     * @dev Returns true if the two strings are equal.\\n     */\\n    function equal(string memory a, string memory b) internal pure returns (bool) {\\n        return keccak256(bytes(a)) == keccak256(bytes(b));\\n    }\\n}\\n\",\"keccak256\":\"0x3088eb2868e8d13d89d16670b5f8612c4ab9ff8956272837d8e90106c59c14a0\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/ERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/ERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC165} interface.\\n *\\n * Contracts that want to implement ERC165 should inherit from this contract and override {supportsInterface} to check\\n * for the additional interface id that will be supported. For example:\\n *\\n * ```solidity\\n * function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n *     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId);\\n * }\\n * ```\\n *\\n * Alternatively, {ERC165Storage} provides an easier to use but more expensive implementation.\\n */\\nabstract contract ERC165 is IERC165 {\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n        return interfaceId == type(IERC165).interfaceId;\\n    }\\n}\\n\",\"keccak256\":\"0xd10975de010d89fd1c78dc5e8a9a7e7f496198085c151648f20cba166b32582b\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/Math.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (utils/math/Math.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Standard math utilities missing in the Solidity language.\\n */\\nlibrary Math {\\n    enum Rounding {\\n        Down, // Toward negative infinity\\n        Up, // Toward infinity\\n        Zero // Toward zero\\n    }\\n\\n    /**\\n     * @dev Returns the largest of two numbers.\\n     */\\n    function max(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return a > b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the smallest of two numbers.\\n     */\\n    function min(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return a < b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the average of two numbers. The result is rounded towards\\n     * zero.\\n     */\\n    function average(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b) / 2 can overflow.\\n        return (a & b) + (a ^ b) / 2;\\n    }\\n\\n    /**\\n     * @dev Returns the ceiling of the division of two numbers.\\n     *\\n     * This differs from standard division with `/` in that it rounds up instead\\n     * of rounding down.\\n     */\\n    function ceilDiv(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b - 1) / b can overflow on addition, so we distribute.\\n        return a == 0 ? 0 : (a - 1) / b + 1;\\n    }\\n\\n    /**\\n     * @notice Calculates floor(x * y / denominator) with full precision. Throws if result overflows a uint256 or denominator == 0\\n     * @dev Original credit to Remco Bloemen under MIT license (https://xn--2-umb.com/21/muldiv)\\n     * with further edits by Uniswap Labs also under MIT license.\\n     */\\n    function mulDiv(uint256 x, uint256 y, uint256 denominator) internal pure returns (uint256 result) {\\n        unchecked {\\n            // 512-bit multiply [prod1 prod0] = x * y. Compute the product mod 2^256 and mod 2^256 - 1, then use\\n            // use the Chinese Remainder Theorem to reconstruct the 512 bit result. The result is stored in two 256\\n            // variables such that product = prod1 * 2^256 + prod0.\\n            uint256 prod0; // Least significant 256 bits of the product\\n            uint256 prod1; // Most significant 256 bits of the product\\n            assembly {\\n                let mm := mulmod(x, y, not(0))\\n                prod0 := mul(x, y)\\n                prod1 := sub(sub(mm, prod0), lt(mm, prod0))\\n            }\\n\\n            // Handle non-overflow cases, 256 by 256 division.\\n            if (prod1 == 0) {\\n                // Solidity will revert if denominator == 0, unlike the div opcode on its own.\\n                // The surrounding unchecked block does not change this fact.\\n                // See https://docs.soliditylang.org/en/latest/control-structures.html#checked-or-unchecked-arithmetic.\\n                return prod0 / denominator;\\n            }\\n\\n            // Make sure the result is less than 2^256. Also prevents denominator == 0.\\n            require(denominator > prod1, \\\"Math: mulDiv overflow\\\");\\n\\n            ///////////////////////////////////////////////\\n            // 512 by 256 division.\\n            ///////////////////////////////////////////////\\n\\n            // Make division exact by subtracting the remainder from [prod1 prod0].\\n            uint256 remainder;\\n            assembly {\\n                // Compute remainder using mulmod.\\n                remainder := mulmod(x, y, denominator)\\n\\n                // Subtract 256 bit number from 512 bit number.\\n                prod1 := sub(prod1, gt(remainder, prod0))\\n                prod0 := sub(prod0, remainder)\\n            }\\n\\n            // Factor powers of two out of denominator and compute largest power of two divisor of denominator. Always >= 1.\\n            // See https://cs.stackexchange.com/q/138556/92363.\\n\\n            // Does not overflow because the denominator cannot be zero at this stage in the function.\\n            uint256 twos = denominator & (~denominator + 1);\\n            assembly {\\n                // Divide denominator by twos.\\n                denominator := div(denominator, twos)\\n\\n                // Divide [prod1 prod0] by twos.\\n                prod0 := div(prod0, twos)\\n\\n                // Flip twos such that it is 2^256 / twos. If twos is zero, then it becomes one.\\n                twos := add(div(sub(0, twos), twos), 1)\\n            }\\n\\n            // Shift in bits from prod1 into prod0.\\n            prod0 |= prod1 * twos;\\n\\n            // Invert denominator mod 2^256. Now that denominator is an odd number, it has an inverse modulo 2^256 such\\n            // that denominator * inv = 1 mod 2^256. Compute the inverse by starting with a seed that is correct for\\n            // four bits. That is, denominator * inv = 1 mod 2^4.\\n            uint256 inverse = (3 * denominator) ^ 2;\\n\\n            // Use the Newton-Raphson iteration to improve the precision. Thanks to Hensel's lifting lemma, this also works\\n            // in modular arithmetic, doubling the correct bits in each step.\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^8\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^16\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^32\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^64\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^128\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^256\\n\\n            // Because the division is now exact we can divide by multiplying with the modular inverse of denominator.\\n            // This will give us the correct result modulo 2^256. Since the preconditions guarantee that the outcome is\\n            // less than 2^256, this is the final result. We don't need to compute the high bits of the result and prod1\\n            // is no longer required.\\n            result = prod0 * inverse;\\n            return result;\\n        }\\n    }\\n\\n    /**\\n     * @notice Calculates x * y / denominator with full precision, following the selected rounding direction.\\n     */\\n    function mulDiv(uint256 x, uint256 y, uint256 denominator, Rounding rounding) internal pure returns (uint256) {\\n        uint256 result = mulDiv(x, y, denominator);\\n        if (rounding == Rounding.Up && mulmod(x, y, denominator) > 0) {\\n            result += 1;\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Returns the square root of a number. If the number is not a perfect square, the value is rounded down.\\n     *\\n     * Inspired by Henry S. Warren, Jr.'s \\\"Hacker's Delight\\\" (Chapter 11).\\n     */\\n    function sqrt(uint256 a) internal pure returns (uint256) {\\n        if (a == 0) {\\n            return 0;\\n        }\\n\\n        // For our first guess, we get the biggest power of 2 which is smaller than the square root of the target.\\n        //\\n        // We know that the \\\"msb\\\" (most significant bit) of our target number `a` is a power of 2 such that we have\\n        // `msb(a) <= a < 2*msb(a)`. This value can be written `msb(a)=2**k` with `k=log2(a)`.\\n        //\\n        // This can be rewritten `2**log2(a) <= a < 2**(log2(a) + 1)`\\n        // \\u2192 `sqrt(2**k) <= sqrt(a) < sqrt(2**(k+1))`\\n        // \\u2192 `2**(k/2) <= sqrt(a) < 2**((k+1)/2) <= 2**(k/2 + 1)`\\n        //\\n        // Consequently, `2**(log2(a) / 2)` is a good first approximation of `sqrt(a)` with at least 1 correct bit.\\n        uint256 result = 1 << (log2(a) >> 1);\\n\\n        // At this point `result` is an estimation with one bit of precision. We know the true value is a uint128,\\n        // since it is the square root of a uint256. Newton's method converges quadratically (precision doubles at\\n        // every iteration). We thus need at most 7 iteration to turn our partial result with one bit of precision\\n        // into the expected uint128 result.\\n        unchecked {\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            return min(result, a / result);\\n        }\\n    }\\n\\n    /**\\n     * @notice Calculates sqrt(a), following the selected rounding direction.\\n     */\\n    function sqrt(uint256 a, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = sqrt(a);\\n            return result + (rounding == Rounding.Up && result * result < a ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >> 128 > 0) {\\n                value >>= 128;\\n                result += 128;\\n            }\\n            if (value >> 64 > 0) {\\n                value >>= 64;\\n                result += 64;\\n            }\\n            if (value >> 32 > 0) {\\n                value >>= 32;\\n                result += 32;\\n            }\\n            if (value >> 16 > 0) {\\n                value >>= 16;\\n                result += 16;\\n            }\\n            if (value >> 8 > 0) {\\n                value >>= 8;\\n                result += 8;\\n            }\\n            if (value >> 4 > 0) {\\n                value >>= 4;\\n                result += 4;\\n            }\\n            if (value >> 2 > 0) {\\n                value >>= 2;\\n                result += 2;\\n            }\\n            if (value >> 1 > 0) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log2(value);\\n            return result + (rounding == Rounding.Up && 1 << result < value ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >= 10 ** 64) {\\n                value /= 10 ** 64;\\n                result += 64;\\n            }\\n            if (value >= 10 ** 32) {\\n                value /= 10 ** 32;\\n                result += 32;\\n            }\\n            if (value >= 10 ** 16) {\\n                value /= 10 ** 16;\\n                result += 16;\\n            }\\n            if (value >= 10 ** 8) {\\n                value /= 10 ** 8;\\n                result += 8;\\n            }\\n            if (value >= 10 ** 4) {\\n                value /= 10 ** 4;\\n                result += 4;\\n            }\\n            if (value >= 10 ** 2) {\\n                value /= 10 ** 2;\\n                result += 2;\\n            }\\n            if (value >= 10 ** 1) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log10(value);\\n            return result + (rounding == Rounding.Up && 10 ** result < value ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     *\\n     * Adding one to the result gives the number of pairs of hex symbols needed to represent `value` as a hex string.\\n     */\\n    function log256(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >> 128 > 0) {\\n                value >>= 128;\\n                result += 16;\\n            }\\n            if (value >> 64 > 0) {\\n                value >>= 64;\\n                result += 8;\\n            }\\n            if (value >> 32 > 0) {\\n                value >>= 32;\\n                result += 4;\\n            }\\n            if (value >> 16 > 0) {\\n                value >>= 16;\\n                result += 2;\\n            }\\n            if (value >> 8 > 0) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log256(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log256(value);\\n            return result + (rounding == Rounding.Up && 1 << (result << 3) < value ? 1 : 0);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xe4455ac1eb7fc497bb7402579e7b4d64d928b846fce7d2b6fde06d366f21c2b3\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/SignedMath.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/math/SignedMath.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Standard signed math utilities missing in the Solidity language.\\n */\\nlibrary SignedMath {\\n    /**\\n     * @dev Returns the largest of two signed numbers.\\n     */\\n    function max(int256 a, int256 b) internal pure returns (int256) {\\n        return a > b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the smallest of two signed numbers.\\n     */\\n    function min(int256 a, int256 b) internal pure returns (int256) {\\n        return a < b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the average of two signed numbers without overflow.\\n     * The result is rounded towards zero.\\n     */\\n    function average(int256 a, int256 b) internal pure returns (int256) {\\n        // Formula from the book \\\"Hacker's Delight\\\"\\n        int256 x = (a & b) + ((a ^ b) >> 1);\\n        return x + (int256(uint256(x) >> 255) & (a ^ b));\\n    }\\n\\n    /**\\n     * @dev Returns the absolute unsigned value of a signed value.\\n     */\\n    function abs(int256 n) internal pure returns (uint256) {\\n        unchecked {\\n            // must be unchecked in order to support `n = type(int256).min`\\n            return uint256(n >= 0 ? n : -n);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xf92515413956f529d95977adc9b0567d583c6203fc31ab1c23824c35187e3ddc\",\"license\":\"MIT\"},\"contracts/Assert.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\n\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts/utils/Strings.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\n/**\\n * Dynamic memory array implementation for AssertResult to facilitate unit test\\n * implementation.\\n */\\nlibrary List {\\n    struct ARList {\\n        Assert.AssertResult[] elements;\\n        uint num;\\n    }\\n\\n    function create(\\n        ARList memory _aList\\n    ) internal pure returns (ARList memory newList) {\\n        _aList.elements = new Assert.AssertResult[](4);\\n        _aList.num = 0;\\n        return _aList;\\n    }\\n\\n    function _resizeUp(\\n        ARList memory _aList\\n    ) internal pure returns (ARList memory) {\\n        ARList memory newList;\\n        newList.elements = new Assert.AssertResult[](\\n            (_aList.elements.length * 3) / 2\\n        );\\n        for (uint i = 0; i < _aList.elements.length; i++) {\\n            newList.elements[i] = _aList.elements[i];\\n        }\\n        newList.num = _aList.num;\\n        return newList;\\n    }\\n\\n    // Follow Solidity .pop() expectation to NOT return a value\\n    function pop(ARList memory _aList) internal pure returns (ARList memory) {\\n        _aList.num--;\\n        delete _aList.elements[_aList.num];\\n        return _aList;\\n    }\\n\\n    function push(\\n        ARList memory _aList,\\n        Assert.AssertResult memory _result\\n    ) internal pure returns (ARList memory) {\\n        if (_aList.num == _aList.elements.length) {\\n            _aList = _resizeUp(_aList);\\n        }\\n        _aList.elements[_aList.num] = _result;\\n        _aList.num++;\\n        return _aList;\\n    }\\n}\\n\\ncontract Assert {\\n    struct AssertResult {\\n        bool passed;\\n        string assertionError;\\n        string returnedAsString;\\n        string expectedAsString;\\n        string methodName;\\n    }\\n\\n    function _arrToString(\\n        uint[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(result, Strings.toString(arr[i]), \\\", \\\");\\n            } else {\\n                result = string.concat(result, Strings.toString(arr[i]), \\\"]\\\");\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function _arrToString(\\n        address[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(\\n                    result,\\n                    Strings.toHexString(uint160(arr[i]), 20),\\n                    \\\", \\\"\\n                );\\n            } else {\\n                result = string.concat(\\n                    result,\\n                    Strings.toHexString(uint160(arr[i]), 20),\\n                    \\\"]\\\"\\n                );\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function _arrToString(\\n        string[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(result, arr[i], \\\", \\\");\\n            } else {\\n                result = string.concat(result, arr[i], \\\"]\\\");\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function isTrue(bool a) public pure returns (AssertResult memory result) {\\n        result.passed = a;\\n        if (!result.passed) {\\n            result.assertionError = \\\"AssertionError: result is not true\\\";\\n        }\\n        result.returnedAsString = a == true ? \\\"true\\\" : \\\"false\\\";\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"isTrue\\\";\\n    }\\n\\n    function isFalse(bool a) public pure returns (AssertResult memory result) {\\n        result.passed = !a;\\n        if (!result.passed) {\\n            result.assertionError = \\\"AssertionError: result is not false\\\";\\n        }\\n        result.returnedAsString = a == true ? \\\"true\\\" : \\\"false\\\";\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"isFalse\\\";\\n    }\\n\\n    function equal(\\n        uint256 a,\\n        uint256 b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a == b);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                Strings.toString(a),\\n                \\\" is not equal to \\\",\\n                Strings.toString(b)\\n            );\\n        }\\n        result.returnedAsString = Strings.toString(a);\\n        result.expectedAsString = Strings.toString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // TODO\\n\\n    // function equal(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function equal(bool a, bool b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function equal(\\n        address a,\\n        address b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a == b);\\n        string memory aString = Strings.toHexString(uint160(a), 20);\\n        string memory bString = Strings.toHexString(uint160(b), 20);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                aString,\\n                \\\" is not equal to \\\",\\n                bString\\n            );\\n        }\\n        result.returnedAsString = aString;\\n        result.expectedAsString = bString;\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function equal(bytes32 a, bytes32 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function equal(\\n        bytes memory a,\\n        bytes memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(a) == keccak256(b);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                string(a),\\n                \\\" is not equal to \\\",\\n                string(b)\\n            );\\n        }\\n        result.returnedAsString = string(a);\\n        result.expectedAsString = string(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        string memory a,\\n        string memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(bytes(a)) == keccak256(bytes(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                a,\\n                \\\" is not equal to \\\",\\n                b\\n            );\\n        }\\n        result.returnedAsString = a;\\n        result.expectedAsString = b;\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        uint[] memory a,\\n        uint[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        address[] memory a,\\n        address[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        string[] memory a,\\n        string[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function notEqual(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(bool a, bool b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function notEqual(\\n        address a,\\n        address b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a != b);\\n        string memory aString = Strings.toHexString(uint160(a), 20);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                \\\" Both values are\\\",\\n                aString\\n            );\\n        }\\n        result.returnedAsString = aString;\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function notEqual(bytes32 a, bytes32 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(string memory a, string memory b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // /*----------------- Greater than --------------------*/\\n    // function greaterThan(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function greaterThan(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function greaterThan(uint256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    //   }\\n    // }\\n    // function greaterThan(int256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // /*----------------- Less than --------------------*/\\n    // function lessThan(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function lessThan(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function lessThan(uint256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function lessThan(int256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n}\\n\",\"keccak256\":\"0x0234e94dadaaac48d2ca78b33d75f44e1116becf391b6f17a3ca449ff836bbb7\",\"license\":\"UNLICENSED\"},\"contracts/Cafe.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\n// Cafe Version 0.2\\n\\n/**\\n * Cafe is a unit test framework designed to facilitate testing of contracts\\n * built against a defined specification.  It awards an NFT pin if the tests\\n * are passed.\\n *\\n * It works with Assert.sol to create a reasonably familiar system for writing\\n * tests.\\n */\\n\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts/token/ERC721/ERC721.sol\\\";\\nimport \\\"@openzeppelin/contracts/security/ReentrancyGuard.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/Counters.sol\\\";\\nimport \\\"@openzeppelin/contracts/access/Ownable.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\nimport \\\"./Assert.sol\\\";\\n\\ninterface ITest {\\n    function execute(\\n        address _submissionAddress\\n    ) external returns (Cafe.TestResult memory);\\n}\\n\\ninterface IDeploy {\\n    function deploy(\\n        address _submissionAddress\\n    ) external returns (Cafe.TestResult memory, address);\\n}\\n\\nabstract contract Cafe is ERC721, Ownable, ReentrancyGuard, Assert {\\n    error SoulboundToken();\\n    error NotActive();\\n\\n    using Counters for Counters.Counter;\\n    Counters.Counter public tokenIds;\\n\\n    mapping(address => bool) public owners;\\n    mapping(address => bool) submittedContracts;\\n\\n    bool active = true;\\n    string AllTokenURI = \\\"\\\";\\n\\n    event TestSuiteResult(\\n        address submission,\\n        bool passed,\\n        TestResult[] testResults\\n    );\\n\\n    struct TestResult {\\n        string message;\\n        List.ARList assertResults;\\n    }\\n\\n    ITest[] tests;\\n\\n    /**\\n     * Used by unit tests to indicate they contain a factory\\n     * Returns the **address** of the contract deployed by the factory\\n     * and the test result, to validate deployment or alert to failure\\n     *\\n     */\\n\\n    IDeploy deployer;\\n\\n    function testContract(address _submissionAddress) public testIsActive {\\n        // Declare here to +1 length if there is a deployer\\n        TestResult[] memory testResults;\\n        uint i = 0;\\n        address testAddress = _submissionAddress;\\n\\n        if (address(deployer) != address(0)) {\\n            testResults = new TestResult[](tests.length + 1);\\n            i++;\\n            // Use the address returned by the deployment for remaining tests\\n            (testResults[0], testAddress) = deployer.deploy(_submissionAddress);\\n        } else {\\n            testResults = new TestResult[](tests.length);\\n        }\\n\\n        for (i; i < tests.length; i++) {\\n            testResults[i] = tests[i].execute(testAddress);\\n        }\\n\\n        processResults(_submissionAddress, testResults);\\n    }\\n\\n    /**\\n     * Check each assert in each test to see if any failed.\\n     *\\n     * Note:  The check is here instead of setting a `passed` bool in\\n     * `TestResult` to reduce the amount of code in each unit test.\\n     */\\n    function checkIfAllPassed(\\n        TestResult[] memory _testResults\\n    ) public pure returns (bool) {\\n        for (uint i = 0; i < _testResults.length; i++) {\\n            for (uint k = 0; k < _testResults[i].assertResults.num; k++) {\\n                if (!_testResults[i].assertResults.elements[k].passed) {\\n                    return false;\\n                }\\n            }\\n        }\\n        return true;\\n    }\\n\\n    function processResults(\\n        address _submissionAddress,\\n        TestResult[] memory _testResults\\n    ) internal nonReentrant {\\n        bool passed = checkIfAllPassed(_testResults);\\n\\n        emit TestSuiteResult(_submissionAddress, passed, _testResults);\\n\\n        /**\\n         * Grant a soulbound NFT pin if:\\n         *  - This contract address has not been submitted before\\n         *  - The sender does not already own one of these pins\\n         *  - The contract submitted passes all unit tests\\n         */\\n        if (\\n            !submittedContracts[_submissionAddress] &&\\n            !owners[msg.sender] &&\\n            passed\\n        ) {\\n            tokenIds.increment();\\n            uint newId = tokenIds.current();\\n            owners[msg.sender] = true;\\n            _safeMint(msg.sender, newId);\\n        }\\n\\n        submittedContracts[_submissionAddress] = true;\\n    }\\n\\n    /**\\n     * Disallow transfers (Soulbound NFT)\\n     */\\n    function _beforeTokenTransfer(\\n        address _from,\\n        address,\\n        uint,\\n        uint\\n    ) internal pure override {\\n        if (_from != address(0)) {\\n            revert SoulboundToken();\\n        }\\n    }\\n\\n    function setActive(bool _setActiveTo) public onlyOwner {\\n        active = _setActiveTo;\\n    }\\n\\n    function setTokenURI(string memory _tokenURI) public onlyOwner {\\n        AllTokenURI = _tokenURI;\\n    }\\n\\n    function tokenURI(uint256) public view override returns (string memory) {\\n        return AllTokenURI;\\n    }\\n\\n    modifier testIsActive() {\\n        if (!active) {\\n            revert NotActive();\\n        }\\n        _;\\n    }\\n}\\n\",\"keccak256\":\"0x56b2acca754829febaf4743e6b51cba12de744e1db473ac37b5622eef0103ecf\",\"license\":\"UNLICENSED\"},\"contracts/ControlStructuresUT.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\npragma solidity ^0.8.23;\\n\\nimport \\\"@openzeppelin/contracts/token/ERC721/ERC721.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/Counters.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/Strings.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\nimport \\\"./Cafe.sol\\\";\\nimport \\\"./Assert.sol\\\";\\n\\nusing List for List.ARList;\\n\\ninterface IControlStructures {\\n    function fizzBuzz(uint _number) external returns (string memory);\\n\\n    function doNotDisturb(uint _time) external returns (string memory);\\n}\\n\\nlibrary Caller {\\n    function callRemoteFizzBuzz(\\n        IControlStructures _submission,\\n        uint _number\\n    ) internal returns (string memory, bool) {\\n        try _submission.fizzBuzz(_number) returns (string memory result) {\\n            return (result, false);\\n        } catch {\\n            return (\\\"\\\", true);\\n        }\\n    }\\n\\n    function callRemoteDoNotDisturb(\\n        IControlStructures _submission,\\n        uint _time\\n    ) internal returns (string memory, bool) {\\n        try _submission.doNotDisturb(_time) returns (string memory result) {\\n            return (result, false);\\n        } catch Error(string memory reason) {\\n            return (reason, true);\\n        } catch Panic(uint errorCode) {\\n            return (Strings.toString(errorCode), true);\\n        } catch (bytes memory lowLevelData) {\\n            return (string(lowLevelData), true);\\n        }\\n    }\\n}\\n\\ncontract testFizzBuzz is ITest, Assert {\\n    function execute(\\n        address _submissionAddress\\n    ) external override returns (Cafe.TestResult memory) {\\n        IControlStructures submission = IControlStructures(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult.message = \\\"Fizzbuzz should work with tradtional rules\\\";\\n        (string memory res, bool callError) = Caller.callRemoteFizzBuzz(\\n            submission,\\n            9\\n        );\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to fizzBuzz failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, \\\"Fizz\\\"));\\n        }\\n\\n        (res, callError) = Caller.callRemoteFizzBuzz(submission, 10);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to fizzBuzz failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, \\\"Buzz\\\"));\\n        }\\n\\n        (res, callError) = Caller.callRemoteFizzBuzz(submission, 15);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to fizzBuzz failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, \\\"FizzBuzz\\\"));\\n        }\\n\\n        (res, callError) = Caller.callRemoteFizzBuzz(submission, 11);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to fizzBuzz failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, \\\"Splat\\\"));\\n        }\\n        return testResult;\\n    }\\n}\\n\\ncontract testAfterHours is ITest, Assert {\\n    function execute(\\n        address _submissionAddress\\n    ) external override returns (Cafe.TestResult memory) {\\n        IControlStructures submission = IControlStructures(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult.message = \\\"AfterHours should revert and assert as described\\\";\\n        (string memory res, bool callError) = Caller.callRemoteDoNotDisturb(\\n            submission,\\n            2401\\n        );\\n        if (callError) {\\n            testResult.assertResults.push(Assert.equal(res, \\\"1\\\"));\\n        } else {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Transaction failed to panic\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        }\\n\\n        (res, callError) = Caller.callRemoteDoNotDisturb(submission, 2201);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.equal(\\n                    res,\\n                    string(abi.encodeWithSignature(\\\"AfterHours(uint256)\\\", 2201))\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Transaction failed to revert\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        }\\n\\n        (res, callError) = Caller.callRemoteDoNotDisturb(submission, 801);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to doNotDisturb failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, \\\"Morning!\\\"));\\n        }\\n\\n        (res, callError) = Caller.callRemoteDoNotDisturb(submission, 1450);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to doNotDisturb failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, \\\"Afternoon!\\\"));\\n        }\\n\\n        (res, callError) = Caller.callRemoteDoNotDisturb(submission, 1830);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to doNotDisturb failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, \\\"Evening!\\\"));\\n        }\\n\\n        return testResult;\\n    }\\n}\\n\\ncontract ControlStructuresUT is Cafe {\\n    constructor() ERC721(\\\"Control Structures Pin\\\", \\\"SCDCS\\\") {\\n        tests.push(new testFizzBuzz());\\n        tests.push(new testAfterHours());\\n    }\\n}\\n\",\"keccak256\":\"0xe4c67fcae86a01f7d7377a4bda38f9a14f893bdd1a6dd7b5a398e99778613103\",\"license\":\"UNLICENSED\"},\"hardhat/console.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity >=0.4.22 <0.9.0;\\n\\nlibrary console {\\n    address constant CONSOLE_ADDRESS =\\n        0x000000000000000000636F6e736F6c652e6c6f67;\\n\\n    function _sendLogPayloadImplementation(bytes memory payload) internal view {\\n        address consoleAddress = CONSOLE_ADDRESS;\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            pop(\\n                staticcall(\\n                    gas(),\\n                    consoleAddress,\\n                    add(payload, 32),\\n                    mload(payload),\\n                    0,\\n                    0\\n                )\\n            )\\n        }\\n    }\\n\\n    function _castToPure(\\n      function(bytes memory) internal view fnIn\\n    ) internal pure returns (function(bytes memory) pure fnOut) {\\n        assembly {\\n            fnOut := fnIn\\n        }\\n    }\\n\\n    function _sendLogPayload(bytes memory payload) internal pure {\\n        _castToPure(_sendLogPayloadImplementation)(payload);\\n    }\\n\\n    function log() internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log()\\\"));\\n    }\\n    function logInt(int256 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(int256)\\\", p0));\\n    }\\n\\n    function logUint(uint256 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256)\\\", p0));\\n    }\\n\\n    function logString(string memory p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string)\\\", p0));\\n    }\\n\\n    function logBool(bool p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool)\\\", p0));\\n    }\\n\\n    function logAddress(address p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address)\\\", p0));\\n    }\\n\\n    function logBytes(bytes memory p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes)\\\", p0));\\n    }\\n\\n    function logBytes1(bytes1 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes1)\\\", p0));\\n    }\\n\\n    function logBytes2(bytes2 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes2)\\\", p0));\\n    }\\n\\n    function logBytes3(bytes3 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes3)\\\", p0));\\n    }\\n\\n    function logBytes4(bytes4 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes4)\\\", p0));\\n    }\\n\\n    function logBytes5(bytes5 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes5)\\\", p0));\\n    }\\n\\n    function logBytes6(bytes6 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes6)\\\", p0));\\n    }\\n\\n    function logBytes7(bytes7 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes7)\\\", p0));\\n    }\\n\\n    function logBytes8(bytes8 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes8)\\\", p0));\\n    }\\n\\n    function logBytes9(bytes9 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes9)\\\", p0));\\n    }\\n\\n    function logBytes10(bytes10 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes10)\\\", p0));\\n    }\\n\\n    function logBytes11(bytes11 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes11)\\\", p0));\\n    }\\n\\n    function logBytes12(bytes12 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes12)\\\", p0));\\n    }\\n\\n    function logBytes13(bytes13 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes13)\\\", p0));\\n    }\\n\\n    function logBytes14(bytes14 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes14)\\\", p0));\\n    }\\n\\n    function logBytes15(bytes15 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes15)\\\", p0));\\n    }\\n\\n    function logBytes16(bytes16 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes16)\\\", p0));\\n    }\\n\\n    function logBytes17(bytes17 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes17)\\\", p0));\\n    }\\n\\n    function logBytes18(bytes18 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes18)\\\", p0));\\n    }\\n\\n    function logBytes19(bytes19 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes19)\\\", p0));\\n    }\\n\\n    function logBytes20(bytes20 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes20)\\\", p0));\\n    }\\n\\n    function logBytes21(bytes21 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes21)\\\", p0));\\n    }\\n\\n    function logBytes22(bytes22 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes22)\\\", p0));\\n    }\\n\\n    function logBytes23(bytes23 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes23)\\\", p0));\\n    }\\n\\n    function logBytes24(bytes24 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes24)\\\", p0));\\n    }\\n\\n    function logBytes25(bytes25 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes25)\\\", p0));\\n    }\\n\\n    function logBytes26(bytes26 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes26)\\\", p0));\\n    }\\n\\n    function logBytes27(bytes27 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes27)\\\", p0));\\n    }\\n\\n    function logBytes28(bytes28 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes28)\\\", p0));\\n    }\\n\\n    function logBytes29(bytes29 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes29)\\\", p0));\\n    }\\n\\n    function logBytes30(bytes30 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes30)\\\", p0));\\n    }\\n\\n    function logBytes31(bytes31 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes31)\\\", p0));\\n    }\\n\\n    function logBytes32(bytes32 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bytes32)\\\", p0));\\n    }\\n\\n    function log(uint256 p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256)\\\", p0));\\n    }\\n\\n    function log(string memory p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string)\\\", p0));\\n    }\\n\\n    function log(bool p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool)\\\", p0));\\n    }\\n\\n    function log(address p0) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address)\\\", p0));\\n    }\\n\\n    function log(uint256 p0, uint256 p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256)\\\", p0, p1));\\n    }\\n\\n    function log(uint256 p0, string memory p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string)\\\", p0, p1));\\n    }\\n\\n    function log(uint256 p0, bool p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool)\\\", p0, p1));\\n    }\\n\\n    function log(uint256 p0, address p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address)\\\", p0, p1));\\n    }\\n\\n    function log(string memory p0, uint256 p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256)\\\", p0, p1));\\n    }\\n\\n    function log(string memory p0, string memory p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string)\\\", p0, p1));\\n    }\\n\\n    function log(string memory p0, bool p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool)\\\", p0, p1));\\n    }\\n\\n    function log(string memory p0, address p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address)\\\", p0, p1));\\n    }\\n\\n    function log(bool p0, uint256 p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256)\\\", p0, p1));\\n    }\\n\\n    function log(bool p0, string memory p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string)\\\", p0, p1));\\n    }\\n\\n    function log(bool p0, bool p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool)\\\", p0, p1));\\n    }\\n\\n    function log(bool p0, address p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address)\\\", p0, p1));\\n    }\\n\\n    function log(address p0, uint256 p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256)\\\", p0, p1));\\n    }\\n\\n    function log(address p0, string memory p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string)\\\", p0, p1));\\n    }\\n\\n    function log(address p0, bool p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool)\\\", p0, p1));\\n    }\\n\\n    function log(address p0, address p1) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address)\\\", p0, p1));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, string memory p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, string memory p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, string memory p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, string memory p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, bool p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, bool p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, bool p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, bool p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, address p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, address p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, address p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, address p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, uint256 p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, uint256 p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, uint256 p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, uint256 p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, string memory p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, string memory p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, string memory p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, string memory p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, bool p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, bool p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, bool p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, bool p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, address p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, address p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, address p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(string memory p0, address p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, uint256 p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, uint256 p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, uint256 p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, uint256 p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, string memory p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, string memory p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, string memory p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, string memory p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, bool p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, bool p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, bool p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, bool p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, address p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, address p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, address p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(bool p0, address p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, uint256 p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, uint256 p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, uint256 p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, uint256 p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, string memory p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, string memory p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, string memory p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, string memory p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, bool p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, bool p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, bool p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, bool p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, address p1, uint256 p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, address p1, string memory p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, address p1, bool p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool)\\\", p0, p1, p2));\\n    }\\n\\n    function log(address p0, address p1, address p2) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address)\\\", p0, p1, p2));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, uint256 p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, string memory p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, bool p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(uint256 p0, address p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, uint256 p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, string memory p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, bool p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(string memory p0, address p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, uint256 p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, string memory p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, bool p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(bool p0, address p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, uint256 p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, string memory p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, bool p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, uint256 p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, uint256 p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, uint256 p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, uint256 p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, string memory p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, string memory p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, string memory p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, string memory p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, bool p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, bool p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, bool p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, bool p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, address p2, uint256 p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,uint256)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, address p2, string memory p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,string)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, address p2, bool p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,bool)\\\", p0, p1, p2, p3));\\n    }\\n\\n    function log(address p0, address p1, address p2, address p3) internal pure {\\n        _sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,address)\\\", p0, p1, p2, p3));\\n    }\\n\\n}\\n\",\"keccak256\":\"0x7434453e6d3b7d0e5d0eb7846ffdbc27f0ccf3b163591263739b628074dc103a\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"events": {"Approval(address,address,uint256)": {"details": "Emitted when `owner` enables `approved` to manage the `tokenId` token."}, "ApprovalForAll(address,address,bool)": {"details": "Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets."}, "Transfer(address,address,uint256)": {"details": "Emitted when `tokenId` token is transferred from `from` to `to`."}}, "kind": "dev", "methods": {"approve(address,uint256)": {"details": "See {IERC721-approve}."}, "balanceOf(address)": {"details": "See {IERC721-balanceOf}."}, "getApproved(uint256)": {"details": "See {IERC721-getApproved}."}, "isApprovedForAll(address,address)": {"details": "See {IERC721-isApprovedForAll}."}, "name()": {"details": "See {IERC721Metadata-name}."}, "owner()": {"details": "Returns the address of the current owner."}, "ownerOf(uint256)": {"details": "See {IERC721-ownerOf}."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "safeTransferFrom(address,address,uint256)": {"details": "See {IERC721-safeTransferFrom}."}, "safeTransferFrom(address,address,uint256,bytes)": {"details": "See {IERC721-safeTransferFrom}."}, "setApprovalForAll(address,bool)": {"details": "See {IERC721-setApprovalForAll}."}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}, "symbol()": {"details": "See {IERC721Metadata-symbol}."}, "transferFrom(address,address,uint256)": {"details": "See {IERC721-transferFrom}."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"checkIfAllPassed((string,((bool,string,string,string,string)[],uint256))[])": {"notice": "Check each assert in each test to see if any failed. Note:  The check is here instead of setting a `passed` bool in `TestResult` to reduce the amount of code in each unit test."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 893, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 895, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_symbol", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 899, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_owners", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 903, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_balances", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 907, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_tokenApprovals", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 913, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_operatorApprovals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_bool))"}, {"astId": 7, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_owner", "offset": 0, "slot": "6", "type": "t_address"}, {"astId": 123, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_status", "offset": 0, "slot": "7", "type": "t_uint256"}, {"astId": 6686, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "tokenIds", "offset": 0, "slot": "8", "type": "t_struct(Counter)2321_storage"}, {"astId": 6690, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "owners", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_bool)"}, {"astId": 6694, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "submittedContracts", "offset": 0, "slot": "10", "type": "t_mapping(t_address,t_bool)"}, {"astId": 6697, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "active", "offset": 0, "slot": "11", "type": "t_bool"}, {"astId": 6700, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "AllTokenURI", "offset": 0, "slot": "12", "type": "t_string_storage"}, {"astId": 6720, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "tests", "offset": 0, "slot": "13", "type": "t_array(t_contract(ITest)6656)dyn_storage"}, {"astId": 6724, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "deployer", "offset": 0, "slot": "14", "type": "t_contract(IDeploy)6667"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_contract(ITest)6656)dyn_storage": {"base": "t_contract(ITest)6656", "encoding": "dynamic_array", "label": "contract ITest[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_contract(IDeploy)6667": {"encoding": "inplace", "label": "contract IDeploy", "numberOfBytes": "20"}, "t_contract(ITest)6656": {"encoding": "inplace", "label": "contract ITest", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_address,t_mapping(t_address,t_bool))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => bool))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_bool)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Counter)2321_storage": {"encoding": "inplace", "label": "struct Counters.Counter", "members": [{"astId": 2320, "contract": "contracts/ControlStructuresUT.sol:ControlStructuresUT", "label": "_value", "offset": 0, "slot": "0", "type": "t_uint256"}], "numberOfBytes": "32"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}