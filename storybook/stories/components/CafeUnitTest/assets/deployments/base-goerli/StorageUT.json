{"address": "0x805712508445AC8883C048B639D7eAdF33C1EE35", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "NotActive", "type": "error"}, {"inputs": [], "name": "SoulboundToken", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "approved", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "submission", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "passed", "type": "bool"}, {"components": [{"internalType": "string", "name": "message", "type": "string"}, {"components": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult[]", "name": "elements", "type": "tuple[]"}, {"internalType": "uint256", "name": "num", "type": "uint256"}], "internalType": "struct List.ARList", "name": "assertResults", "type": "tuple"}], "indexed": false, "internalType": "struct Cafe.TestResult[]", "name": "testResults", "type": "tuple[]"}], "name": "TestSuiteResult", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "string", "name": "message", "type": "string"}, {"components": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult[]", "name": "elements", "type": "tuple[]"}, {"internalType": "uint256", "name": "num", "type": "uint256"}], "internalType": "struct List.ARList", "name": "assertResults", "type": "tuple"}], "internalType": "struct Cafe.TestResult[]", "name": "_testResults", "type": "tuple[]"}], "name": "checkIfAllPassed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "a", "type": "bytes"}, {"internalType": "bytes", "name": "b", "type": "bytes"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "address", "name": "b", "type": "address"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "string", "name": "a", "type": "string"}, {"internalType": "string", "name": "b", "type": "string"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "a", "type": "string[]"}, {"internalType": "string[]", "name": "b", "type": "string[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "a", "type": "address[]"}, {"internalType": "address[]", "name": "b", "type": "address[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "a", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "b", "type": "uint256[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "a", "type": "bool"}], "name": "isFalse", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "a", "type": "bool"}], "name": "isTrue", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "address", "name": "b", "type": "address"}], "name": "notEqual", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "owners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_setActiveTo", "type": "bool"}], "name": "setActive", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_tokenURI", "type": "string"}], "name": "setTokenURI", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_submissionAddress", "type": "address"}], "name": "testContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0x0175510d7641feaac65afef47151eaf175f2ea5522a2f310f0944df3dd9c89c7", "receipt": {"to": null, "from": "0x0919C594E549545374772246B0D433a4988A0eC9", "contractAddress": "0x805712508445AC8883C048B639D7eAdF33C1EE35", "transactionIndex": 3, "gasUsed": "10288391", "logsBloom": "0x00000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000020000280000000000000840000000000000000000000000000000400000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000040000000000000000000000000000000000000000000000000000", "blockHash": "0x7e1bb9f134a3b3a357c3cb80c087e08df8f543704bc88034da993895d6b52745", "transactionHash": "0x0175510d7641feaac65afef47151eaf175f2ea5522a2f310f0944df3dd9c89c7", "logs": [{"transactionIndex": 3, "blockNumber": 4536863, "transactionHash": "0x0175510d7641feaac65afef47151eaf175f2ea5522a2f310f0944df3dd9c89c7", "address": "0x805712508445AC8883C048B639D7eAdF33C1EE35", "topics": ["0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x0000000000000000000000000919c594e549545374772246b0d433a4988a0ec9"], "data": "0x", "logIndex": 4, "blockHash": "0x7e1bb9f134a3b3a357c3cb80c087e08df8f543704bc88034da993895d6b52745"}], "blockNumber": 4536863, "cumulativeGasUsed": "10602492", "status": 1, "byzantium": true}, "args": [], "numDeployments": 2, "solcInputHash": "cce5e99710aedb2b7694c5fe3ee6e1a0", "metadata": "{\"compiler\":{\"version\":\"0.8.17+commit.8df45f5f\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"NotActive\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SoulboundToken\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"submission\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"string\",\"name\":\"message\",\"type\":\"string\"},{\"components\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult[]\",\"name\":\"elements\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"internalType\":\"struct List.ARList\",\"name\":\"assertResults\",\"type\":\"tuple\"}],\"indexed\":false,\"internalType\":\"struct Cafe.TestResult[]\",\"name\":\"testResults\",\"type\":\"tuple[]\"}],\"name\":\"TestSuiteResult\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"message\",\"type\":\"string\"},{\"components\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult[]\",\"name\":\"elements\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"internalType\":\"struct List.ARList\",\"name\":\"assertResults\",\"type\":\"tuple\"}],\"internalType\":\"struct Cafe.TestResult[]\",\"name\":\"_testResults\",\"type\":\"tuple[]\"}],\"name\":\"checkIfAllPassed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"a\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"b\",\"type\":\"bytes\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"b\",\"type\":\"uint256\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"a\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"b\",\"type\":\"address\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"a\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"b\",\"type\":\"string\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"a\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"b\",\"type\":\"string[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"a\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"b\",\"type\":\"address[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"a\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"b\",\"type\":\"uint256[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"a\",\"type\":\"bool\"}],\"name\":\"isFalse\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"a\",\"type\":\"bool\"}],\"name\":\"isTrue\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"a\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"b\",\"type\":\"address\"}],\"name\":\"notEqual\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"owners\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_setActiveTo\",\"type\":\"bool\"}],\"name\":\"setActive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_tokenURI\",\"type\":\"string\"}],\"name\":\"setTokenURI\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_submissionAddress\",\"type\":\"address\"}],\"name\":\"testContract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"approve(address,uint256)\":{\"details\":\"See {IERC721-approve}.\"},\"balanceOf(address)\":{\"details\":\"See {IERC721-balanceOf}.\"},\"getApproved(uint256)\":{\"details\":\"See {IERC721-getApproved}.\"},\"isApprovedForAll(address,address)\":{\"details\":\"See {IERC721-isApprovedForAll}.\"},\"name()\":{\"details\":\"See {IERC721Metadata-name}.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"ownerOf(uint256)\":{\"details\":\"See {IERC721-ownerOf}.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions anymore. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby removing any functionality that is only available to the owner.\"},\"safeTransferFrom(address,address,uint256)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"safeTransferFrom(address,address,uint256,bytes)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"setApprovalForAll(address,bool)\":{\"details\":\"See {IERC721-setApprovalForAll}.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"},\"symbol()\":{\"details\":\"See {IERC721Metadata-symbol}.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC721-transferFrom}.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"checkIfAllPassed((string,((bool,string,string,string,string)[],uint256))[])\":{\"notice\":\"Check each assert in each test to see if any failed. Note:  The check is here instead of setting a `passed` bool in `TestResult` to reduce the amount of code in each unit test.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/StorageUnitTest.sol\":\"StorageUT\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":1000},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.7.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../utils/Context.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * By default, the owner account will be the one that deploys the contract. This\\n * can later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract Ownable is Context {\\n    address private _owner;\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial owner.\\n     */\\n    constructor() {\\n        _transferOwnership(_msgSender());\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        require(owner() == _msgSender(), \\\"Ownable: caller is not the owner\\\");\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions anymore. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby removing any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        require(newOwner != address(0), \\\"Ownable: new owner is the zero address\\\");\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xa94b34880e3c1b0b931662cb1c09e5dfa6662f31cba80e07c5ee71cd135c9673\",\"license\":\"MIT\"},\"@openzeppelin/contracts/security/ReentrancyGuard.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (security/ReentrancyGuard.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Contract module that helps prevent reentrant calls to a function.\\n *\\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\\n * available, which can be applied to functions to make sure there are no nested\\n * (reentrant) calls to them.\\n *\\n * Note that because there is a single `nonReentrant` guard, functions marked as\\n * `nonReentrant` may not call one another. This can be worked around by making\\n * those functions `private`, and then adding `external` `nonReentrant` entry\\n * points to them.\\n *\\n * TIP: If you would like to learn more about reentrancy and alternative ways\\n * to protect against it, check out our blog post\\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\\n */\\nabstract contract ReentrancyGuard {\\n    // Booleans are more expensive than uint256 or any type that takes up a full\\n    // word because each write operation emits an extra SLOAD to first read the\\n    // slot's contents, replace the bits taken up by the boolean, and then write\\n    // back. This is the compiler's defense against contract upgrades and\\n    // pointer aliasing, and it cannot be disabled.\\n\\n    // The values being non-zero value makes deployment a bit more expensive,\\n    // but in exchange the refund on every call to nonReentrant will be lower in\\n    // amount. Since refunds are capped to a percentage of the total\\n    // transaction's gas, it is best to keep them low in cases like this one, to\\n    // increase the likelihood of the full refund coming into effect.\\n    uint256 private constant _NOT_ENTERED = 1;\\n    uint256 private constant _ENTERED = 2;\\n\\n    uint256 private _status;\\n\\n    constructor() {\\n        _status = _NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and making it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        _nonReentrantBefore();\\n        _;\\n        _nonReentrantAfter();\\n    }\\n\\n    function _nonReentrantBefore() private {\\n        // On the first call to nonReentrant, _status will be _NOT_ENTERED\\n        require(_status != _ENTERED, \\\"ReentrancyGuard: reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        _status = _ENTERED;\\n    }\\n\\n    function _nonReentrantAfter() private {\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        _status = _NOT_ENTERED;\\n    }\\n}\\n\",\"keccak256\":\"0x190dd6f8d592b7e4e930feb7f4313aeb8e1c4ad3154c27ce1cf6a512fc30d8cc\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/ERC721.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC721/ERC721.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC721.sol\\\";\\nimport \\\"./IERC721Receiver.sol\\\";\\nimport \\\"./extensions/IERC721Metadata.sol\\\";\\nimport \\\"../../utils/Address.sol\\\";\\nimport \\\"../../utils/Context.sol\\\";\\nimport \\\"../../utils/Strings.sol\\\";\\nimport \\\"../../utils/introspection/ERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of https://eips.ethereum.org/EIPS/eip-721[ERC721] Non-Fungible Token Standard, including\\n * the Metadata extension, but not including the Enumerable extension, which is available separately as\\n * {ERC721Enumerable}.\\n */\\ncontract ERC721 is Context, ERC165, IERC721, IERC721Metadata {\\n    using Address for address;\\n    using Strings for uint256;\\n\\n    // Token name\\n    string private _name;\\n\\n    // Token symbol\\n    string private _symbol;\\n\\n    // Mapping from token ID to owner address\\n    mapping(uint256 => address) private _owners;\\n\\n    // Mapping owner address to token count\\n    mapping(address => uint256) private _balances;\\n\\n    // Mapping from token ID to approved address\\n    mapping(uint256 => address) private _tokenApprovals;\\n\\n    // Mapping from owner to operator approvals\\n    mapping(address => mapping(address => bool)) private _operatorApprovals;\\n\\n    /**\\n     * @dev Initializes the contract by setting a `name` and a `symbol` to the token collection.\\n     */\\n    constructor(string memory name_, string memory symbol_) {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override(ERC165, IERC165) returns (bool) {\\n        return\\n            interfaceId == type(IERC721).interfaceId ||\\n            interfaceId == type(IERC721Metadata).interfaceId ||\\n            super.supportsInterface(interfaceId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-balanceOf}.\\n     */\\n    function balanceOf(address owner) public view virtual override returns (uint256) {\\n        require(owner != address(0), \\\"ERC721: address zero is not a valid owner\\\");\\n        return _balances[owner];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-ownerOf}.\\n     */\\n    function ownerOf(uint256 tokenId) public view virtual override returns (address) {\\n        address owner = _ownerOf(tokenId);\\n        require(owner != address(0), \\\"ERC721: invalid token ID\\\");\\n        return owner;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-name}.\\n     */\\n    function name() public view virtual override returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-symbol}.\\n     */\\n    function symbol() public view virtual override returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-tokenURI}.\\n     */\\n    function tokenURI(uint256 tokenId) public view virtual override returns (string memory) {\\n        _requireMinted(tokenId);\\n\\n        string memory baseURI = _baseURI();\\n        return bytes(baseURI).length > 0 ? string(abi.encodePacked(baseURI, tokenId.toString())) : \\\"\\\";\\n    }\\n\\n    /**\\n     * @dev Base URI for computing {tokenURI}. If set, the resulting URI for each\\n     * token will be the concatenation of the `baseURI` and the `tokenId`. Empty\\n     * by default, can be overridden in child contracts.\\n     */\\n    function _baseURI() internal view virtual returns (string memory) {\\n        return \\\"\\\";\\n    }\\n\\n    /**\\n     * @dev See {IERC721-approve}.\\n     */\\n    function approve(address to, uint256 tokenId) public virtual override {\\n        address owner = ERC721.ownerOf(tokenId);\\n        require(to != owner, \\\"ERC721: approval to current owner\\\");\\n\\n        require(\\n            _msgSender() == owner || isApprovedForAll(owner, _msgSender()),\\n            \\\"ERC721: approve caller is not token owner or approved for all\\\"\\n        );\\n\\n        _approve(to, tokenId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-getApproved}.\\n     */\\n    function getApproved(uint256 tokenId) public view virtual override returns (address) {\\n        _requireMinted(tokenId);\\n\\n        return _tokenApprovals[tokenId];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-setApprovalForAll}.\\n     */\\n    function setApprovalForAll(address operator, bool approved) public virtual override {\\n        _setApprovalForAll(_msgSender(), operator, approved);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-isApprovedForAll}.\\n     */\\n    function isApprovedForAll(address owner, address operator) public view virtual override returns (bool) {\\n        return _operatorApprovals[owner][operator];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-transferFrom}.\\n     */\\n    function transferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) public virtual override {\\n        //solhint-disable-next-line max-line-length\\n        require(_isApprovedOrOwner(_msgSender(), tokenId), \\\"ERC721: caller is not token owner or approved\\\");\\n\\n        _transfer(from, to, tokenId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-safeTransferFrom}.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) public virtual override {\\n        safeTransferFrom(from, to, tokenId, \\\"\\\");\\n    }\\n\\n    /**\\n     * @dev See {IERC721-safeTransferFrom}.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) public virtual override {\\n        require(_isApprovedOrOwner(_msgSender(), tokenId), \\\"ERC721: caller is not token owner or approved\\\");\\n        _safeTransfer(from, to, tokenId, data);\\n    }\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients\\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\\n     *\\n     * `data` is additional data, it has no specified format and it is sent in call to `to`.\\n     *\\n     * This internal function is equivalent to {safeTransferFrom}, and can be used to e.g.\\n     * implement alternative mechanisms to perform token transfer, such as signature-based.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _safeTransfer(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) internal virtual {\\n        _transfer(from, to, tokenId);\\n        require(_checkOnERC721Received(from, to, tokenId, data), \\\"ERC721: transfer to non ERC721Receiver implementer\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the owner of the `tokenId`. Does NOT revert if token doesn't exist\\n     */\\n    function _ownerOf(uint256 tokenId) internal view virtual returns (address) {\\n        return _owners[tokenId];\\n    }\\n\\n    /**\\n     * @dev Returns whether `tokenId` exists.\\n     *\\n     * Tokens can be managed by their owner or approved accounts via {approve} or {setApprovalForAll}.\\n     *\\n     * Tokens start existing when they are minted (`_mint`),\\n     * and stop existing when they are burned (`_burn`).\\n     */\\n    function _exists(uint256 tokenId) internal view virtual returns (bool) {\\n        return _ownerOf(tokenId) != address(0);\\n    }\\n\\n    /**\\n     * @dev Returns whether `spender` is allowed to manage `tokenId`.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function _isApprovedOrOwner(address spender, uint256 tokenId) internal view virtual returns (bool) {\\n        address owner = ERC721.ownerOf(tokenId);\\n        return (spender == owner || isApprovedForAll(owner, spender) || getApproved(tokenId) == spender);\\n    }\\n\\n    /**\\n     * @dev Safely mints `tokenId` and transfers it to `to`.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must not exist.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _safeMint(address to, uint256 tokenId) internal virtual {\\n        _safeMint(to, tokenId, \\\"\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-ERC721-_safeMint-address-uint256-}[`_safeMint`], with an additional `data` parameter which is\\n     * forwarded in {IERC721Receiver-onERC721Received} to contract recipients.\\n     */\\n    function _safeMint(\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) internal virtual {\\n        _mint(to, tokenId);\\n        require(\\n            _checkOnERC721Received(address(0), to, tokenId, data),\\n            \\\"ERC721: transfer to non ERC721Receiver implementer\\\"\\n        );\\n    }\\n\\n    /**\\n     * @dev Mints `tokenId` and transfers it to `to`.\\n     *\\n     * WARNING: Usage of this method is discouraged, use {_safeMint} whenever possible\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must not exist.\\n     * - `to` cannot be the zero address.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _mint(address to, uint256 tokenId) internal virtual {\\n        require(to != address(0), \\\"ERC721: mint to the zero address\\\");\\n        require(!_exists(tokenId), \\\"ERC721: token already minted\\\");\\n\\n        _beforeTokenTransfer(address(0), to, tokenId, 1);\\n\\n        // Check that tokenId was not minted by `_beforeTokenTransfer` hook\\n        require(!_exists(tokenId), \\\"ERC721: token already minted\\\");\\n\\n        unchecked {\\n            // Will not overflow unless all 2**256 token ids are minted to the same owner.\\n            // Given that tokens are minted one by one, it is impossible in practice that\\n            // this ever happens. Might change if we allow batch minting.\\n            // The ERC fails to describe this case.\\n            _balances[to] += 1;\\n        }\\n\\n        _owners[tokenId] = to;\\n\\n        emit Transfer(address(0), to, tokenId);\\n\\n        _afterTokenTransfer(address(0), to, tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Destroys `tokenId`.\\n     * The approval is cleared when the token is burned.\\n     * This is an internal function that does not check if the sender is authorized to operate on the token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _burn(uint256 tokenId) internal virtual {\\n        address owner = ERC721.ownerOf(tokenId);\\n\\n        _beforeTokenTransfer(owner, address(0), tokenId, 1);\\n\\n        // Update ownership in case tokenId was transferred by `_beforeTokenTransfer` hook\\n        owner = ERC721.ownerOf(tokenId);\\n\\n        // Clear approvals\\n        delete _tokenApprovals[tokenId];\\n\\n        unchecked {\\n            // Cannot overflow, as that would require more tokens to be burned/transferred\\n            // out than the owner initially received through minting and transferring in.\\n            _balances[owner] -= 1;\\n        }\\n        delete _owners[tokenId];\\n\\n        emit Transfer(owner, address(0), tokenId);\\n\\n        _afterTokenTransfer(owner, address(0), tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Transfers `tokenId` from `from` to `to`.\\n     *  As opposed to {transferFrom}, this imposes no restrictions on msg.sender.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must be owned by `from`.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _transfer(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) internal virtual {\\n        require(ERC721.ownerOf(tokenId) == from, \\\"ERC721: transfer from incorrect owner\\\");\\n        require(to != address(0), \\\"ERC721: transfer to the zero address\\\");\\n\\n        _beforeTokenTransfer(from, to, tokenId, 1);\\n\\n        // Check that tokenId was not transferred by `_beforeTokenTransfer` hook\\n        require(ERC721.ownerOf(tokenId) == from, \\\"ERC721: transfer from incorrect owner\\\");\\n\\n        // Clear approvals from the previous owner\\n        delete _tokenApprovals[tokenId];\\n\\n        unchecked {\\n            // `_balances[from]` cannot overflow for the same reason as described in `_burn`:\\n            // `from`'s balance is the number of token held, which is at least one before the current\\n            // transfer.\\n            // `_balances[to]` could overflow in the conditions described in `_mint`. That would require\\n            // all 2**256 token ids to be minted, which in practice is impossible.\\n            _balances[from] -= 1;\\n            _balances[to] += 1;\\n        }\\n        _owners[tokenId] = to;\\n\\n        emit Transfer(from, to, tokenId);\\n\\n        _afterTokenTransfer(from, to, tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Approve `to` to operate on `tokenId`\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function _approve(address to, uint256 tokenId) internal virtual {\\n        _tokenApprovals[tokenId] = to;\\n        emit Approval(ERC721.ownerOf(tokenId), to, tokenId);\\n    }\\n\\n    /**\\n     * @dev Approve `operator` to operate on all of `owner` tokens\\n     *\\n     * Emits an {ApprovalForAll} event.\\n     */\\n    function _setApprovalForAll(\\n        address owner,\\n        address operator,\\n        bool approved\\n    ) internal virtual {\\n        require(owner != operator, \\\"ERC721: approve to caller\\\");\\n        _operatorApprovals[owner][operator] = approved;\\n        emit ApprovalForAll(owner, operator, approved);\\n    }\\n\\n    /**\\n     * @dev Reverts if the `tokenId` has not been minted yet.\\n     */\\n    function _requireMinted(uint256 tokenId) internal view virtual {\\n        require(_exists(tokenId), \\\"ERC721: invalid token ID\\\");\\n    }\\n\\n    /**\\n     * @dev Internal function to invoke {IERC721Receiver-onERC721Received} on a target address.\\n     * The call is not executed if the target address is not a contract.\\n     *\\n     * @param from address representing the previous owner of the given token ID\\n     * @param to target address that will receive the tokens\\n     * @param tokenId uint256 ID of the token to be transferred\\n     * @param data bytes optional data to send along with the call\\n     * @return bool whether the call correctly returned the expected magic value\\n     */\\n    function _checkOnERC721Received(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) private returns (bool) {\\n        if (to.isContract()) {\\n            try IERC721Receiver(to).onERC721Received(_msgSender(), from, tokenId, data) returns (bytes4 retval) {\\n                return retval == IERC721Receiver.onERC721Received.selector;\\n            } catch (bytes memory reason) {\\n                if (reason.length == 0) {\\n                    revert(\\\"ERC721: transfer to non ERC721Receiver implementer\\\");\\n                } else {\\n                    /// @solidity memory-safe-assembly\\n                    assembly {\\n                        revert(add(32, reason), mload(reason))\\n                    }\\n                }\\n            }\\n        } else {\\n            return true;\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called before any token transfer. This includes minting and burning. If {ERC721Consecutive} is\\n     * used, the hook may be called as part of a consecutive (batch) mint, as indicated by `batchSize` greater than 1.\\n     *\\n     * Calling conditions:\\n     *\\n     * - When `from` and `to` are both non-zero, ``from``'s tokens will be transferred to `to`.\\n     * - When `from` is zero, the tokens will be minted for `to`.\\n     * - When `to` is zero, ``from``'s tokens will be burned.\\n     * - `from` and `to` are never both zero.\\n     * - `batchSize` is non-zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _beforeTokenTransfer(\\n        address from,\\n        address to,\\n        uint256, /* firstTokenId */\\n        uint256 batchSize\\n    ) internal virtual {\\n        if (batchSize > 1) {\\n            if (from != address(0)) {\\n                _balances[from] -= batchSize;\\n            }\\n            if (to != address(0)) {\\n                _balances[to] += batchSize;\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called after any token transfer. This includes minting and burning. If {ERC721Consecutive} is\\n     * used, the hook may be called as part of a consecutive (batch) mint, as indicated by `batchSize` greater than 1.\\n     *\\n     * Calling conditions:\\n     *\\n     * - When `from` and `to` are both non-zero, ``from``'s tokens were transferred to `to`.\\n     * - When `from` is zero, the tokens were minted for `to`.\\n     * - When `to` is zero, ``from``'s tokens were burned.\\n     * - `from` and `to` are never both zero.\\n     * - `batchSize` is non-zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _afterTokenTransfer(\\n        address from,\\n        address to,\\n        uint256 firstTokenId,\\n        uint256 batchSize\\n    ) internal virtual {}\\n}\\n\",\"keccak256\":\"0xd89f3585b211fc9e3408384a4c4efdc3a93b2f877a3821046fa01c219d35be1b\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/IERC721.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC721/IERC721.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../../utils/introspection/IERC165.sol\\\";\\n\\n/**\\n * @dev Required interface of an ERC721 compliant contract.\\n */\\ninterface IERC721 is IERC165 {\\n    /**\\n     * @dev Emitted when `tokenId` token is transferred from `from` to `to`.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables `approved` to manage the `tokenId` token.\\n     */\\n    event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets.\\n     */\\n    event ApprovalForAll(address indexed owner, address indexed operator, bool approved);\\n\\n    /**\\n     * @dev Returns the number of tokens in ``owner``'s account.\\n     */\\n    function balanceOf(address owner) external view returns (uint256 balance);\\n\\n    /**\\n     * @dev Returns the owner of the `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function ownerOf(uint256 tokenId) external view returns (address owner);\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes calldata data\\n    ) external;\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients\\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must have been allowed to move this token by either {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) external;\\n\\n    /**\\n     * @dev Transfers `tokenId` token from `from` to `to`.\\n     *\\n     * WARNING: Note that the caller is responsible to confirm that the recipient is capable of receiving ERC721\\n     * or else they may be permanently lost. Usage of {safeTransferFrom} prevents loss, though the caller must\\n     * understand this adds an external call which potentially creates a reentrancy vulnerability.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must be owned by `from`.\\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) external;\\n\\n    /**\\n     * @dev Gives permission to `to` to transfer `tokenId` token to another account.\\n     * The approval is cleared when the token is transferred.\\n     *\\n     * Only a single account can be approved at a time, so approving the zero address clears previous approvals.\\n     *\\n     * Requirements:\\n     *\\n     * - The caller must own the token or be an approved operator.\\n     * - `tokenId` must exist.\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Approve or remove `operator` as an operator for the caller.\\n     * Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller.\\n     *\\n     * Requirements:\\n     *\\n     * - The `operator` cannot be the caller.\\n     *\\n     * Emits an {ApprovalForAll} event.\\n     */\\n    function setApprovalForAll(address operator, bool _approved) external;\\n\\n    /**\\n     * @dev Returns the account approved for `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function getApproved(uint256 tokenId) external view returns (address operator);\\n\\n    /**\\n     * @dev Returns if the `operator` is allowed to manage all of the assets of `owner`.\\n     *\\n     * See {setApprovalForAll}\\n     */\\n    function isApprovedForAll(address owner, address operator) external view returns (bool);\\n}\\n\",\"keccak256\":\"0xab28a56179c1db258c9bf5235b382698cb650debecb51b23d12be9e241374b68\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC721/IERC721Receiver.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @title ERC721 token receiver interface\\n * @dev Interface for any contract that wants to support safeTransfers\\n * from ERC721 asset contracts.\\n */\\ninterface IERC721Receiver {\\n    /**\\n     * @dev Whenever an {IERC721} `tokenId` token is transferred to this contract via {IERC721-safeTransferFrom}\\n     * by `operator` from `from`, this function is called.\\n     *\\n     * It must return its Solidity selector to confirm the token transfer.\\n     * If any other value is returned or the interface is not implemented by the recipient, the transfer will be reverted.\\n     *\\n     * The selector can be obtained in Solidity with `IERC721Receiver.onERC721Received.selector`.\\n     */\\n    function onERC721Received(\\n        address operator,\\n        address from,\\n        uint256 tokenId,\\n        bytes calldata data\\n    ) external returns (bytes4);\\n}\\n\",\"keccak256\":\"0xa82b58eca1ee256be466e536706850163d2ec7821945abd6b4778cfb3bee37da\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC721/extensions/IERC721Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC721.sol\\\";\\n\\n/**\\n * @title ERC-721 Non-Fungible Token Standard, optional metadata extension\\n * @dev See https://eips.ethereum.org/EIPS/eip-721\\n */\\ninterface IERC721Metadata is IERC721 {\\n    /**\\n     * @dev Returns the token collection name.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the token collection symbol.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the Uniform Resource Identifier (URI) for `tokenId` token.\\n     */\\n    function tokenURI(uint256 tokenId) external view returns (string memory);\\n}\\n\",\"keccak256\":\"0x75b829ff2f26c14355d1cba20e16fe7b29ca58eb5fef665ede48bc0f9c6c74b9\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Address.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following\\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     * ====\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n     *\\n     * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n     * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n     * constructor.\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // This method relies on extcodesize/address.code.length, which returns 0\\n        // for contracts in construction, since the code is only stored at the end\\n        // of the constructor execution.\\n\\n        return account.code.length > 0;\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n\\n    /**\\n     * @dev Performs a Solidity function call using a low level `call`. A\\n     * plain `call` is an unsafe replacement for a function call: use this\\n     * function instead.\\n     *\\n     * If `target` reverts with a revert reason, it is bubbled up by this\\n     * function (like regular Solidity function calls).\\n     *\\n     * Returns the raw returned data. To convert to the expected return value,\\n     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n     *\\n     * Requirements:\\n     *\\n     * - `target` must be a contract.\\n     * - calling `target` with `data` must not revert.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n     * `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but also transferring `value` wei to `target`.\\n     *\\n     * Requirements:\\n     *\\n     * - the calling contract must have an ETH balance of at least `value`.\\n     * - the called Solidity function must be `payable`.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n     * with `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        require(address(this).balance >= value, \\\"Address: insufficient balance for call\\\");\\n        (bool success, bytes memory returndata) = target.call{value: value}(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n        return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.staticcall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.delegatecall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n     * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n     *\\n     * _Available since v4.8._\\n     */\\n    function verifyCallResultFromTarget(\\n        address target,\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        if (success) {\\n            if (returndata.length == 0) {\\n                // only check isContract if the call was successful and the return data is empty\\n                // otherwise we already know that it was a contract\\n                require(isContract(target), \\\"Address: call to non-contract\\\");\\n            }\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n     * revert reason or using the provided one.\\n     *\\n     * _Available since v4.3._\\n     */\\n    function verifyCallResult(\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal pure returns (bytes memory) {\\n        if (success) {\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n        // Look for revert reason and bubble it up if present\\n        if (returndata.length > 0) {\\n            // The easiest way to bubble the revert reason is using memory via assembly\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                let returndata_size := mload(returndata)\\n                revert(add(32, returndata), returndata_size)\\n            }\\n        } else {\\n            revert(errorMessage);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xf96f969e24029d43d0df89e59d365f277021dac62b48e1c1e3ebe0acdd7f1ca1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n}\\n\",\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Counters.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Counters.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @title Counters\\n * <AUTHOR> Condon (@shrugs)\\n * @dev Provides counters that can only be incremented, decremented or reset. This can be used e.g. to track the number\\n * of elements in a mapping, issuing ERC721 ids, or counting request ids.\\n *\\n * Include with `using Counters for Counters.Counter;`\\n */\\nlibrary Counters {\\n    struct Counter {\\n        // This variable should never be directly accessed by users of the library: interactions must be restricted to\\n        // the library's function. As of Solidity v0.5.2, this cannot be enforced, though there is a proposal to add\\n        // this feature: see https://github.com/ethereum/solidity/issues/4637\\n        uint256 _value; // default: 0\\n    }\\n\\n    function current(Counter storage counter) internal view returns (uint256) {\\n        return counter._value;\\n    }\\n\\n    function increment(Counter storage counter) internal {\\n        unchecked {\\n            counter._value += 1;\\n        }\\n    }\\n\\n    function decrement(Counter storage counter) internal {\\n        uint256 value = counter._value;\\n        require(value > 0, \\\"Counter: decrement overflow\\\");\\n        unchecked {\\n            counter._value = value - 1;\\n        }\\n    }\\n\\n    function reset(Counter storage counter) internal {\\n        counter._value = 0;\\n    }\\n}\\n\",\"keccak256\":\"0xf0018c2440fbe238dd3a8732fa8e17a0f9dce84d31451dc8a32f6d62b349c9f1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Strings.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/Strings.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./math/Math.sol\\\";\\n\\n/**\\n * @dev String operations.\\n */\\nlibrary Strings {\\n    bytes16 private constant _SYMBOLS = \\\"0123456789abcdef\\\";\\n    uint8 private constant _ADDRESS_LENGTH = 20;\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` decimal representation.\\n     */\\n    function toString(uint256 value) internal pure returns (string memory) {\\n        unchecked {\\n            uint256 length = Math.log10(value) + 1;\\n            string memory buffer = new string(length);\\n            uint256 ptr;\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                ptr := add(buffer, add(32, length))\\n            }\\n            while (true) {\\n                ptr--;\\n                /// @solidity memory-safe-assembly\\n                assembly {\\n                    mstore8(ptr, byte(mod(value, 10), _SYMBOLS))\\n                }\\n                value /= 10;\\n                if (value == 0) break;\\n            }\\n            return buffer;\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation.\\n     */\\n    function toHexString(uint256 value) internal pure returns (string memory) {\\n        unchecked {\\n            return toHexString(value, Math.log256(value) + 1);\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation with fixed length.\\n     */\\n    function toHexString(uint256 value, uint256 length) internal pure returns (string memory) {\\n        bytes memory buffer = new bytes(2 * length + 2);\\n        buffer[0] = \\\"0\\\";\\n        buffer[1] = \\\"x\\\";\\n        for (uint256 i = 2 * length + 1; i > 1; --i) {\\n            buffer[i] = _SYMBOLS[value & 0xf];\\n            value >>= 4;\\n        }\\n        require(value == 0, \\\"Strings: hex length insufficient\\\");\\n        return string(buffer);\\n    }\\n\\n    /**\\n     * @dev Converts an `address` with fixed length of 20 bytes to its not checksummed ASCII `string` hexadecimal representation.\\n     */\\n    function toHexString(address addr) internal pure returns (string memory) {\\n        return toHexString(uint256(uint160(addr)), _ADDRESS_LENGTH);\\n    }\\n}\\n\",\"keccak256\":\"0xa4d1d62251f8574deb032a35fc948386a9b4de74b812d4f545a1ac120486b48a\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/ERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/ERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC165} interface.\\n *\\n * Contracts that want to implement ERC165 should inherit from this contract and override {supportsInterface} to check\\n * for the additional interface id that will be supported. For example:\\n *\\n * ```solidity\\n * function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n *     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId);\\n * }\\n * ```\\n *\\n * Alternatively, {ERC165Storage} provides an easier to use but more expensive implementation.\\n */\\nabstract contract ERC165 is IERC165 {\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n        return interfaceId == type(IERC165).interfaceId;\\n    }\\n}\\n\",\"keccak256\":\"0xd10975de010d89fd1c78dc5e8a9a7e7f496198085c151648f20cba166b32582b\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/Math.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/math/Math.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Standard math utilities missing in the Solidity language.\\n */\\nlibrary Math {\\n    enum Rounding {\\n        Down, // Toward negative infinity\\n        Up, // Toward infinity\\n        Zero // Toward zero\\n    }\\n\\n    /**\\n     * @dev Returns the largest of two numbers.\\n     */\\n    function max(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return a > b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the smallest of two numbers.\\n     */\\n    function min(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return a < b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the average of two numbers. The result is rounded towards\\n     * zero.\\n     */\\n    function average(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b) / 2 can overflow.\\n        return (a & b) + (a ^ b) / 2;\\n    }\\n\\n    /**\\n     * @dev Returns the ceiling of the division of two numbers.\\n     *\\n     * This differs from standard division with `/` in that it rounds up instead\\n     * of rounding down.\\n     */\\n    function ceilDiv(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b - 1) / b can overflow on addition, so we distribute.\\n        return a == 0 ? 0 : (a - 1) / b + 1;\\n    }\\n\\n    /**\\n     * @notice Calculates floor(x * y / denominator) with full precision. Throws if result overflows a uint256 or denominator == 0\\n     * @dev Original credit to Remco Bloemen under MIT license (https://xn--2-umb.com/21/muldiv)\\n     * with further edits by Uniswap Labs also under MIT license.\\n     */\\n    function mulDiv(\\n        uint256 x,\\n        uint256 y,\\n        uint256 denominator\\n    ) internal pure returns (uint256 result) {\\n        unchecked {\\n            // 512-bit multiply [prod1 prod0] = x * y. Compute the product mod 2^256 and mod 2^256 - 1, then use\\n            // use the Chinese Remainder Theorem to reconstruct the 512 bit result. The result is stored in two 256\\n            // variables such that product = prod1 * 2^256 + prod0.\\n            uint256 prod0; // Least significant 256 bits of the product\\n            uint256 prod1; // Most significant 256 bits of the product\\n            assembly {\\n                let mm := mulmod(x, y, not(0))\\n                prod0 := mul(x, y)\\n                prod1 := sub(sub(mm, prod0), lt(mm, prod0))\\n            }\\n\\n            // Handle non-overflow cases, 256 by 256 division.\\n            if (prod1 == 0) {\\n                return prod0 / denominator;\\n            }\\n\\n            // Make sure the result is less than 2^256. Also prevents denominator == 0.\\n            require(denominator > prod1);\\n\\n            ///////////////////////////////////////////////\\n            // 512 by 256 division.\\n            ///////////////////////////////////////////////\\n\\n            // Make division exact by subtracting the remainder from [prod1 prod0].\\n            uint256 remainder;\\n            assembly {\\n                // Compute remainder using mulmod.\\n                remainder := mulmod(x, y, denominator)\\n\\n                // Subtract 256 bit number from 512 bit number.\\n                prod1 := sub(prod1, gt(remainder, prod0))\\n                prod0 := sub(prod0, remainder)\\n            }\\n\\n            // Factor powers of two out of denominator and compute largest power of two divisor of denominator. Always >= 1.\\n            // See https://cs.stackexchange.com/q/138556/92363.\\n\\n            // Does not overflow because the denominator cannot be zero at this stage in the function.\\n            uint256 twos = denominator & (~denominator + 1);\\n            assembly {\\n                // Divide denominator by twos.\\n                denominator := div(denominator, twos)\\n\\n                // Divide [prod1 prod0] by twos.\\n                prod0 := div(prod0, twos)\\n\\n                // Flip twos such that it is 2^256 / twos. If twos is zero, then it becomes one.\\n                twos := add(div(sub(0, twos), twos), 1)\\n            }\\n\\n            // Shift in bits from prod1 into prod0.\\n            prod0 |= prod1 * twos;\\n\\n            // Invert denominator mod 2^256. Now that denominator is an odd number, it has an inverse modulo 2^256 such\\n            // that denominator * inv = 1 mod 2^256. Compute the inverse by starting with a seed that is correct for\\n            // four bits. That is, denominator * inv = 1 mod 2^4.\\n            uint256 inverse = (3 * denominator) ^ 2;\\n\\n            // Use the Newton-Raphson iteration to improve the precision. Thanks to Hensel's lifting lemma, this also works\\n            // in modular arithmetic, doubling the correct bits in each step.\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^8\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^16\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^32\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^64\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^128\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^256\\n\\n            // Because the division is now exact we can divide by multiplying with the modular inverse of denominator.\\n            // This will give us the correct result modulo 2^256. Since the preconditions guarantee that the outcome is\\n            // less than 2^256, this is the final result. We don't need to compute the high bits of the result and prod1\\n            // is no longer required.\\n            result = prod0 * inverse;\\n            return result;\\n        }\\n    }\\n\\n    /**\\n     * @notice Calculates x * y / denominator with full precision, following the selected rounding direction.\\n     */\\n    function mulDiv(\\n        uint256 x,\\n        uint256 y,\\n        uint256 denominator,\\n        Rounding rounding\\n    ) internal pure returns (uint256) {\\n        uint256 result = mulDiv(x, y, denominator);\\n        if (rounding == Rounding.Up && mulmod(x, y, denominator) > 0) {\\n            result += 1;\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Returns the square root of a number. If the number is not a perfect square, the value is rounded down.\\n     *\\n     * Inspired by Henry S. Warren, Jr.'s \\\"Hacker's Delight\\\" (Chapter 11).\\n     */\\n    function sqrt(uint256 a) internal pure returns (uint256) {\\n        if (a == 0) {\\n            return 0;\\n        }\\n\\n        // For our first guess, we get the biggest power of 2 which is smaller than the square root of the target.\\n        //\\n        // We know that the \\\"msb\\\" (most significant bit) of our target number `a` is a power of 2 such that we have\\n        // `msb(a) <= a < 2*msb(a)`. This value can be written `msb(a)=2**k` with `k=log2(a)`.\\n        //\\n        // This can be rewritten `2**log2(a) <= a < 2**(log2(a) + 1)`\\n        // \\u2192 `sqrt(2**k) <= sqrt(a) < sqrt(2**(k+1))`\\n        // \\u2192 `2**(k/2) <= sqrt(a) < 2**((k+1)/2) <= 2**(k/2 + 1)`\\n        //\\n        // Consequently, `2**(log2(a) / 2)` is a good first approximation of `sqrt(a)` with at least 1 correct bit.\\n        uint256 result = 1 << (log2(a) >> 1);\\n\\n        // At this point `result` is an estimation with one bit of precision. We know the true value is a uint128,\\n        // since it is the square root of a uint256. Newton's method converges quadratically (precision doubles at\\n        // every iteration). We thus need at most 7 iteration to turn our partial result with one bit of precision\\n        // into the expected uint128 result.\\n        unchecked {\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            return min(result, a / result);\\n        }\\n    }\\n\\n    /**\\n     * @notice Calculates sqrt(a), following the selected rounding direction.\\n     */\\n    function sqrt(uint256 a, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = sqrt(a);\\n            return result + (rounding == Rounding.Up && result * result < a ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >> 128 > 0) {\\n                value >>= 128;\\n                result += 128;\\n            }\\n            if (value >> 64 > 0) {\\n                value >>= 64;\\n                result += 64;\\n            }\\n            if (value >> 32 > 0) {\\n                value >>= 32;\\n                result += 32;\\n            }\\n            if (value >> 16 > 0) {\\n                value >>= 16;\\n                result += 16;\\n            }\\n            if (value >> 8 > 0) {\\n                value >>= 8;\\n                result += 8;\\n            }\\n            if (value >> 4 > 0) {\\n                value >>= 4;\\n                result += 4;\\n            }\\n            if (value >> 2 > 0) {\\n                value >>= 2;\\n                result += 2;\\n            }\\n            if (value >> 1 > 0) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log2(value);\\n            return result + (rounding == Rounding.Up && 1 << result < value ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >= 10**64) {\\n                value /= 10**64;\\n                result += 64;\\n            }\\n            if (value >= 10**32) {\\n                value /= 10**32;\\n                result += 32;\\n            }\\n            if (value >= 10**16) {\\n                value /= 10**16;\\n                result += 16;\\n            }\\n            if (value >= 10**8) {\\n                value /= 10**8;\\n                result += 8;\\n            }\\n            if (value >= 10**4) {\\n                value /= 10**4;\\n                result += 4;\\n            }\\n            if (value >= 10**2) {\\n                value /= 10**2;\\n                result += 2;\\n            }\\n            if (value >= 10**1) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log10(value);\\n            return result + (rounding == Rounding.Up && 10**result < value ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     *\\n     * Adding one to the result gives the number of pairs of hex symbols needed to represent `value` as a hex string.\\n     */\\n    function log256(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >> 128 > 0) {\\n                value >>= 128;\\n                result += 16;\\n            }\\n            if (value >> 64 > 0) {\\n                value >>= 64;\\n                result += 8;\\n            }\\n            if (value >> 32 > 0) {\\n                value >>= 32;\\n                result += 4;\\n            }\\n            if (value >> 16 > 0) {\\n                value >>= 16;\\n                result += 2;\\n            }\\n            if (value >> 8 > 0) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log256(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log256(value);\\n            return result + (rounding == Rounding.Up && 1 << (result * 8) < value ? 1 : 0);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xa1e8e83cd0087785df04ac79fb395d9f3684caeaf973d9e2c71caef723a3a5d6\",\"license\":\"MIT\"},\"contracts/Assert.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\n\\npragma solidity >=0.8.17 <0.9.0;\\n\\nimport \\\"@openzeppelin/contracts/utils/Strings.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\n/**\\n * Dynamic memory array implementation for AssertResult to facilitate unit test\\n * implementation.\\n */\\nlibrary List {\\n    struct ARList {\\n        Assert.AssertResult[] elements;\\n        uint num;\\n    }\\n\\n    function create(\\n        ARList memory _aList\\n    ) internal pure returns (ARList memory newList) {\\n        _aList.elements = new Assert.AssertResult[](4);\\n        _aList.num = 0;\\n        return _aList;\\n    }\\n\\n    function _resizeUp(\\n        ARList memory _aList\\n    ) internal pure returns (ARList memory) {\\n        ARList memory newList;\\n        newList.elements = new Assert.AssertResult[](\\n            (_aList.elements.length * 3) / 2\\n        );\\n        for (uint i = 0; i < _aList.elements.length; i++) {\\n            newList.elements[i] = _aList.elements[i];\\n        }\\n        newList.num = _aList.num;\\n        return newList;\\n    }\\n\\n    // Follow Solidity .pop() expectation to NOT return a value\\n    function pop(ARList memory _aList) internal pure returns (ARList memory) {\\n        _aList.num--;\\n        delete _aList.elements[_aList.num];\\n        return _aList;\\n    }\\n\\n    function push(\\n        ARList memory _aList,\\n        Assert.AssertResult memory _result\\n    ) internal pure returns (ARList memory) {\\n        if (_aList.num == _aList.elements.length) {\\n            _aList = _resizeUp(_aList);\\n        }\\n        _aList.elements[_aList.num] = _result;\\n        _aList.num++;\\n        return _aList;\\n    }\\n}\\n\\ncontract Assert {\\n    struct AssertResult {\\n        bool passed;\\n        string assertionError;\\n        string returnedAsString;\\n        string expectedAsString;\\n        string methodName;\\n    }\\n\\n    function _arrToString(\\n        uint[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(result, Strings.toString(arr[i]), \\\", \\\");\\n            } else {\\n                result = string.concat(result, Strings.toString(arr[i]), \\\"]\\\");\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function _arrToString(\\n        address[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(\\n                    result,\\n                    Strings.toHexString(uint160(arr[i]), 20),\\n                    \\\", \\\"\\n                );\\n            } else {\\n                result = string.concat(\\n                    result,\\n                    Strings.toHexString(uint160(arr[i]), 20),\\n                    \\\"]\\\"\\n                );\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function _arrToString(\\n        string[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(result, arr[i], \\\", \\\");\\n            } else {\\n                result = string.concat(result, arr[i], \\\"]\\\");\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function isTrue(bool a) public pure returns (AssertResult memory result) {\\n        result.passed = a;\\n        if (!result.passed) {\\n            result.assertionError = \\\"AssertionError: result is not true\\\";\\n        }\\n        result.returnedAsString = a == true ? \\\"true\\\" : \\\"false\\\";\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"isTrue\\\";\\n    }\\n\\n    function isFalse(bool a) public pure returns (AssertResult memory result) {\\n        result.passed = !a;\\n        if (!result.passed) {\\n            result.assertionError = \\\"AssertionError: result is not false\\\";\\n        }\\n        result.returnedAsString = a == true ? \\\"true\\\" : \\\"false\\\";\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"isFalse\\\";\\n    }\\n\\n    function equal(\\n        uint256 a,\\n        uint256 b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a == b);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                Strings.toString(a),\\n                \\\" is not equal to \\\",\\n                Strings.toString(b)\\n            );\\n        }\\n        result.returnedAsString = Strings.toString(a);\\n        result.expectedAsString = Strings.toString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // TODO\\n\\n    // function equal(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function equal(bool a, bool b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function equal(\\n        address a,\\n        address b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a == b);\\n        string memory aString = Strings.toHexString(uint160(a), 20);\\n        string memory bString = Strings.toHexString(uint160(b), 20);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                aString,\\n                \\\" is not equal to \\\",\\n                bString\\n            );\\n        }\\n        result.returnedAsString = aString;\\n        result.expectedAsString = bString;\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function equal(bytes32 a, bytes32 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function equal(\\n        bytes memory a,\\n        bytes memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(a) == keccak256(b);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                string(a),\\n                \\\" is not equal to \\\",\\n                string(b)\\n            );\\n        }\\n        result.returnedAsString = string(a);\\n        result.expectedAsString = string(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        string memory a,\\n        string memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(bytes(a)) == keccak256(bytes(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                a,\\n                \\\" is not equal to \\\",\\n                b\\n            );\\n        }\\n        result.returnedAsString = a;\\n        result.expectedAsString = b;\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        uint[] memory a,\\n        uint[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        address[] memory a,\\n        address[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        string[] memory a,\\n        string[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function notEqual(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(bool a, bool b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function notEqual(\\n        address a,\\n        address b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a != b);\\n        string memory aString = Strings.toHexString(uint160(a), 20);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                \\\" Both values are\\\",\\n                aString\\n            );\\n        }\\n        result.returnedAsString = aString;\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function notEqual(bytes32 a, bytes32 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(string memory a, string memory b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // /*----------------- Greater than --------------------*/\\n    // function greaterThan(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function greaterThan(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function greaterThan(uint256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    //   }\\n    // }\\n    // function greaterThan(int256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // /*----------------- Less than --------------------*/\\n    // function lessThan(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function lessThan(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function lessThan(uint256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function lessThan(int256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n}\\n\",\"keccak256\":\"0xbc7342fffc4408c65f4ed955bfd992a6dc231c9df74cd8a78ba46af2e5bbbdb5\",\"license\":\"UNLICENSED\"},\"contracts/Cafe.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\n// Cafe Version 0.2\\n\\n/**\\n * Cafe is a unit test framework designed to facilitate testing of contracts\\n * built against a defined specification.  It awards an NFT pin if the tests\\n * are passed.\\n *\\n * It works with Assert.sol to create a reasonably familiar system for writing\\n * tests.\\n */\\n\\npragma solidity ^0.8.17;\\n\\nimport \\\"@openzeppelin/contracts/token/ERC721/ERC721.sol\\\";\\nimport \\\"@openzeppelin/contracts/security/ReentrancyGuard.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/Counters.sol\\\";\\nimport \\\"@openzeppelin/contracts/access/Ownable.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\nimport \\\"./Assert.sol\\\";\\n\\ninterface ITest {\\n    function execute(\\n        address _submissionAddress\\n    ) external returns (Cafe.TestResult memory);\\n}\\n\\ninterface IDeploy {\\n    function deploy(\\n        address _submissionAddress\\n    ) external returns (Cafe.TestResult memory, address);\\n}\\n\\nabstract contract Cafe is ERC721, Ownable, ReentrancyGuard, Assert {\\n    error SoulboundToken();\\n    error NotActive();\\n\\n    using Counters for Counters.Counter;\\n    Counters.Counter private tokenIds;\\n\\n    mapping(address => bool) public owners;\\n    mapping(address => bool) submittedContracts;\\n\\n    bool active = true;\\n    string AllTokenURI = \\\"\\\";\\n\\n    event TestSuiteResult(\\n        address submission,\\n        bool passed,\\n        TestResult[] testResults\\n    );\\n\\n    struct TestResult {\\n        string message;\\n        List.ARList assertResults;\\n    }\\n\\n    ITest[] tests;\\n\\n    /**\\n     * Used by unit tests to indicate they contain a factory\\n     * Returns the **address** of the contract deployed by the factory\\n     * and the test result, to validate deployment or alert to failure\\n     *\\n     */\\n\\n    IDeploy deployer;\\n\\n    function testContract(address _submissionAddress) public testIsActive {\\n        // Declare here to +1 length if there is a deployer\\n        TestResult[] memory testResults;\\n        uint i = 0;\\n        address testAddress = _submissionAddress;\\n\\n        if (address(deployer) != address(0)) {\\n            testResults = new TestResult[](tests.length + 1);\\n            i++;\\n            // Use the address returned by the deployment for remaining tests\\n            (testResults[0], testAddress) = deployer.deploy(_submissionAddress);\\n        } else {\\n            testResults = new TestResult[](tests.length);\\n        }\\n\\n        for (i; i < tests.length; i++) {\\n            testResults[i] = tests[i].execute(testAddress);\\n        }\\n\\n        processResults(_submissionAddress, testResults);\\n    }\\n\\n    /**\\n     * Check each assert in each test to see if any failed.\\n     *\\n     * Note:  The check is here instead of setting a `passed` bool in\\n     * `TestResult` to reduce the amount of code in each unit test.\\n     */\\n    function checkIfAllPassed(\\n        TestResult[] memory _testResults\\n    ) public pure returns (bool) {\\n        for (uint i = 0; i < _testResults.length; i++) {\\n            for (uint k = 0; k < _testResults[i].assertResults.num; k++) {\\n                if (!_testResults[i].assertResults.elements[k].passed) {\\n                    return false;\\n                }\\n            }\\n        }\\n        return true;\\n    }\\n\\n    function processResults(\\n        address _submissionAddress,\\n        TestResult[] memory _testResults\\n    ) internal nonReentrant {\\n        bool passed = checkIfAllPassed(_testResults);\\n\\n        emit TestSuiteResult(_submissionAddress, passed, _testResults);\\n\\n        /**\\n         * Grant a soulbound NFT pin if:\\n         *  - This contract address has not been submitted before\\n         *  - The sender does not already own one of these pins\\n         *  - The contract submitted passes all unit tests\\n         */\\n        if (\\n            !submittedContracts[_submissionAddress] &&\\n            !owners[msg.sender] &&\\n            passed\\n        ) {\\n            tokenIds.increment();\\n            uint newId = tokenIds.current();\\n            owners[msg.sender] = true;\\n            _safeMint(msg.sender, newId);\\n        }\\n\\n        submittedContracts[_submissionAddress] = true;\\n    }\\n\\n    /**\\n     * Disallow transfers (Soulbound NFT)\\n     */\\n    function _beforeTokenTransfer(\\n        address _from,\\n        address,\\n        uint,\\n        uint\\n    ) internal pure override {\\n        if (_from != address(0)) {\\n            revert SoulboundToken();\\n        }\\n    }\\n\\n    function setActive(bool _setActiveTo) public onlyOwner {\\n        active = _setActiveTo;\\n    }\\n\\n    function setTokenURI(string memory _tokenURI) public onlyOwner {\\n        AllTokenURI = _tokenURI;\\n    }\\n\\n    function tokenURI(uint256) public view override returns (string memory) {\\n        return AllTokenURI;\\n    }\\n\\n    modifier testIsActive() {\\n        if (!active) {\\n            revert NotActive();\\n        }\\n        _;\\n    }\\n}\\n\",\"keccak256\":\"0xe350e3229cbd709b8c8070980eaa36e1a29bf7bc3f2e3e5d6c7f9940ec376144\",\"license\":\"UNLICENSED\"},\"contracts/StorageUnitTest.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\npragma solidity ^0.8.17;\\n\\nimport \\\"@openzeppelin/contracts/token/ERC721/ERC721.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/Counters.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\nimport \\\"./Cafe.sol\\\";\\nimport \\\"./Assert.sol\\\";\\n\\nusing List for List.ARList;\\n\\ninterface IEmployeeStorage {\\n    function grantShares(uint16 _newShares) external;\\n\\n    function checkForPacking(uint _slot) external returns (uint result);\\n\\n    function name() external view returns (string memory);\\n\\n    function idNumber() external view returns (uint);\\n\\n    function salary() external view returns (uint24);\\n\\n    function shares() external view returns (uint16);\\n\\n    function viewShares() external view returns (uint16);\\n\\n    function viewSalary() external view returns (uint24);\\n\\n    function debugResetShares() external;\\n}\\n\\nlibrary Caller {\\n    function callRemoteGrantShares(\\n        IEmployeeStorage _submission,\\n        uint16 _newShares\\n    ) internal returns (string memory, bool) {\\n        try _submission.grantShares(_newShares) {\\n            return (\\\"\\\", false);\\n        } catch Error(string memory reason) {\\n            return (reason, true);\\n        } catch {\\n            return (\\\"\\\", true);\\n        }\\n    }\\n\\n    function callRemoteCheckForPacking(\\n        IEmployeeStorage _submission,\\n        uint _slot\\n    ) internal returns (uint, bool) {\\n        try _submission.checkForPacking(_slot) returns (uint result) {\\n            return (result, false);\\n        } catch {\\n            return (0, true);\\n        }\\n    }\\n\\n    function callRemoteSalary(\\n        IEmployeeStorage _submission\\n    ) internal view returns (uint24, bool) {\\n        try _submission.salary() returns (uint24 result) {\\n            return (result, false);\\n        } catch {\\n            return (0, true);\\n        }\\n    }\\n\\n    function callRemoteShares(\\n        IEmployeeStorage _submission\\n    ) internal view returns (uint16, bool) {\\n        try _submission.shares() returns (uint16 result) {\\n            return (result, false);\\n        } catch {\\n            return (0, true);\\n        }\\n    }\\n\\n    function callRemoteViewSalary(\\n        IEmployeeStorage _submission\\n    ) internal view returns (uint24, bool) {\\n        try _submission.viewSalary() returns (uint24 result) {\\n            return (result, false);\\n        } catch {\\n            return (0, true);\\n        }\\n    }\\n\\n    function callRemoteViewShares(\\n        IEmployeeStorage _submission\\n    ) internal view returns (uint16, bool) {\\n        try _submission.viewShares() returns (uint16 result) {\\n            return (result, false);\\n        } catch {\\n            return (0, true);\\n        }\\n    }\\n}\\n\\ncontract testPublicStorage is ITest, Assert {\\n    function execute(\\n        address _submissionAddress\\n    ) external view override returns (Cafe.TestResult memory) {\\n        IEmployeeStorage submission = IEmployeeStorage(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult.message = \\\"Constructor should initialize data correctly\\\";\\n\\n        // Shouldn't need to check for call failure for public automatic getters\\n        testResult.assertResults.push(Assert.equal(submission.name(), \\\"Pat\\\"));\\n        testResult.assertResults.push(\\n            Assert.equal(submission.idNumber(), 112358132134)\\n        );\\n\\n        return testResult;\\n    }\\n}\\n\\ncontract testPrivateStorage is ITest, Assert {\\n    function execute(\\n        address _submissionAddress\\n    ) external override returns (Cafe.TestResult memory) {\\n        IEmployeeStorage submission = IEmployeeStorage(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult\\n            .message = \\\"Private vars should revert, have public accessors\\\";\\n\\n        // Reset share numbers for test\\n        submission.debugResetShares();\\n\\n        (uint16 shares, bool callError) = Caller.callRemoteShares(submission);\\n        testResult.assertResults.push(Assert.isTrue(callError));\\n\\n        (shares, callError) = Caller.callRemoteViewShares(submission);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to remoteViewShares failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(shares, 1000));\\n        }\\n\\n        uint24 salary;\\n        (salary, callError) = Caller.callRemoteSalary(submission);\\n        testResult.assertResults.push(Assert.isTrue(callError));\\n\\n        (salary, callError) = Caller.callRemoteViewSalary(submission);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to remoteViewSalary failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(salary, 50000));\\n        }\\n        return testResult;\\n    }\\n}\\n\\ncontract testPacking is ITest, Assert {\\n    function execute(\\n        address _submissionAddress\\n    ) external override returns (Cafe.TestResult memory) {\\n        IEmployeeStorage submission = IEmployeeStorage(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult.message = \\\"Variables should pack appropriately\\\";\\n        (uint res, bool callError) = Caller.callRemoteCheckForPacking(\\n            submission,\\n            3\\n        );\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to checkForPacking failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            testResult.assertResults.push(Assert.equal(res, 0));\\n        }\\n        return testResult;\\n    }\\n}\\n\\ncontract testGrantShares is ITest, Assert {\\n    function execute(\\n        address _submissionAddress\\n    ) external override returns (Cafe.TestResult memory) {\\n        IEmployeeStorage submission = IEmployeeStorage(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult.message = \\\"Can grant shares, revert >= 5000\\\";\\n\\n        // Reset share numbers for test\\n        submission.debugResetShares();\\n\\n        (string memory reason, bool callError) = Caller.callRemoteGrantShares(\\n            submission,\\n            5001\\n        );\\n\\n        testResult.assertResults.push(Assert.isTrue(callError));\\n        testResult.assertResults.push(Assert.equal(reason, \\\"Too many shares\\\"));\\n\\n        (reason, callError) = Caller.callRemoteGrantShares(submission, 1000);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to grantShares failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            uint16 shares;\\n            (shares, callError) = Caller.callRemoteViewShares(submission);\\n            testResult.assertResults.push(Assert.equal(shares, 2000));\\n        }\\n        return testResult;\\n    }\\n}\\n\\ncontract StorageUT is Cafe {\\n    constructor() ERC721(\\\"Storage Pin\\\", \\\"SCDS\\\") {\\n        tests.push(new testPublicStorage());\\n        tests.push(new testPrivateStorage());\\n        tests.push(new testPacking());\\n        tests.push(new testGrantShares());\\n    }\\n}\\n\",\"keccak256\":\"0x042f0227c5add29277c5993e039ba7a627b3e9c986d47ebce5c795a7478d2556\",\"license\":\"UNLICENSED\"},\"hardhat/console.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity >= 0.4.22 <0.9.0;\\n\\nlibrary console {\\n\\taddress constant CONSOLE_ADDRESS = address(0x000000000000000000636F6e736F6c652e6c6f67);\\n\\n\\tfunction _sendLogPayload(bytes memory payload) private view {\\n\\t\\tuint256 payloadLength = payload.length;\\n\\t\\taddress consoleAddress = CONSOLE_ADDRESS;\\n\\t\\tassembly {\\n\\t\\t\\tlet payloadStart := add(payload, 32)\\n\\t\\t\\tlet r := staticcall(gas(), consoleAddress, payloadStart, payloadLength, 0, 0)\\n\\t\\t}\\n\\t}\\n\\n\\tfunction log() internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log()\\\"));\\n\\t}\\n\\n\\tfunction logInt(int256 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(int256)\\\", p0));\\n\\t}\\n\\n\\tfunction logUint(uint256 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256)\\\", p0));\\n\\t}\\n\\n\\tfunction logString(string memory p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string)\\\", p0));\\n\\t}\\n\\n\\tfunction logBool(bool p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool)\\\", p0));\\n\\t}\\n\\n\\tfunction logAddress(address p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes(bytes memory p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes1(bytes1 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes1)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes2(bytes2 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes2)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes3(bytes3 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes3)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes4(bytes4 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes4)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes5(bytes5 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes5)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes6(bytes6 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes6)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes7(bytes7 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes7)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes8(bytes8 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes8)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes9(bytes9 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes9)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes10(bytes10 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes10)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes11(bytes11 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes11)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes12(bytes12 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes12)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes13(bytes13 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes13)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes14(bytes14 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes14)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes15(bytes15 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes15)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes16(bytes16 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes16)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes17(bytes17 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes17)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes18(bytes18 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes18)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes19(bytes19 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes19)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes20(bytes20 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes20)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes21(bytes21 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes21)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes22(bytes22 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes22)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes23(bytes23 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes23)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes24(bytes24 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes24)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes25(bytes25 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes25)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes26(bytes26 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes26)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes27(bytes27 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes27)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes28(bytes28 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes28)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes29(bytes29 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes29)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes30(bytes30 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes30)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes31(bytes31 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes31)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes32(bytes32 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes32)\\\", p0));\\n\\t}\\n\\n\\tfunction log(uint256 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256)\\\", p0));\\n\\t}\\n\\n\\tfunction log(string memory p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string)\\\", p0));\\n\\t}\\n\\n\\tfunction log(bool p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool)\\\", p0));\\n\\t}\\n\\n\\tfunction log(address p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address)\\\", p0));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n}\\n\",\"keccak256\":\"0x60b0215121bf25612a6739fb2f1ec35f31ee82e4a8216c032c8243d904ab3aa9\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"kind": "dev", "methods": {"approve(address,uint256)": {"details": "See {IERC721-approve}."}, "balanceOf(address)": {"details": "See {IERC721-balanceOf}."}, "getApproved(uint256)": {"details": "See {IERC721-getApproved}."}, "isApprovedForAll(address,address)": {"details": "See {IERC721-isApprovedForAll}."}, "name()": {"details": "See {IERC721Metadata-name}."}, "owner()": {"details": "Returns the address of the current owner."}, "ownerOf(uint256)": {"details": "See {IERC721-ownerOf}."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions anymore. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby removing any functionality that is only available to the owner."}, "safeTransferFrom(address,address,uint256)": {"details": "See {IERC721-safeTransferFrom}."}, "safeTransferFrom(address,address,uint256,bytes)": {"details": "See {IERC721-safeTransferFrom}."}, "setApprovalForAll(address,bool)": {"details": "See {IERC721-setApprovalForAll}."}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}, "symbol()": {"details": "See {IERC721Metadata-symbol}."}, "transferFrom(address,address,uint256)": {"details": "See {IERC721-transferFrom}."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"checkIfAllPassed((string,((bool,string,string,string,string)[],uint256))[])": {"notice": "Check each assert in each test to see if any failed. Note:  The check is here instead of setting a `passed` bool in `TestResult` to reduce the amount of code in each unit test."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 882, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 884, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_symbol", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 888, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_owners", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 892, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_balances", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 896, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_tokenApprovals", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 902, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_operatorApprovals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_bool))"}, {"astId": 7, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_owner", "offset": 0, "slot": "6", "type": "t_address"}, {"astId": 123, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_status", "offset": 0, "slot": "7", "type": "t_uint256"}, {"astId": 6525, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "tokenIds", "offset": 0, "slot": "8", "type": "t_struct(Counter)2320_storage"}, {"astId": 6529, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "owners", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_bool)"}, {"astId": 6533, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "submittedContracts", "offset": 0, "slot": "10", "type": "t_mapping(t_address,t_bool)"}, {"astId": 6536, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "active", "offset": 0, "slot": "11", "type": "t_bool"}, {"astId": 6539, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "AllTokenURI", "offset": 0, "slot": "12", "type": "t_string_storage"}, {"astId": 6559, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "tests", "offset": 0, "slot": "13", "type": "t_array(t_contract(ITest)6495)dyn_storage"}, {"astId": 6563, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "deployer", "offset": 0, "slot": "14", "type": "t_contract(IDeploy)6506"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_contract(ITest)6495)dyn_storage": {"base": "t_contract(ITest)6495", "encoding": "dynamic_array", "label": "contract ITest[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_contract(IDeploy)6506": {"encoding": "inplace", "label": "contract IDeploy", "numberOfBytes": "20"}, "t_contract(ITest)6495": {"encoding": "inplace", "label": "contract ITest", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_address,t_mapping(t_address,t_bool))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => bool))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_bool)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Counter)2320_storage": {"encoding": "inplace", "label": "struct Counters.Counter", "members": [{"astId": 2319, "contract": "contracts/StorageUnitTest.sol:StorageUT", "label": "_value", "offset": 0, "slot": "0", "type": "t_uint256"}], "numberOfBytes": "32"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}