{"address": "0xdC01a54eD81923F96aF4CF8fC7dB2B00a9366714", "abi": [{"inputs": [{"internalType": "address", "name": "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "NotActive", "type": "error"}, {"inputs": [], "name": "SoulboundToken", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "approved", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "submission", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "passed", "type": "bool"}, {"components": [{"internalType": "string", "name": "message", "type": "string"}, {"components": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult[]", "name": "elements", "type": "tuple[]"}, {"internalType": "uint256", "name": "num", "type": "uint256"}], "internalType": "struct List.ARList", "name": "assertResults", "type": "tuple"}], "indexed": false, "internalType": "struct Cafe.TestResult[]", "name": "testResults", "type": "tuple[]"}], "name": "TestSuiteResult", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "string", "name": "message", "type": "string"}, {"components": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult[]", "name": "elements", "type": "tuple[]"}, {"internalType": "uint256", "name": "num", "type": "uint256"}], "internalType": "struct List.ARList", "name": "assertResults", "type": "tuple"}], "internalType": "struct Cafe.TestResult[]", "name": "_testResults", "type": "tuple[]"}], "name": "checkIfAllPassed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "a", "type": "bytes"}, {"internalType": "bytes", "name": "b", "type": "bytes"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}, {"internalType": "uint256", "name": "b", "type": "uint256"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "address", "name": "b", "type": "address"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "string", "name": "a", "type": "string"}, {"internalType": "string", "name": "b", "type": "string"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "string[]", "name": "a", "type": "string[]"}, {"internalType": "string[]", "name": "b", "type": "string[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "a", "type": "address[]"}, {"internalType": "address[]", "name": "b", "type": "address[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "a", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "b", "type": "uint256[]"}], "name": "equal", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "a", "type": "bool"}], "name": "isFalse", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "a", "type": "bool"}], "name": "isTrue", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "a", "type": "address"}, {"internalType": "address", "name": "b", "type": "address"}], "name": "notEqual", "outputs": [{"components": [{"internalType": "bool", "name": "passed", "type": "bool"}, {"internalType": "string", "name": "assertionError", "type": "string"}, {"internalType": "string", "name": "returnedAsString", "type": "string"}, {"internalType": "string", "name": "expectedAsString", "type": "string"}, {"internalType": "string", "name": "methodName", "type": "string"}], "internalType": "struct Assert.AssertResult", "name": "result", "type": "tuple"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "owners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_setActiveTo", "type": "bool"}], "name": "setActive", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_tokenURI", "type": "string"}], "name": "setTokenURI", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_submissionAddress", "type": "address"}], "name": "testContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "transactionHash": "0xeb22df6812376d82fbbe54fde4bbe7d1db725f3b37c2c2d21fc336aae4aaa267", "receipt": {"to": null, "from": "0x0919C594E549545374772246B0D433a4988A0eC9", "contractAddress": "0xdC01a54eD81923F96aF4CF8fC7dB2B00a9366714", "transactionIndex": 2, "gasUsed": "7282742", "logsBloom": "0x00000000000000000000000008000000000000000000000000800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000020000200000000000000840000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000800040000000000000000000000000000000000000000000000000000", "blockHash": "0xbbbc195d89e9226df2a32eb14a12b909cf7b5f234ebdec2e6f4a11077a95cb73", "transactionHash": "0xeb22df6812376d82fbbe54fde4bbe7d1db725f3b37c2c2d21fc336aae4aaa267", "logs": [{"transactionIndex": 2, "blockNumber": 4536868, "transactionHash": "0xeb22df6812376d82fbbe54fde4bbe7d1db725f3b37c2c2d21fc336aae4aaa267", "address": "0xdC01a54eD81923F96aF4CF8fC7dB2B00a9366714", "topics": ["0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x0000000000000000000000000919c594e549545374772246b0d433a4988a0ec9"], "data": "0x", "logIndex": 0, "blockHash": "0xbbbc195d89e9226df2a32eb14a12b909cf7b5f234ebdec2e6f4a11077a95cb73"}], "blockNumber": 4536868, "cumulativeGasUsed": "7367731", "status": 1, "byzantium": true}, "args": ["0x3e99064bD7d167933AeE3F49b2D691f32c1f5a13"], "numDeployments": 2, "solcInputHash": "cce5e99710aedb2b7694c5fe3ee6e1a0", "metadata": "{\"compiler\":{\"version\":\"0.8.17+commit.8df45f5f\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_secondCallerAddress\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"NotActive\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SoulboundToken\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"submission\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"string\",\"name\":\"message\",\"type\":\"string\"},{\"components\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult[]\",\"name\":\"elements\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"internalType\":\"struct List.ARList\",\"name\":\"assertResults\",\"type\":\"tuple\"}],\"indexed\":false,\"internalType\":\"struct Cafe.TestResult[]\",\"name\":\"testResults\",\"type\":\"tuple[]\"}],\"name\":\"TestSuiteResult\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"message\",\"type\":\"string\"},{\"components\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult[]\",\"name\":\"elements\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256\",\"name\":\"num\",\"type\":\"uint256\"}],\"internalType\":\"struct List.ARList\",\"name\":\"assertResults\",\"type\":\"tuple\"}],\"internalType\":\"struct Cafe.TestResult[]\",\"name\":\"_testResults\",\"type\":\"tuple[]\"}],\"name\":\"checkIfAllPassed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"a\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"b\",\"type\":\"bytes\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"b\",\"type\":\"uint256\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"a\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"b\",\"type\":\"address\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"a\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"b\",\"type\":\"string\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"a\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"b\",\"type\":\"string[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"a\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"b\",\"type\":\"address[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"a\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"b\",\"type\":\"uint256[]\"}],\"name\":\"equal\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"a\",\"type\":\"bool\"}],\"name\":\"isFalse\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"a\",\"type\":\"bool\"}],\"name\":\"isTrue\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"a\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"b\",\"type\":\"address\"}],\"name\":\"notEqual\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"passed\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"assertionError\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"returnedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"expectedAsString\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"methodName\",\"type\":\"string\"}],\"internalType\":\"struct Assert.AssertResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"owners\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_setActiveTo\",\"type\":\"bool\"}],\"name\":\"setActive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_tokenURI\",\"type\":\"string\"}],\"name\":\"setTokenURI\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_submissionAddress\",\"type\":\"address\"}],\"name\":\"testContract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"approve(address,uint256)\":{\"details\":\"See {IERC721-approve}.\"},\"balanceOf(address)\":{\"details\":\"See {IERC721-balanceOf}.\"},\"getApproved(uint256)\":{\"details\":\"See {IERC721-getApproved}.\"},\"isApprovedForAll(address,address)\":{\"details\":\"See {IERC721-isApprovedForAll}.\"},\"name()\":{\"details\":\"See {IERC721Metadata-name}.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"ownerOf(uint256)\":{\"details\":\"See {IERC721-ownerOf}.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions anymore. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby removing any functionality that is only available to the owner.\"},\"safeTransferFrom(address,address,uint256)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"safeTransferFrom(address,address,uint256,bytes)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"setApprovalForAll(address,bool)\":{\"details\":\"See {IERC721-setApprovalForAll}.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"},\"symbol()\":{\"details\":\"See {IERC721Metadata-symbol}.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC721-transferFrom}.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"checkIfAllPassed((string,((bool,string,string,string,string)[],uint256))[])\":{\"notice\":\"Check each assert in each test to see if any failed. Note:  The check is here instead of setting a `passed` bool in `TestResult` to reduce the amount of code in each unit test.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/ArraysUT.sol\":\"ArraysUT\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":1000},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.7.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../utils/Context.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * By default, the owner account will be the one that deploys the contract. This\\n * can later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract Ownable is Context {\\n    address private _owner;\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial owner.\\n     */\\n    constructor() {\\n        _transferOwnership(_msgSender());\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        require(owner() == _msgSender(), \\\"Ownable: caller is not the owner\\\");\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions anymore. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby removing any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        require(newOwner != address(0), \\\"Ownable: new owner is the zero address\\\");\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n}\\n\",\"keccak256\":\"0xa94b34880e3c1b0b931662cb1c09e5dfa6662f31cba80e07c5ee71cd135c9673\",\"license\":\"MIT\"},\"@openzeppelin/contracts/security/ReentrancyGuard.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (security/ReentrancyGuard.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Contract module that helps prevent reentrant calls to a function.\\n *\\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\\n * available, which can be applied to functions to make sure there are no nested\\n * (reentrant) calls to them.\\n *\\n * Note that because there is a single `nonReentrant` guard, functions marked as\\n * `nonReentrant` may not call one another. This can be worked around by making\\n * those functions `private`, and then adding `external` `nonReentrant` entry\\n * points to them.\\n *\\n * TIP: If you would like to learn more about reentrancy and alternative ways\\n * to protect against it, check out our blog post\\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\\n */\\nabstract contract ReentrancyGuard {\\n    // Booleans are more expensive than uint256 or any type that takes up a full\\n    // word because each write operation emits an extra SLOAD to first read the\\n    // slot's contents, replace the bits taken up by the boolean, and then write\\n    // back. This is the compiler's defense against contract upgrades and\\n    // pointer aliasing, and it cannot be disabled.\\n\\n    // The values being non-zero value makes deployment a bit more expensive,\\n    // but in exchange the refund on every call to nonReentrant will be lower in\\n    // amount. Since refunds are capped to a percentage of the total\\n    // transaction's gas, it is best to keep them low in cases like this one, to\\n    // increase the likelihood of the full refund coming into effect.\\n    uint256 private constant _NOT_ENTERED = 1;\\n    uint256 private constant _ENTERED = 2;\\n\\n    uint256 private _status;\\n\\n    constructor() {\\n        _status = _NOT_ENTERED;\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and making it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        _nonReentrantBefore();\\n        _;\\n        _nonReentrantAfter();\\n    }\\n\\n    function _nonReentrantBefore() private {\\n        // On the first call to nonReentrant, _status will be _NOT_ENTERED\\n        require(_status != _ENTERED, \\\"ReentrancyGuard: reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        _status = _ENTERED;\\n    }\\n\\n    function _nonReentrantAfter() private {\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        _status = _NOT_ENTERED;\\n    }\\n}\\n\",\"keccak256\":\"0x190dd6f8d592b7e4e930feb7f4313aeb8e1c4ad3154c27ce1cf6a512fc30d8cc\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/ERC721.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC721/ERC721.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC721.sol\\\";\\nimport \\\"./IERC721Receiver.sol\\\";\\nimport \\\"./extensions/IERC721Metadata.sol\\\";\\nimport \\\"../../utils/Address.sol\\\";\\nimport \\\"../../utils/Context.sol\\\";\\nimport \\\"../../utils/Strings.sol\\\";\\nimport \\\"../../utils/introspection/ERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of https://eips.ethereum.org/EIPS/eip-721[ERC721] Non-Fungible Token Standard, including\\n * the Metadata extension, but not including the Enumerable extension, which is available separately as\\n * {ERC721Enumerable}.\\n */\\ncontract ERC721 is Context, ERC165, IERC721, IERC721Metadata {\\n    using Address for address;\\n    using Strings for uint256;\\n\\n    // Token name\\n    string private _name;\\n\\n    // Token symbol\\n    string private _symbol;\\n\\n    // Mapping from token ID to owner address\\n    mapping(uint256 => address) private _owners;\\n\\n    // Mapping owner address to token count\\n    mapping(address => uint256) private _balances;\\n\\n    // Mapping from token ID to approved address\\n    mapping(uint256 => address) private _tokenApprovals;\\n\\n    // Mapping from owner to operator approvals\\n    mapping(address => mapping(address => bool)) private _operatorApprovals;\\n\\n    /**\\n     * @dev Initializes the contract by setting a `name` and a `symbol` to the token collection.\\n     */\\n    constructor(string memory name_, string memory symbol_) {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override(ERC165, IERC165) returns (bool) {\\n        return\\n            interfaceId == type(IERC721).interfaceId ||\\n            interfaceId == type(IERC721Metadata).interfaceId ||\\n            super.supportsInterface(interfaceId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-balanceOf}.\\n     */\\n    function balanceOf(address owner) public view virtual override returns (uint256) {\\n        require(owner != address(0), \\\"ERC721: address zero is not a valid owner\\\");\\n        return _balances[owner];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-ownerOf}.\\n     */\\n    function ownerOf(uint256 tokenId) public view virtual override returns (address) {\\n        address owner = _ownerOf(tokenId);\\n        require(owner != address(0), \\\"ERC721: invalid token ID\\\");\\n        return owner;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-name}.\\n     */\\n    function name() public view virtual override returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-symbol}.\\n     */\\n    function symbol() public view virtual override returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev See {IERC721Metadata-tokenURI}.\\n     */\\n    function tokenURI(uint256 tokenId) public view virtual override returns (string memory) {\\n        _requireMinted(tokenId);\\n\\n        string memory baseURI = _baseURI();\\n        return bytes(baseURI).length > 0 ? string(abi.encodePacked(baseURI, tokenId.toString())) : \\\"\\\";\\n    }\\n\\n    /**\\n     * @dev Base URI for computing {tokenURI}. If set, the resulting URI for each\\n     * token will be the concatenation of the `baseURI` and the `tokenId`. Empty\\n     * by default, can be overridden in child contracts.\\n     */\\n    function _baseURI() internal view virtual returns (string memory) {\\n        return \\\"\\\";\\n    }\\n\\n    /**\\n     * @dev See {IERC721-approve}.\\n     */\\n    function approve(address to, uint256 tokenId) public virtual override {\\n        address owner = ERC721.ownerOf(tokenId);\\n        require(to != owner, \\\"ERC721: approval to current owner\\\");\\n\\n        require(\\n            _msgSender() == owner || isApprovedForAll(owner, _msgSender()),\\n            \\\"ERC721: approve caller is not token owner or approved for all\\\"\\n        );\\n\\n        _approve(to, tokenId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-getApproved}.\\n     */\\n    function getApproved(uint256 tokenId) public view virtual override returns (address) {\\n        _requireMinted(tokenId);\\n\\n        return _tokenApprovals[tokenId];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-setApprovalForAll}.\\n     */\\n    function setApprovalForAll(address operator, bool approved) public virtual override {\\n        _setApprovalForAll(_msgSender(), operator, approved);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-isApprovedForAll}.\\n     */\\n    function isApprovedForAll(address owner, address operator) public view virtual override returns (bool) {\\n        return _operatorApprovals[owner][operator];\\n    }\\n\\n    /**\\n     * @dev See {IERC721-transferFrom}.\\n     */\\n    function transferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) public virtual override {\\n        //solhint-disable-next-line max-line-length\\n        require(_isApprovedOrOwner(_msgSender(), tokenId), \\\"ERC721: caller is not token owner or approved\\\");\\n\\n        _transfer(from, to, tokenId);\\n    }\\n\\n    /**\\n     * @dev See {IERC721-safeTransferFrom}.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) public virtual override {\\n        safeTransferFrom(from, to, tokenId, \\\"\\\");\\n    }\\n\\n    /**\\n     * @dev See {IERC721-safeTransferFrom}.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) public virtual override {\\n        require(_isApprovedOrOwner(_msgSender(), tokenId), \\\"ERC721: caller is not token owner or approved\\\");\\n        _safeTransfer(from, to, tokenId, data);\\n    }\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients\\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\\n     *\\n     * `data` is additional data, it has no specified format and it is sent in call to `to`.\\n     *\\n     * This internal function is equivalent to {safeTransferFrom}, and can be used to e.g.\\n     * implement alternative mechanisms to perform token transfer, such as signature-based.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _safeTransfer(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) internal virtual {\\n        _transfer(from, to, tokenId);\\n        require(_checkOnERC721Received(from, to, tokenId, data), \\\"ERC721: transfer to non ERC721Receiver implementer\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the owner of the `tokenId`. Does NOT revert if token doesn't exist\\n     */\\n    function _ownerOf(uint256 tokenId) internal view virtual returns (address) {\\n        return _owners[tokenId];\\n    }\\n\\n    /**\\n     * @dev Returns whether `tokenId` exists.\\n     *\\n     * Tokens can be managed by their owner or approved accounts via {approve} or {setApprovalForAll}.\\n     *\\n     * Tokens start existing when they are minted (`_mint`),\\n     * and stop existing when they are burned (`_burn`).\\n     */\\n    function _exists(uint256 tokenId) internal view virtual returns (bool) {\\n        return _ownerOf(tokenId) != address(0);\\n    }\\n\\n    /**\\n     * @dev Returns whether `spender` is allowed to manage `tokenId`.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function _isApprovedOrOwner(address spender, uint256 tokenId) internal view virtual returns (bool) {\\n        address owner = ERC721.ownerOf(tokenId);\\n        return (spender == owner || isApprovedForAll(owner, spender) || getApproved(tokenId) == spender);\\n    }\\n\\n    /**\\n     * @dev Safely mints `tokenId` and transfers it to `to`.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must not exist.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _safeMint(address to, uint256 tokenId) internal virtual {\\n        _safeMint(to, tokenId, \\\"\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-ERC721-_safeMint-address-uint256-}[`_safeMint`], with an additional `data` parameter which is\\n     * forwarded in {IERC721Receiver-onERC721Received} to contract recipients.\\n     */\\n    function _safeMint(\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) internal virtual {\\n        _mint(to, tokenId);\\n        require(\\n            _checkOnERC721Received(address(0), to, tokenId, data),\\n            \\\"ERC721: transfer to non ERC721Receiver implementer\\\"\\n        );\\n    }\\n\\n    /**\\n     * @dev Mints `tokenId` and transfers it to `to`.\\n     *\\n     * WARNING: Usage of this method is discouraged, use {_safeMint} whenever possible\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must not exist.\\n     * - `to` cannot be the zero address.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _mint(address to, uint256 tokenId) internal virtual {\\n        require(to != address(0), \\\"ERC721: mint to the zero address\\\");\\n        require(!_exists(tokenId), \\\"ERC721: token already minted\\\");\\n\\n        _beforeTokenTransfer(address(0), to, tokenId, 1);\\n\\n        // Check that tokenId was not minted by `_beforeTokenTransfer` hook\\n        require(!_exists(tokenId), \\\"ERC721: token already minted\\\");\\n\\n        unchecked {\\n            // Will not overflow unless all 2**256 token ids are minted to the same owner.\\n            // Given that tokens are minted one by one, it is impossible in practice that\\n            // this ever happens. Might change if we allow batch minting.\\n            // The ERC fails to describe this case.\\n            _balances[to] += 1;\\n        }\\n\\n        _owners[tokenId] = to;\\n\\n        emit Transfer(address(0), to, tokenId);\\n\\n        _afterTokenTransfer(address(0), to, tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Destroys `tokenId`.\\n     * The approval is cleared when the token is burned.\\n     * This is an internal function that does not check if the sender is authorized to operate on the token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _burn(uint256 tokenId) internal virtual {\\n        address owner = ERC721.ownerOf(tokenId);\\n\\n        _beforeTokenTransfer(owner, address(0), tokenId, 1);\\n\\n        // Update ownership in case tokenId was transferred by `_beforeTokenTransfer` hook\\n        owner = ERC721.ownerOf(tokenId);\\n\\n        // Clear approvals\\n        delete _tokenApprovals[tokenId];\\n\\n        unchecked {\\n            // Cannot overflow, as that would require more tokens to be burned/transferred\\n            // out than the owner initially received through minting and transferring in.\\n            _balances[owner] -= 1;\\n        }\\n        delete _owners[tokenId];\\n\\n        emit Transfer(owner, address(0), tokenId);\\n\\n        _afterTokenTransfer(owner, address(0), tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Transfers `tokenId` from `from` to `to`.\\n     *  As opposed to {transferFrom}, this imposes no restrictions on msg.sender.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must be owned by `from`.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function _transfer(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) internal virtual {\\n        require(ERC721.ownerOf(tokenId) == from, \\\"ERC721: transfer from incorrect owner\\\");\\n        require(to != address(0), \\\"ERC721: transfer to the zero address\\\");\\n\\n        _beforeTokenTransfer(from, to, tokenId, 1);\\n\\n        // Check that tokenId was not transferred by `_beforeTokenTransfer` hook\\n        require(ERC721.ownerOf(tokenId) == from, \\\"ERC721: transfer from incorrect owner\\\");\\n\\n        // Clear approvals from the previous owner\\n        delete _tokenApprovals[tokenId];\\n\\n        unchecked {\\n            // `_balances[from]` cannot overflow for the same reason as described in `_burn`:\\n            // `from`'s balance is the number of token held, which is at least one before the current\\n            // transfer.\\n            // `_balances[to]` could overflow in the conditions described in `_mint`. That would require\\n            // all 2**256 token ids to be minted, which in practice is impossible.\\n            _balances[from] -= 1;\\n            _balances[to] += 1;\\n        }\\n        _owners[tokenId] = to;\\n\\n        emit Transfer(from, to, tokenId);\\n\\n        _afterTokenTransfer(from, to, tokenId, 1);\\n    }\\n\\n    /**\\n     * @dev Approve `to` to operate on `tokenId`\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function _approve(address to, uint256 tokenId) internal virtual {\\n        _tokenApprovals[tokenId] = to;\\n        emit Approval(ERC721.ownerOf(tokenId), to, tokenId);\\n    }\\n\\n    /**\\n     * @dev Approve `operator` to operate on all of `owner` tokens\\n     *\\n     * Emits an {ApprovalForAll} event.\\n     */\\n    function _setApprovalForAll(\\n        address owner,\\n        address operator,\\n        bool approved\\n    ) internal virtual {\\n        require(owner != operator, \\\"ERC721: approve to caller\\\");\\n        _operatorApprovals[owner][operator] = approved;\\n        emit ApprovalForAll(owner, operator, approved);\\n    }\\n\\n    /**\\n     * @dev Reverts if the `tokenId` has not been minted yet.\\n     */\\n    function _requireMinted(uint256 tokenId) internal view virtual {\\n        require(_exists(tokenId), \\\"ERC721: invalid token ID\\\");\\n    }\\n\\n    /**\\n     * @dev Internal function to invoke {IERC721Receiver-onERC721Received} on a target address.\\n     * The call is not executed if the target address is not a contract.\\n     *\\n     * @param from address representing the previous owner of the given token ID\\n     * @param to target address that will receive the tokens\\n     * @param tokenId uint256 ID of the token to be transferred\\n     * @param data bytes optional data to send along with the call\\n     * @return bool whether the call correctly returned the expected magic value\\n     */\\n    function _checkOnERC721Received(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes memory data\\n    ) private returns (bool) {\\n        if (to.isContract()) {\\n            try IERC721Receiver(to).onERC721Received(_msgSender(), from, tokenId, data) returns (bytes4 retval) {\\n                return retval == IERC721Receiver.onERC721Received.selector;\\n            } catch (bytes memory reason) {\\n                if (reason.length == 0) {\\n                    revert(\\\"ERC721: transfer to non ERC721Receiver implementer\\\");\\n                } else {\\n                    /// @solidity memory-safe-assembly\\n                    assembly {\\n                        revert(add(32, reason), mload(reason))\\n                    }\\n                }\\n            }\\n        } else {\\n            return true;\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called before any token transfer. This includes minting and burning. If {ERC721Consecutive} is\\n     * used, the hook may be called as part of a consecutive (batch) mint, as indicated by `batchSize` greater than 1.\\n     *\\n     * Calling conditions:\\n     *\\n     * - When `from` and `to` are both non-zero, ``from``'s tokens will be transferred to `to`.\\n     * - When `from` is zero, the tokens will be minted for `to`.\\n     * - When `to` is zero, ``from``'s tokens will be burned.\\n     * - `from` and `to` are never both zero.\\n     * - `batchSize` is non-zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _beforeTokenTransfer(\\n        address from,\\n        address to,\\n        uint256, /* firstTokenId */\\n        uint256 batchSize\\n    ) internal virtual {\\n        if (batchSize > 1) {\\n            if (from != address(0)) {\\n                _balances[from] -= batchSize;\\n            }\\n            if (to != address(0)) {\\n                _balances[to] += batchSize;\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called after any token transfer. This includes minting and burning. If {ERC721Consecutive} is\\n     * used, the hook may be called as part of a consecutive (batch) mint, as indicated by `batchSize` greater than 1.\\n     *\\n     * Calling conditions:\\n     *\\n     * - When `from` and `to` are both non-zero, ``from``'s tokens were transferred to `to`.\\n     * - When `from` is zero, the tokens were minted for `to`.\\n     * - When `to` is zero, ``from``'s tokens were burned.\\n     * - `from` and `to` are never both zero.\\n     * - `batchSize` is non-zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _afterTokenTransfer(\\n        address from,\\n        address to,\\n        uint256 firstTokenId,\\n        uint256 batchSize\\n    ) internal virtual {}\\n}\\n\",\"keccak256\":\"0xd89f3585b211fc9e3408384a4c4efdc3a93b2f877a3821046fa01c219d35be1b\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/IERC721.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC721/IERC721.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../../utils/introspection/IERC165.sol\\\";\\n\\n/**\\n * @dev Required interface of an ERC721 compliant contract.\\n */\\ninterface IERC721 is IERC165 {\\n    /**\\n     * @dev Emitted when `tokenId` token is transferred from `from` to `to`.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables `approved` to manage the `tokenId` token.\\n     */\\n    event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId);\\n\\n    /**\\n     * @dev Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets.\\n     */\\n    event ApprovalForAll(address indexed owner, address indexed operator, bool approved);\\n\\n    /**\\n     * @dev Returns the number of tokens in ``owner``'s account.\\n     */\\n    function balanceOf(address owner) external view returns (uint256 balance);\\n\\n    /**\\n     * @dev Returns the owner of the `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function ownerOf(uint256 tokenId) external view returns (address owner);\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId,\\n        bytes calldata data\\n    ) external;\\n\\n    /**\\n     * @dev Safely transfers `tokenId` token from `from` to `to`, checking first that contract recipients\\n     * are aware of the ERC721 protocol to prevent tokens from being forever locked.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must exist and be owned by `from`.\\n     * - If the caller is not `from`, it must have been allowed to move this token by either {approve} or {setApprovalForAll}.\\n     * - If `to` refers to a smart contract, it must implement {IERC721Receiver-onERC721Received}, which is called upon a safe transfer.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function safeTransferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) external;\\n\\n    /**\\n     * @dev Transfers `tokenId` token from `from` to `to`.\\n     *\\n     * WARNING: Note that the caller is responsible to confirm that the recipient is capable of receiving ERC721\\n     * or else they may be permanently lost. Usage of {safeTransferFrom} prevents loss, though the caller must\\n     * understand this adds an external call which potentially creates a reentrancy vulnerability.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `tokenId` token must be owned by `from`.\\n     * - If the caller is not `from`, it must be approved to move this token by either {approve} or {setApprovalForAll}.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(\\n        address from,\\n        address to,\\n        uint256 tokenId\\n    ) external;\\n\\n    /**\\n     * @dev Gives permission to `to` to transfer `tokenId` token to another account.\\n     * The approval is cleared when the token is transferred.\\n     *\\n     * Only a single account can be approved at a time, so approving the zero address clears previous approvals.\\n     *\\n     * Requirements:\\n     *\\n     * - The caller must own the token or be an approved operator.\\n     * - `tokenId` must exist.\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address to, uint256 tokenId) external;\\n\\n    /**\\n     * @dev Approve or remove `operator` as an operator for the caller.\\n     * Operators can call {transferFrom} or {safeTransferFrom} for any token owned by the caller.\\n     *\\n     * Requirements:\\n     *\\n     * - The `operator` cannot be the caller.\\n     *\\n     * Emits an {ApprovalForAll} event.\\n     */\\n    function setApprovalForAll(address operator, bool _approved) external;\\n\\n    /**\\n     * @dev Returns the account approved for `tokenId` token.\\n     *\\n     * Requirements:\\n     *\\n     * - `tokenId` must exist.\\n     */\\n    function getApproved(uint256 tokenId) external view returns (address operator);\\n\\n    /**\\n     * @dev Returns if the `operator` is allowed to manage all of the assets of `owner`.\\n     *\\n     * See {setApprovalForAll}\\n     */\\n    function isApprovedForAll(address owner, address operator) external view returns (bool);\\n}\\n\",\"keccak256\":\"0xab28a56179c1db258c9bf5235b382698cb650debecb51b23d12be9e241374b68\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC721/IERC721Receiver.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @title ERC721 token receiver interface\\n * @dev Interface for any contract that wants to support safeTransfers\\n * from ERC721 asset contracts.\\n */\\ninterface IERC721Receiver {\\n    /**\\n     * @dev Whenever an {IERC721} `tokenId` token is transferred to this contract via {IERC721-safeTransferFrom}\\n     * by `operator` from `from`, this function is called.\\n     *\\n     * It must return its Solidity selector to confirm the token transfer.\\n     * If any other value is returned or the interface is not implemented by the recipient, the transfer will be reverted.\\n     *\\n     * The selector can be obtained in Solidity with `IERC721Receiver.onERC721Received.selector`.\\n     */\\n    function onERC721Received(\\n        address operator,\\n        address from,\\n        uint256 tokenId,\\n        bytes calldata data\\n    ) external returns (bytes4);\\n}\\n\",\"keccak256\":\"0xa82b58eca1ee256be466e536706850163d2ec7821945abd6b4778cfb3bee37da\",\"license\":\"MIT\"},\"@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC721/extensions/IERC721Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC721.sol\\\";\\n\\n/**\\n * @title ERC-721 Non-Fungible Token Standard, optional metadata extension\\n * @dev See https://eips.ethereum.org/EIPS/eip-721\\n */\\ninterface IERC721Metadata is IERC721 {\\n    /**\\n     * @dev Returns the token collection name.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the token collection symbol.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the Uniform Resource Identifier (URI) for `tokenId` token.\\n     */\\n    function tokenURI(uint256 tokenId) external view returns (string memory);\\n}\\n\",\"keccak256\":\"0x75b829ff2f26c14355d1cba20e16fe7b29ca58eb5fef665ede48bc0f9c6c74b9\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Address.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following\\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     * ====\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n     *\\n     * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n     * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n     * constructor.\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // This method relies on extcodesize/address.code.length, which returns 0\\n        // for contracts in construction, since the code is only stored at the end\\n        // of the constructor execution.\\n\\n        return account.code.length > 0;\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n\\n    /**\\n     * @dev Performs a Solidity function call using a low level `call`. A\\n     * plain `call` is an unsafe replacement for a function call: use this\\n     * function instead.\\n     *\\n     * If `target` reverts with a revert reason, it is bubbled up by this\\n     * function (like regular Solidity function calls).\\n     *\\n     * Returns the raw returned data. To convert to the expected return value,\\n     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n     *\\n     * Requirements:\\n     *\\n     * - `target` must be a contract.\\n     * - calling `target` with `data` must not revert.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n     * `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but also transferring `value` wei to `target`.\\n     *\\n     * Requirements:\\n     *\\n     * - the calling contract must have an ETH balance of at least `value`.\\n     * - the called Solidity function must be `payable`.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n     * with `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        require(address(this).balance >= value, \\\"Address: insufficient balance for call\\\");\\n        (bool success, bytes memory returndata) = target.call{value: value}(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n        return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.staticcall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.delegatecall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n     * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n     *\\n     * _Available since v4.8._\\n     */\\n    function verifyCallResultFromTarget(\\n        address target,\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        if (success) {\\n            if (returndata.length == 0) {\\n                // only check isContract if the call was successful and the return data is empty\\n                // otherwise we already know that it was a contract\\n                require(isContract(target), \\\"Address: call to non-contract\\\");\\n            }\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n     * revert reason or using the provided one.\\n     *\\n     * _Available since v4.3._\\n     */\\n    function verifyCallResult(\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal pure returns (bytes memory) {\\n        if (success) {\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n        // Look for revert reason and bubble it up if present\\n        if (returndata.length > 0) {\\n            // The easiest way to bubble the revert reason is using memory via assembly\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                let returndata_size := mload(returndata)\\n                revert(add(32, returndata), returndata_size)\\n            }\\n        } else {\\n            revert(errorMessage);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xf96f969e24029d43d0df89e59d365f277021dac62b48e1c1e3ebe0acdd7f1ca1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n}\\n\",\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Counters.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Counters.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @title Counters\\n * <AUTHOR> Condon (@shrugs)\\n * @dev Provides counters that can only be incremented, decremented or reset. This can be used e.g. to track the number\\n * of elements in a mapping, issuing ERC721 ids, or counting request ids.\\n *\\n * Include with `using Counters for Counters.Counter;`\\n */\\nlibrary Counters {\\n    struct Counter {\\n        // This variable should never be directly accessed by users of the library: interactions must be restricted to\\n        // the library's function. As of Solidity v0.5.2, this cannot be enforced, though there is a proposal to add\\n        // this feature: see https://github.com/ethereum/solidity/issues/4637\\n        uint256 _value; // default: 0\\n    }\\n\\n    function current(Counter storage counter) internal view returns (uint256) {\\n        return counter._value;\\n    }\\n\\n    function increment(Counter storage counter) internal {\\n        unchecked {\\n            counter._value += 1;\\n        }\\n    }\\n\\n    function decrement(Counter storage counter) internal {\\n        uint256 value = counter._value;\\n        require(value > 0, \\\"Counter: decrement overflow\\\");\\n        unchecked {\\n            counter._value = value - 1;\\n        }\\n    }\\n\\n    function reset(Counter storage counter) internal {\\n        counter._value = 0;\\n    }\\n}\\n\",\"keccak256\":\"0xf0018c2440fbe238dd3a8732fa8e17a0f9dce84d31451dc8a32f6d62b349c9f1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/Strings.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/Strings.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./math/Math.sol\\\";\\n\\n/**\\n * @dev String operations.\\n */\\nlibrary Strings {\\n    bytes16 private constant _SYMBOLS = \\\"0123456789abcdef\\\";\\n    uint8 private constant _ADDRESS_LENGTH = 20;\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` decimal representation.\\n     */\\n    function toString(uint256 value) internal pure returns (string memory) {\\n        unchecked {\\n            uint256 length = Math.log10(value) + 1;\\n            string memory buffer = new string(length);\\n            uint256 ptr;\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                ptr := add(buffer, add(32, length))\\n            }\\n            while (true) {\\n                ptr--;\\n                /// @solidity memory-safe-assembly\\n                assembly {\\n                    mstore8(ptr, byte(mod(value, 10), _SYMBOLS))\\n                }\\n                value /= 10;\\n                if (value == 0) break;\\n            }\\n            return buffer;\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation.\\n     */\\n    function toHexString(uint256 value) internal pure returns (string memory) {\\n        unchecked {\\n            return toHexString(value, Math.log256(value) + 1);\\n        }\\n    }\\n\\n    /**\\n     * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation with fixed length.\\n     */\\n    function toHexString(uint256 value, uint256 length) internal pure returns (string memory) {\\n        bytes memory buffer = new bytes(2 * length + 2);\\n        buffer[0] = \\\"0\\\";\\n        buffer[1] = \\\"x\\\";\\n        for (uint256 i = 2 * length + 1; i > 1; --i) {\\n            buffer[i] = _SYMBOLS[value & 0xf];\\n            value >>= 4;\\n        }\\n        require(value == 0, \\\"Strings: hex length insufficient\\\");\\n        return string(buffer);\\n    }\\n\\n    /**\\n     * @dev Converts an `address` with fixed length of 20 bytes to its not checksummed ASCII `string` hexadecimal representation.\\n     */\\n    function toHexString(address addr) internal pure returns (string memory) {\\n        return toHexString(uint256(uint160(addr)), _ADDRESS_LENGTH);\\n    }\\n}\\n\",\"keccak256\":\"0xa4d1d62251f8574deb032a35fc948386a9b4de74b812d4f545a1ac120486b48a\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/ERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/ERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC165} interface.\\n *\\n * Contracts that want to implement ERC165 should inherit from this contract and override {supportsInterface} to check\\n * for the additional interface id that will be supported. For example:\\n *\\n * ```solidity\\n * function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n *     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId);\\n * }\\n * ```\\n *\\n * Alternatively, {ERC165Storage} provides an easier to use but more expensive implementation.\\n */\\nabstract contract ERC165 is IERC165 {\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n        return interfaceId == type(IERC165).interfaceId;\\n    }\\n}\\n\",\"keccak256\":\"0xd10975de010d89fd1c78dc5e8a9a7e7f496198085c151648f20cba166b32582b\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\",\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\"},\"@openzeppelin/contracts/utils/math/Math.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/math/Math.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Standard math utilities missing in the Solidity language.\\n */\\nlibrary Math {\\n    enum Rounding {\\n        Down, // Toward negative infinity\\n        Up, // Toward infinity\\n        Zero // Toward zero\\n    }\\n\\n    /**\\n     * @dev Returns the largest of two numbers.\\n     */\\n    function max(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return a > b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the smallest of two numbers.\\n     */\\n    function min(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return a < b ? a : b;\\n    }\\n\\n    /**\\n     * @dev Returns the average of two numbers. The result is rounded towards\\n     * zero.\\n     */\\n    function average(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b) / 2 can overflow.\\n        return (a & b) + (a ^ b) / 2;\\n    }\\n\\n    /**\\n     * @dev Returns the ceiling of the division of two numbers.\\n     *\\n     * This differs from standard division with `/` in that it rounds up instead\\n     * of rounding down.\\n     */\\n    function ceilDiv(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // (a + b - 1) / b can overflow on addition, so we distribute.\\n        return a == 0 ? 0 : (a - 1) / b + 1;\\n    }\\n\\n    /**\\n     * @notice Calculates floor(x * y / denominator) with full precision. Throws if result overflows a uint256 or denominator == 0\\n     * @dev Original credit to Remco Bloemen under MIT license (https://xn--2-umb.com/21/muldiv)\\n     * with further edits by Uniswap Labs also under MIT license.\\n     */\\n    function mulDiv(\\n        uint256 x,\\n        uint256 y,\\n        uint256 denominator\\n    ) internal pure returns (uint256 result) {\\n        unchecked {\\n            // 512-bit multiply [prod1 prod0] = x * y. Compute the product mod 2^256 and mod 2^256 - 1, then use\\n            // use the Chinese Remainder Theorem to reconstruct the 512 bit result. The result is stored in two 256\\n            // variables such that product = prod1 * 2^256 + prod0.\\n            uint256 prod0; // Least significant 256 bits of the product\\n            uint256 prod1; // Most significant 256 bits of the product\\n            assembly {\\n                let mm := mulmod(x, y, not(0))\\n                prod0 := mul(x, y)\\n                prod1 := sub(sub(mm, prod0), lt(mm, prod0))\\n            }\\n\\n            // Handle non-overflow cases, 256 by 256 division.\\n            if (prod1 == 0) {\\n                return prod0 / denominator;\\n            }\\n\\n            // Make sure the result is less than 2^256. Also prevents denominator == 0.\\n            require(denominator > prod1);\\n\\n            ///////////////////////////////////////////////\\n            // 512 by 256 division.\\n            ///////////////////////////////////////////////\\n\\n            // Make division exact by subtracting the remainder from [prod1 prod0].\\n            uint256 remainder;\\n            assembly {\\n                // Compute remainder using mulmod.\\n                remainder := mulmod(x, y, denominator)\\n\\n                // Subtract 256 bit number from 512 bit number.\\n                prod1 := sub(prod1, gt(remainder, prod0))\\n                prod0 := sub(prod0, remainder)\\n            }\\n\\n            // Factor powers of two out of denominator and compute largest power of two divisor of denominator. Always >= 1.\\n            // See https://cs.stackexchange.com/q/138556/92363.\\n\\n            // Does not overflow because the denominator cannot be zero at this stage in the function.\\n            uint256 twos = denominator & (~denominator + 1);\\n            assembly {\\n                // Divide denominator by twos.\\n                denominator := div(denominator, twos)\\n\\n                // Divide [prod1 prod0] by twos.\\n                prod0 := div(prod0, twos)\\n\\n                // Flip twos such that it is 2^256 / twos. If twos is zero, then it becomes one.\\n                twos := add(div(sub(0, twos), twos), 1)\\n            }\\n\\n            // Shift in bits from prod1 into prod0.\\n            prod0 |= prod1 * twos;\\n\\n            // Invert denominator mod 2^256. Now that denominator is an odd number, it has an inverse modulo 2^256 such\\n            // that denominator * inv = 1 mod 2^256. Compute the inverse by starting with a seed that is correct for\\n            // four bits. That is, denominator * inv = 1 mod 2^4.\\n            uint256 inverse = (3 * denominator) ^ 2;\\n\\n            // Use the Newton-Raphson iteration to improve the precision. Thanks to Hensel's lifting lemma, this also works\\n            // in modular arithmetic, doubling the correct bits in each step.\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^8\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^16\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^32\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^64\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^128\\n            inverse *= 2 - denominator * inverse; // inverse mod 2^256\\n\\n            // Because the division is now exact we can divide by multiplying with the modular inverse of denominator.\\n            // This will give us the correct result modulo 2^256. Since the preconditions guarantee that the outcome is\\n            // less than 2^256, this is the final result. We don't need to compute the high bits of the result and prod1\\n            // is no longer required.\\n            result = prod0 * inverse;\\n            return result;\\n        }\\n    }\\n\\n    /**\\n     * @notice Calculates x * y / denominator with full precision, following the selected rounding direction.\\n     */\\n    function mulDiv(\\n        uint256 x,\\n        uint256 y,\\n        uint256 denominator,\\n        Rounding rounding\\n    ) internal pure returns (uint256) {\\n        uint256 result = mulDiv(x, y, denominator);\\n        if (rounding == Rounding.Up && mulmod(x, y, denominator) > 0) {\\n            result += 1;\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Returns the square root of a number. If the number is not a perfect square, the value is rounded down.\\n     *\\n     * Inspired by Henry S. Warren, Jr.'s \\\"Hacker's Delight\\\" (Chapter 11).\\n     */\\n    function sqrt(uint256 a) internal pure returns (uint256) {\\n        if (a == 0) {\\n            return 0;\\n        }\\n\\n        // For our first guess, we get the biggest power of 2 which is smaller than the square root of the target.\\n        //\\n        // We know that the \\\"msb\\\" (most significant bit) of our target number `a` is a power of 2 such that we have\\n        // `msb(a) <= a < 2*msb(a)`. This value can be written `msb(a)=2**k` with `k=log2(a)`.\\n        //\\n        // This can be rewritten `2**log2(a) <= a < 2**(log2(a) + 1)`\\n        // \\u2192 `sqrt(2**k) <= sqrt(a) < sqrt(2**(k+1))`\\n        // \\u2192 `2**(k/2) <= sqrt(a) < 2**((k+1)/2) <= 2**(k/2 + 1)`\\n        //\\n        // Consequently, `2**(log2(a) / 2)` is a good first approximation of `sqrt(a)` with at least 1 correct bit.\\n        uint256 result = 1 << (log2(a) >> 1);\\n\\n        // At this point `result` is an estimation with one bit of precision. We know the true value is a uint128,\\n        // since it is the square root of a uint256. Newton's method converges quadratically (precision doubles at\\n        // every iteration). We thus need at most 7 iteration to turn our partial result with one bit of precision\\n        // into the expected uint128 result.\\n        unchecked {\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            result = (result + a / result) >> 1;\\n            return min(result, a / result);\\n        }\\n    }\\n\\n    /**\\n     * @notice Calculates sqrt(a), following the selected rounding direction.\\n     */\\n    function sqrt(uint256 a, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = sqrt(a);\\n            return result + (rounding == Rounding.Up && result * result < a ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >> 128 > 0) {\\n                value >>= 128;\\n                result += 128;\\n            }\\n            if (value >> 64 > 0) {\\n                value >>= 64;\\n                result += 64;\\n            }\\n            if (value >> 32 > 0) {\\n                value >>= 32;\\n                result += 32;\\n            }\\n            if (value >> 16 > 0) {\\n                value >>= 16;\\n                result += 16;\\n            }\\n            if (value >> 8 > 0) {\\n                value >>= 8;\\n                result += 8;\\n            }\\n            if (value >> 4 > 0) {\\n                value >>= 4;\\n                result += 4;\\n            }\\n            if (value >> 2 > 0) {\\n                value >>= 2;\\n                result += 2;\\n            }\\n            if (value >> 1 > 0) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 2, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log2(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log2(value);\\n            return result + (rounding == Rounding.Up && 1 << result < value ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >= 10**64) {\\n                value /= 10**64;\\n                result += 64;\\n            }\\n            if (value >= 10**32) {\\n                value /= 10**32;\\n                result += 32;\\n            }\\n            if (value >= 10**16) {\\n                value /= 10**16;\\n                result += 16;\\n            }\\n            if (value >= 10**8) {\\n                value /= 10**8;\\n                result += 8;\\n            }\\n            if (value >= 10**4) {\\n                value /= 10**4;\\n                result += 4;\\n            }\\n            if (value >= 10**2) {\\n                value /= 10**2;\\n                result += 2;\\n            }\\n            if (value >= 10**1) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log10(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log10(value);\\n            return result + (rounding == Rounding.Up && 10**result < value ? 1 : 0);\\n        }\\n    }\\n\\n    /**\\n     * @dev Return the log in base 256, rounded down, of a positive value.\\n     * Returns 0 if given 0.\\n     *\\n     * Adding one to the result gives the number of pairs of hex symbols needed to represent `value` as a hex string.\\n     */\\n    function log256(uint256 value) internal pure returns (uint256) {\\n        uint256 result = 0;\\n        unchecked {\\n            if (value >> 128 > 0) {\\n                value >>= 128;\\n                result += 16;\\n            }\\n            if (value >> 64 > 0) {\\n                value >>= 64;\\n                result += 8;\\n            }\\n            if (value >> 32 > 0) {\\n                value >>= 32;\\n                result += 4;\\n            }\\n            if (value >> 16 > 0) {\\n                value >>= 16;\\n                result += 2;\\n            }\\n            if (value >> 8 > 0) {\\n                result += 1;\\n            }\\n        }\\n        return result;\\n    }\\n\\n    /**\\n     * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n     * Returns 0 if given 0.\\n     */\\n    function log256(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n        unchecked {\\n            uint256 result = log256(value);\\n            return result + (rounding == Rounding.Up && 1 << (result * 8) < value ? 1 : 0);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0xa1e8e83cd0087785df04ac79fb395d9f3684caeaf973d9e2c71caef723a3a5d6\",\"license\":\"MIT\"},\"contracts/ArraysUT.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\npragma solidity ^0.8.17;\\n\\nimport \\\"@openzeppelin/contracts/token/ERC721/ERC721.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/Counters.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\nimport \\\"./Cafe.sol\\\";\\nimport \\\"./Assert.sol\\\";\\n\\nusing List for List.ARList;\\n\\ninterface ISubmission {\\n    function getNumbers() external view returns (uint[] memory);\\n\\n    function resetNumbers() external;\\n\\n    function appendToNumbers(uint[] calldata _toAppend) external;\\n\\n    function saveTimestamp(uint _unixTimestamp) external;\\n\\n    function afterY2K() external view returns (uint[] memory, address[] memory);\\n\\n    function resetSenders() external;\\n\\n    function resetTimestamps() external;\\n}\\n\\nlibrary Caller {\\n    function getNumbers(\\n        ISubmission _submission\\n    ) internal view returns (uint[] memory, bool) {\\n        try _submission.getNumbers() returns (uint[] memory result) {\\n            return (result, false);\\n        } catch {\\n            uint[] memory badResult;\\n            return (badResult, true);\\n        }\\n    }\\n\\n    function appendToNumbers(\\n        ISubmission _submission,\\n        uint[] memory _toAppend\\n    ) internal returns (bool) {\\n        try _submission.appendToNumbers(_toAppend) {\\n            return (false);\\n        } catch {\\n            return (true);\\n        }\\n    }\\n\\n    function saveTimestamp(\\n        ISubmission _submission,\\n        uint _unixTimestamp\\n    ) internal returns (bool) {\\n        try _submission.saveTimestamp(_unixTimestamp) {\\n            return (false);\\n        } catch {\\n            return (true);\\n        }\\n    }\\n\\n    function afterY2K(\\n        ISubmission _submission\\n    ) internal view returns (uint[] memory, address[] memory, bool) {\\n        try _submission.afterY2K() returns (\\n            uint[] memory timestamps,\\n            address[] memory addresses\\n        ) {\\n            return (timestamps, addresses, false);\\n        } catch {\\n            uint[] memory badResultUint;\\n            address[] memory badResAddr;\\n            return (badResultUint, badResAddr, true);\\n        }\\n    }\\n}\\n\\ninterface ISecondCaller {\\n    function secondSaveTimestamp(\\n        ISubmission _submission,\\n        uint _timestamp\\n    ) external returns (bool);\\n}\\n\\n// Create a second contract to have a second address to test afterY2K\\ncontract SecondCaller {\\n    function secondSaveTimestamp(\\n        ISubmission _submission,\\n        uint _timestamp\\n    ) public returns (bool) {\\n        return Caller.saveTimestamp(_submission, _timestamp);\\n    }\\n}\\n\\ncontract TestAppendToNumbers is ITest, Assert {\\n    function execute(\\n        address _submissionAddress\\n    ) external override returns (Cafe.TestResult memory) {\\n        ISubmission submission = ISubmission(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult.message = \\\"Should reset numbers and append provided array\\\";\\n\\n        submission.resetNumbers();\\n\\n        uint[4] memory newNumbers = [uint(11), 12, 13, 14];\\n        uint[] memory toAppend = new uint[](4);\\n        for (uint i = 0; i < toAppend.length; i++) {\\n            toAppend[i] = newNumbers[i];\\n        }\\n\\n        bool callError = Caller.appendToNumbers(submission, toAppend);\\n        testResult.assertResults.push(Assert.isFalse(callError));\\n\\n        uint[] memory result;\\n        (result, callError) = Caller.getNumbers(submission);\\n\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to getNumbers failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            uint[14] memory expectedNumbers = [\\n                uint(1),\\n                2,\\n                3,\\n                4,\\n                5,\\n                6,\\n                7,\\n                8,\\n                9,\\n                10,\\n                11,\\n                12,\\n                13,\\n                14\\n            ];\\n            uint[] memory expected = new uint[](14);\\n            for (uint i = 0; i < expected.length; i++) {\\n                expected[i] = expectedNumbers[i];\\n            }\\n            testResult.assertResults.push(Assert.equal(result, expected));\\n        }\\n        return testResult;\\n    }\\n}\\n\\ncontract TestAfterY2K is ITest, Assert {\\n    ISecondCaller secondCaller;\\n\\n    constructor(address _secondCallerAddress) {\\n        secondCaller = ISecondCaller(_secondCallerAddress);\\n    }\\n\\n    function execute(\\n        address _submissionAddress\\n    ) external override returns (Cafe.TestResult memory) {\\n        ISubmission submission = ISubmission(_submissionAddress);\\n        Cafe.TestResult memory testResult;\\n        testResult.assertResults.create();\\n        testResult\\n            .message = \\\"afterY2K should return timestamps and addresses > Y2k\\\";\\n\\n        submission.resetSenders();\\n        submission.resetTimestamps();\\n\\n        bool callError = Caller.saveTimestamp(submission, 946702600);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to saveTimestamp failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        }\\n\\n        callError = Caller.saveTimestamp(submission, 946702700);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to saveTimestamp failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        }\\n\\n        callError = Caller.saveTimestamp(submission, 946702900);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to saveTimestamp failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        }\\n\\n        // Use secondCaller to get different msg.sender\\n        callError = secondCaller.secondSaveTimestamp(submission, 946702650);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to secondSaveTimestamp failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        }\\n\\n        callError = secondCaller.secondSaveTimestamp(submission, 946702950);\\n        if (callError) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to secondSaveTimestamp failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        }\\n\\n        (\\n            uint[] memory timestamps,\\n            address[] memory addresses,\\n            bool err\\n        ) = Caller.afterY2K(submission);\\n\\n        if (err) {\\n            testResult.assertResults.push(\\n                Assert.AssertResult(\\n                    false,\\n                    \\\"Call to afterY2K failed\\\",\\n                    \\\"\\\",\\n                    \\\"\\\",\\n                    \\\"\\\"\\n                )\\n            );\\n        } else {\\n            uint[] memory expectedTimestamps = new uint[](2);\\n            expectedTimestamps[0] = 946702900;\\n            expectedTimestamps[1] = 946702950;\\n\\n            address[] memory expectedAddresses = new address[](2);\\n            expectedAddresses[0] = address(this);\\n            expectedAddresses[1] = address(secondCaller);\\n\\n            testResult.assertResults.push(\\n                Assert.equal(timestamps, expectedTimestamps)\\n            );\\n            testResult.assertResults.push(\\n                Assert.equal(addresses, expectedAddresses)\\n            );\\n        }\\n\\n        return testResult;\\n    }\\n}\\n\\ncontract ArraysUT is Cafe {\\n    constructor(address _secondCallerAddress) ERC721(\\\"Arrays Pin\\\", \\\"SCDA\\\") {\\n        tests.push(new TestAppendToNumbers());\\n        tests.push(new TestAfterY2K(_secondCallerAddress));\\n    }\\n}\\n\",\"keccak256\":\"0x5d79c3e8798292bca3a86bf94a08a8d2eee473f9ee32ecffc6053e5b29ec4c2f\",\"license\":\"UNLICENSED\"},\"contracts/Assert.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\n\\npragma solidity >=0.8.17 <0.9.0;\\n\\nimport \\\"@openzeppelin/contracts/utils/Strings.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\n/**\\n * Dynamic memory array implementation for AssertResult to facilitate unit test\\n * implementation.\\n */\\nlibrary List {\\n    struct ARList {\\n        Assert.AssertResult[] elements;\\n        uint num;\\n    }\\n\\n    function create(\\n        ARList memory _aList\\n    ) internal pure returns (ARList memory newList) {\\n        _aList.elements = new Assert.AssertResult[](4);\\n        _aList.num = 0;\\n        return _aList;\\n    }\\n\\n    function _resizeUp(\\n        ARList memory _aList\\n    ) internal pure returns (ARList memory) {\\n        ARList memory newList;\\n        newList.elements = new Assert.AssertResult[](\\n            (_aList.elements.length * 3) / 2\\n        );\\n        for (uint i = 0; i < _aList.elements.length; i++) {\\n            newList.elements[i] = _aList.elements[i];\\n        }\\n        newList.num = _aList.num;\\n        return newList;\\n    }\\n\\n    // Follow Solidity .pop() expectation to NOT return a value\\n    function pop(ARList memory _aList) internal pure returns (ARList memory) {\\n        _aList.num--;\\n        delete _aList.elements[_aList.num];\\n        return _aList;\\n    }\\n\\n    function push(\\n        ARList memory _aList,\\n        Assert.AssertResult memory _result\\n    ) internal pure returns (ARList memory) {\\n        if (_aList.num == _aList.elements.length) {\\n            _aList = _resizeUp(_aList);\\n        }\\n        _aList.elements[_aList.num] = _result;\\n        _aList.num++;\\n        return _aList;\\n    }\\n}\\n\\ncontract Assert {\\n    struct AssertResult {\\n        bool passed;\\n        string assertionError;\\n        string returnedAsString;\\n        string expectedAsString;\\n        string methodName;\\n    }\\n\\n    function _arrToString(\\n        uint[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(result, Strings.toString(arr[i]), \\\", \\\");\\n            } else {\\n                result = string.concat(result, Strings.toString(arr[i]), \\\"]\\\");\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function _arrToString(\\n        address[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(\\n                    result,\\n                    Strings.toHexString(uint160(arr[i]), 20),\\n                    \\\", \\\"\\n                );\\n            } else {\\n                result = string.concat(\\n                    result,\\n                    Strings.toHexString(uint160(arr[i]), 20),\\n                    \\\"]\\\"\\n                );\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function _arrToString(\\n        string[] memory arr\\n    ) internal pure returns (string memory) {\\n        string memory result = \\\"[\\\";\\n\\n        for (uint i = 0; i < arr.length; i++) {\\n            if (i < arr.length - 1) {\\n                result = string.concat(result, arr[i], \\\", \\\");\\n            } else {\\n                result = string.concat(result, arr[i], \\\"]\\\");\\n            }\\n        }\\n\\n        return result;\\n    }\\n\\n    function isTrue(bool a) public pure returns (AssertResult memory result) {\\n        result.passed = a;\\n        if (!result.passed) {\\n            result.assertionError = \\\"AssertionError: result is not true\\\";\\n        }\\n        result.returnedAsString = a == true ? \\\"true\\\" : \\\"false\\\";\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"isTrue\\\";\\n    }\\n\\n    function isFalse(bool a) public pure returns (AssertResult memory result) {\\n        result.passed = !a;\\n        if (!result.passed) {\\n            result.assertionError = \\\"AssertionError: result is not false\\\";\\n        }\\n        result.returnedAsString = a == true ? \\\"true\\\" : \\\"false\\\";\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"isFalse\\\";\\n    }\\n\\n    function equal(\\n        uint256 a,\\n        uint256 b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a == b);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                Strings.toString(a),\\n                \\\" is not equal to \\\",\\n                Strings.toString(b)\\n            );\\n        }\\n        result.returnedAsString = Strings.toString(a);\\n        result.expectedAsString = Strings.toString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // TODO\\n\\n    // function equal(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function equal(bool a, bool b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function equal(\\n        address a,\\n        address b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a == b);\\n        string memory aString = Strings.toHexString(uint160(a), 20);\\n        string memory bString = Strings.toHexString(uint160(b), 20);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                aString,\\n                \\\" is not equal to \\\",\\n                bString\\n            );\\n        }\\n        result.returnedAsString = aString;\\n        result.expectedAsString = bString;\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function equal(bytes32 a, bytes32 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function equal(\\n        bytes memory a,\\n        bytes memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(a) == keccak256(b);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                string(a),\\n                \\\" is not equal to \\\",\\n                string(b)\\n            );\\n        }\\n        result.returnedAsString = string(a);\\n        result.expectedAsString = string(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        string memory a,\\n        string memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(bytes(a)) == keccak256(bytes(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                a,\\n                \\\" is not equal to \\\",\\n                b\\n            );\\n        }\\n        result.returnedAsString = a;\\n        result.expectedAsString = b;\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        uint[] memory a,\\n        uint[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        address[] memory a,\\n        address[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    function equal(\\n        string[] memory a,\\n        string[] memory b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = keccak256(abi.encode(a)) == keccak256(abi.encode(b));\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                _arrToString(a),\\n                \\\" is not equal to \\\",\\n                _arrToString(b)\\n            );\\n        }\\n        result.returnedAsString = _arrToString(a);\\n        result.expectedAsString = _arrToString(b);\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function notEqual(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(bool a, bool b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    function notEqual(\\n        address a,\\n        address b\\n    ) public pure returns (AssertResult memory result) {\\n        result.passed = (a != b);\\n        string memory aString = Strings.toHexString(uint160(a), 20);\\n        if (!result.passed) {\\n            result.assertionError = string.concat(\\n                \\\"AssertionError: \\\",\\n                \\\" Both values are\\\",\\n                aString\\n            );\\n        }\\n        result.returnedAsString = aString;\\n        result.expectedAsString = \\\"\\\";\\n        result.methodName = \\\"equal\\\";\\n    }\\n\\n    // function notEqual(bytes32 a, bytes32 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function notEqual(string memory a, string memory b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // /*----------------- Greater than --------------------*/\\n    // function greaterThan(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function greaterThan(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function greaterThan(uint256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    //   }\\n    // }\\n    // function greaterThan(int256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // /*----------------- Less than --------------------*/\\n    // function lessThan(uint256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function lessThan(int256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n\\n    // function lessThan(uint256 a, int256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n    // function lessThan(int256 a, uint256 b) public returns (AssertResult memory result) {\\n\\n    // }\\n}\\n\",\"keccak256\":\"0xbc7342fffc4408c65f4ed955bfd992a6dc231c9df74cd8a78ba46af2e5bbbdb5\",\"license\":\"UNLICENSED\"},\"contracts/Cafe.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\n// Cafe Version 0.2\\n\\n/**\\n * Cafe is a unit test framework designed to facilitate testing of contracts\\n * built against a defined specification.  It awards an NFT pin if the tests\\n * are passed.\\n *\\n * It works with Assert.sol to create a reasonably familiar system for writing\\n * tests.\\n */\\n\\npragma solidity ^0.8.17;\\n\\nimport \\\"@openzeppelin/contracts/token/ERC721/ERC721.sol\\\";\\nimport \\\"@openzeppelin/contracts/security/ReentrancyGuard.sol\\\";\\nimport \\\"@openzeppelin/contracts/utils/Counters.sol\\\";\\nimport \\\"@openzeppelin/contracts/access/Ownable.sol\\\";\\n\\nimport \\\"hardhat/console.sol\\\";\\n\\nimport \\\"./Assert.sol\\\";\\n\\ninterface ITest {\\n    function execute(\\n        address _submissionAddress\\n    ) external returns (Cafe.TestResult memory);\\n}\\n\\ninterface IDeploy {\\n    function deploy(\\n        address _submissionAddress\\n    ) external returns (Cafe.TestResult memory, address);\\n}\\n\\nabstract contract Cafe is ERC721, Ownable, ReentrancyGuard, Assert {\\n    error SoulboundToken();\\n    error NotActive();\\n\\n    using Counters for Counters.Counter;\\n    Counters.Counter private tokenIds;\\n\\n    mapping(address => bool) public owners;\\n    mapping(address => bool) submittedContracts;\\n\\n    bool active = true;\\n    string AllTokenURI = \\\"\\\";\\n\\n    event TestSuiteResult(\\n        address submission,\\n        bool passed,\\n        TestResult[] testResults\\n    );\\n\\n    struct TestResult {\\n        string message;\\n        List.ARList assertResults;\\n    }\\n\\n    ITest[] tests;\\n\\n    /**\\n     * Used by unit tests to indicate they contain a factory\\n     * Returns the **address** of the contract deployed by the factory\\n     * and the test result, to validate deployment or alert to failure\\n     *\\n     */\\n\\n    IDeploy deployer;\\n\\n    function testContract(address _submissionAddress) public testIsActive {\\n        // Declare here to +1 length if there is a deployer\\n        TestResult[] memory testResults;\\n        uint i = 0;\\n        address testAddress = _submissionAddress;\\n\\n        if (address(deployer) != address(0)) {\\n            testResults = new TestResult[](tests.length + 1);\\n            i++;\\n            // Use the address returned by the deployment for remaining tests\\n            (testResults[0], testAddress) = deployer.deploy(_submissionAddress);\\n        } else {\\n            testResults = new TestResult[](tests.length);\\n        }\\n\\n        for (i; i < tests.length; i++) {\\n            testResults[i] = tests[i].execute(testAddress);\\n        }\\n\\n        processResults(_submissionAddress, testResults);\\n    }\\n\\n    /**\\n     * Check each assert in each test to see if any failed.\\n     *\\n     * Note:  The check is here instead of setting a `passed` bool in\\n     * `TestResult` to reduce the amount of code in each unit test.\\n     */\\n    function checkIfAllPassed(\\n        TestResult[] memory _testResults\\n    ) public pure returns (bool) {\\n        for (uint i = 0; i < _testResults.length; i++) {\\n            for (uint k = 0; k < _testResults[i].assertResults.num; k++) {\\n                if (!_testResults[i].assertResults.elements[k].passed) {\\n                    return false;\\n                }\\n            }\\n        }\\n        return true;\\n    }\\n\\n    function processResults(\\n        address _submissionAddress,\\n        TestResult[] memory _testResults\\n    ) internal nonReentrant {\\n        bool passed = checkIfAllPassed(_testResults);\\n\\n        emit TestSuiteResult(_submissionAddress, passed, _testResults);\\n\\n        /**\\n         * Grant a soulbound NFT pin if:\\n         *  - This contract address has not been submitted before\\n         *  - The sender does not already own one of these pins\\n         *  - The contract submitted passes all unit tests\\n         */\\n        if (\\n            !submittedContracts[_submissionAddress] &&\\n            !owners[msg.sender] &&\\n            passed\\n        ) {\\n            tokenIds.increment();\\n            uint newId = tokenIds.current();\\n            owners[msg.sender] = true;\\n            _safeMint(msg.sender, newId);\\n        }\\n\\n        submittedContracts[_submissionAddress] = true;\\n    }\\n\\n    /**\\n     * Disallow transfers (Soulbound NFT)\\n     */\\n    function _beforeTokenTransfer(\\n        address _from,\\n        address,\\n        uint,\\n        uint\\n    ) internal pure override {\\n        if (_from != address(0)) {\\n            revert SoulboundToken();\\n        }\\n    }\\n\\n    function setActive(bool _setActiveTo) public onlyOwner {\\n        active = _setActiveTo;\\n    }\\n\\n    function setTokenURI(string memory _tokenURI) public onlyOwner {\\n        AllTokenURI = _tokenURI;\\n    }\\n\\n    function tokenURI(uint256) public view override returns (string memory) {\\n        return AllTokenURI;\\n    }\\n\\n    modifier testIsActive() {\\n        if (!active) {\\n            revert NotActive();\\n        }\\n        _;\\n    }\\n}\\n\",\"keccak256\":\"0xe350e3229cbd709b8c8070980eaa36e1a29bf7bc3f2e3e5d6c7f9940ec376144\",\"license\":\"UNLICENSED\"},\"hardhat/console.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity >= 0.4.22 <0.9.0;\\n\\nlibrary console {\\n\\taddress constant CONSOLE_ADDRESS = address(0x000000000000000000636F6e736F6c652e6c6f67);\\n\\n\\tfunction _sendLogPayload(bytes memory payload) private view {\\n\\t\\tuint256 payloadLength = payload.length;\\n\\t\\taddress consoleAddress = CONSOLE_ADDRESS;\\n\\t\\tassembly {\\n\\t\\t\\tlet payloadStart := add(payload, 32)\\n\\t\\t\\tlet r := staticcall(gas(), consoleAddress, payloadStart, payloadLength, 0, 0)\\n\\t\\t}\\n\\t}\\n\\n\\tfunction log() internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log()\\\"));\\n\\t}\\n\\n\\tfunction logInt(int256 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(int256)\\\", p0));\\n\\t}\\n\\n\\tfunction logUint(uint256 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256)\\\", p0));\\n\\t}\\n\\n\\tfunction logString(string memory p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string)\\\", p0));\\n\\t}\\n\\n\\tfunction logBool(bool p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool)\\\", p0));\\n\\t}\\n\\n\\tfunction logAddress(address p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes(bytes memory p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes1(bytes1 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes1)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes2(bytes2 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes2)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes3(bytes3 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes3)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes4(bytes4 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes4)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes5(bytes5 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes5)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes6(bytes6 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes6)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes7(bytes7 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes7)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes8(bytes8 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes8)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes9(bytes9 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes9)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes10(bytes10 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes10)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes11(bytes11 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes11)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes12(bytes12 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes12)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes13(bytes13 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes13)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes14(bytes14 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes14)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes15(bytes15 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes15)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes16(bytes16 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes16)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes17(bytes17 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes17)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes18(bytes18 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes18)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes19(bytes19 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes19)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes20(bytes20 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes20)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes21(bytes21 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes21)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes22(bytes22 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes22)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes23(bytes23 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes23)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes24(bytes24 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes24)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes25(bytes25 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes25)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes26(bytes26 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes26)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes27(bytes27 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes27)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes28(bytes28 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes28)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes29(bytes29 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes29)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes30(bytes30 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes30)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes31(bytes31 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes31)\\\", p0));\\n\\t}\\n\\n\\tfunction logBytes32(bytes32 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bytes32)\\\", p0));\\n\\t}\\n\\n\\tfunction log(uint256 p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256)\\\", p0));\\n\\t}\\n\\n\\tfunction log(string memory p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string)\\\", p0));\\n\\t}\\n\\n\\tfunction log(bool p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool)\\\", p0));\\n\\t}\\n\\n\\tfunction log(address p0) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address)\\\", p0));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(address p0, address p1) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address)\\\", p0, p1));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address)\\\", p0, p1, p2));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(uint256 p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(uint256,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(string memory p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(string,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(bool p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(bool,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, uint256 p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,uint256,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, string memory p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,string,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, bool p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,bool,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, uint256 p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,uint256,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, string memory p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,string,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, bool p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,bool,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, uint256 p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,uint256)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, string memory p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,string)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, bool p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,bool)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n\\tfunction log(address p0, address p1, address p2, address p3) internal view {\\n\\t\\t_sendLogPayload(abi.encodeWithSignature(\\\"log(address,address,address,address)\\\", p0, p1, p2, p3));\\n\\t}\\n\\n}\\n\",\"keccak256\":\"0x60b0215121bf25612a6739fb2f1ec35f31ee82e4a8216c032c8243d904ab3aa9\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"kind": "dev", "methods": {"approve(address,uint256)": {"details": "See {IERC721-approve}."}, "balanceOf(address)": {"details": "See {IERC721-balanceOf}."}, "getApproved(uint256)": {"details": "See {IERC721-getApproved}."}, "isApprovedForAll(address,address)": {"details": "See {IERC721-isApprovedForAll}."}, "name()": {"details": "See {IERC721Metadata-name}."}, "owner()": {"details": "Returns the address of the current owner."}, "ownerOf(uint256)": {"details": "See {IERC721-ownerOf}."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions anymore. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby removing any functionality that is only available to the owner."}, "safeTransferFrom(address,address,uint256)": {"details": "See {IERC721-safeTransferFrom}."}, "safeTransferFrom(address,address,uint256,bytes)": {"details": "See {IERC721-safeTransferFrom}."}, "setApprovalForAll(address,bool)": {"details": "See {IERC721-setApprovalForAll}."}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}, "symbol()": {"details": "See {IERC721Metadata-symbol}."}, "transferFrom(address,address,uint256)": {"details": "See {IERC721-transferFrom}."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"checkIfAllPassed((string,((bool,string,string,string,string)[],uint256))[])": {"notice": "Check each assert in each test to see if any failed. Note:  The check is here instead of setting a `passed` bool in `TestResult` to reduce the amount of code in each unit test."}}, "version": 1}, "storageLayout": {"storage": [{"astId": 882, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_name", "offset": 0, "slot": "0", "type": "t_string_storage"}, {"astId": 884, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_symbol", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 888, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_owners", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 892, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_balances", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 896, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_tokenApprovals", "offset": 0, "slot": "4", "type": "t_mapping(t_uint256,t_address)"}, {"astId": 902, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_operatorApprovals", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_address,t_bool))"}, {"astId": 7, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_owner", "offset": 0, "slot": "6", "type": "t_address"}, {"astId": 123, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_status", "offset": 0, "slot": "7", "type": "t_uint256"}, {"astId": 6525, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "tokenIds", "offset": 0, "slot": "8", "type": "t_struct(Counter)2320_storage"}, {"astId": 6529, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "owners", "offset": 0, "slot": "9", "type": "t_mapping(t_address,t_bool)"}, {"astId": 6533, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "submittedContracts", "offset": 0, "slot": "10", "type": "t_mapping(t_address,t_bool)"}, {"astId": 6536, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "active", "offset": 0, "slot": "11", "type": "t_bool"}, {"astId": 6539, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "AllTokenURI", "offset": 0, "slot": "12", "type": "t_string_storage"}, {"astId": 6559, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "tests", "offset": 0, "slot": "13", "type": "t_array(t_contract(ITest)6495)dyn_storage"}, {"astId": 6563, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "deployer", "offset": 0, "slot": "14", "type": "t_contract(IDeploy)6506"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_contract(ITest)6495)dyn_storage": {"base": "t_contract(ITest)6495", "encoding": "dynamic_array", "label": "contract ITest[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_contract(IDeploy)6506": {"encoding": "inplace", "label": "contract IDeploy", "numberOfBytes": "20"}, "t_contract(ITest)6495": {"encoding": "inplace", "label": "contract ITest", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_address,t_mapping(t_address,t_bool))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => bool))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_bool)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_mapping(t_uint256,t_address)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => address)", "numberOfBytes": "32", "value": "t_address"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Counter)2320_storage": {"encoding": "inplace", "label": "struct Counters.Counter", "members": [{"astId": 2319, "contract": "contracts/ArraysUT.sol:ArraysUT", "label": "_value", "offset": 0, "slot": "0", "type": "t_uint256"}], "numberOfBytes": "32"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}