{"name": "storybook", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@coinbase/onchainkit": "^0.38.13", "@coinbase/wallet-sdk-canary": "npm:@coinbase/wallet-sdk@4.4.0-canary.1", "@tanstack/react-query": "^5.76.1", "classnames": "^2.5.1", "lucide-react": "^0.516.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.0", "viem": "^2.30.0", "wagmi": "^2.15.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/addon-themes": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "^8.6.14", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "chromatic": "^11.28.2", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-plugin-storybook": "^0.12.0", "storybook": "^8.6.14", "storybook-dark-mode": "^4.0.2", "tailwindcss": "^4", "typescript": "^5"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}